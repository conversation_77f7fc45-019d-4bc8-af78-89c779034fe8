// main_Kernels.cuh - Updated header
#pragma once

#include <cuda.h>
#include "HeadDetector.h"

// Function declarations for CUDA kernel launchers
void launchDetectUncertainRegions(float* alphaBuffer,
                                 unsigned char* alphaRegionsBuffer,
                                 int width, int height, int regionSize,
                                 const Box* headBoxes, int headCount,
                                 CUstream stream);

// New function declarations for IndexNet processing
void launchPrepareIndexNetInput(float* outputBuffer, const float* inputRgbBuffer, const float* inputAlphaBuffer, int x, int y, int width, int height, int alphaRegionSize, int alphaRegionSizeY, int padding, CUstream stream);

void launchUpdateAlphaBufferRegion(float* outputAlphaBuffer, CUtexObject originalAlphaTexture, const float* trimapBuffer, const float* improvedAlpha, int x, int y, int width, int height, int alphaRegionSize, int padding, CUstream stream);

// Additional kernel functions for multi-pass processing
void launchExtractRegion(const float* inputBuffer, float* outputBuffer, int inputWidth, int inputHeight,
                        int regionX, int regionY, int regionWidth, int regionHeight, int channels, CUstream stream);
void launchCombineAlphaRegion(float* mainAlpha, const float* regionAlpha, int mainWidth, int mainHeight,
                           int regionX, int regionY, int regionWidth, int regionHeight, CUstream stream);

// Resize alpha region with bilinear interpolation
void launchResizeAlpha(const float* inputAlpha, float* outputAlpha, 
                       int inputWidth, int inputHeight,
                       int outputWidth, int outputHeight, CUstream stream);

// New trimap generation functions
void launchGenerateTrimap(float* outputTrimap, const float* inputAlpha, int width, int height, CUstream stream);
