#include "VideoBackgroundRemover.h"
#include "FFmpegIO/CodecConfig.h"
#include "FFmpegIO/DirectVideoReader.h"
#include "FFmpegIO/DirectVideoWriter.h"
#include "ImageMattingFactory.h"
#include "Helpers.h"
#include "ProcessBodyRegions.h"
#include "HeadDetector.h"
#include "RAII.h"
#include <cuda.h>
#include <cuda_runtime.h>
#include <string>
#include <memory>
#include <atomic>
#include <functional>
#include <chrono>
#include <iostream>
#include <iomanip>
#include <vector>
#include <set>
#include <algorithm>
#include <cmath>
#include <main_Kernels.cuh>
#include "BackgroundEstimationKernels.cuh"
#include <filesystem>
#include "KernelManager.h"

// HeadInfo structure is now defined in the header file

//=============================================================================
// VideoBackgroundRemover Class Implementation
//=============================================================================

VideoBackgroundRemover::VideoBackgroundRemover()
	: m_progressCb(nullptr), m_userData(nullptr), m_context(nullptr), m_processingStream(nullptr), m_videoWidth(0), m_videoHeight(0), m_totalFrames(0), m_totalProgressSteps(6), m_engine(EngineType::NONE) {
}

VideoBackgroundRemover::~VideoBackgroundRemover() {
	Cleanup();
}

int VideoBackgroundRemover::Process(
	const std::wstring& inputPath,
	const std::wstring& outputPath,
	EngineType engine,
	ProgressCallback progressCb,
	void* userData) {
	try {
		// Store parameters
		m_inputPath = inputPath;
		m_outputPath = outputPath;
		m_engine = engine;
		m_progressCb = progressCb;
		m_userData = userData;

		// Initialize processing
		int result = InitializeProcessing(inputPath);
		if (result != 0)
			return result;

		// Execute processing passes
		result = ProcessPass1_InitialAlpha();
		if (result != 0)
			return result;

		result = ProcessPass2_HeadDetection();
		if (result != 0)
			return result;

		result = ProcessPass3_RefineHeadRegions();
		if (result != 0)
			return result;

		result = ProcessPass4_RefineBodyRegions();
		if (result != 0)
			return result;

		result = ProcessPass5_ReplaceHeadAlphaWithRefined();
		if (result != 0)
			return result;

	result = ProcessPass6_EstimateBackground();
	if (result != 0)
		return result;

	// Rename final files to output path with "rgb" and "alpha" suffixes
	result = RenameFinalFilesAndCleanup();
	if (result != 0)
		return result;

	std::cout << "Multi-pass processing completed successfully" << std::endl;
	return 0;
	}
	catch (const std::exception& e) {
		std::cerr << "Error in VideoBackgroundRemover::Process: " << e.what() << std::endl;
		return 1;
	}
}

int VideoBackgroundRemover::InitializeProcessing(const std::wstring& inputPath) {
	try {
		// Initialize CUDA Driver API
		DRIVER_API_CHECK(cuInit(0));

		// Initialize CUDA context
		CUcontext cudaContext;
		DRIVER_API_CHECK(cuCtxCreate(&cudaContext, 0, 0));

		// Set the context for this thread
		m_context = cudaContext;
		DRIVER_API_CHECK(cuCtxSetCurrent(cudaContext));

		// Initialize kernel manager for CUDA kernels
		if (!KernelHelpers::InitializeKernels()) {
			std::cerr << "Failed to initialize CUDA kernels" << std::endl;
			DRIVER_API_CHECK(cuCtxDestroy(cudaContext)); // Cleanup context on failure
			return 1;
		}
		std::cout << "CUDA kernels initialized successfully" << std::endl;

		// Prepare the models
		ImageMattingFactory::PrepareModels();

		// Create direct video reader
		std::string inputPathStr = Helpers::WStringToString(inputPath);
		m_reader = DirectVideoReader::Create(inputPathStr, m_context);
		if (!m_reader) {
			std::wcerr << L"Failed to create video reader for: " << inputPath << std::endl;
			DRIVER_API_CHECK(cuCtxDestroy(cudaContext)); // Cleanup context on failure
			return 1;
		}

		m_videoWidth = m_reader->GetWidth();
		m_videoHeight = m_reader->GetHeight();
		// Estimate total frames from duration and frame rate
		double durationSec = m_reader->GetDuration();
		double fps = m_reader->GetFrameRateDouble();
		m_totalFrames = static_cast<int>(durationSec * fps + 0.5);

		std::cout << "Video dimensions: " << m_videoWidth << "x" << m_videoHeight << std::endl;
		std::cout << "Total frames: " << m_totalFrames << std::endl;

		// Create CUDA stream for processing
		DRIVER_API_CHECK(cuStreamCreate(&m_processingStream, CU_STREAM_DEFAULT));

		// Note: Context and stream are destroyed in Cleanup()
		return 0;
	}
	catch (const std::exception& e) {
		std::cerr << "Error in InitializeProcessing: " << e.what() << std::endl;
		return 1;
	}
}
int VideoBackgroundRemover::ProcessPass1_InitialAlpha() {
	try {
		std::cout << "\n=== Pass 1: Initial Alpha Generation ===" << std::endl;

		// Declare frameIndex outside scoped block for later use
		int frameIndex = 0;

		// Use scoped block to ensure model cleanup
		{
			// Initialize initial image matting model using InsPyReNet
			auto m_initialImageMatter = ImageMattingFactory::Init(
				ModelType::RMBG2_0,
				m_videoWidth,
				m_videoHeight,
				m_processingStream,
				InferenceBackend::AUTO,
				ImageMattingFactory::BestModelSelectionMethod::ASPECT_RATIO_CLOSEST_FIT,
				false);

			if (!m_initialImageMatter) {
				std::cerr << "Failed to initialize InsPyReNet model for initial alpha matting" << std::endl;
				return false;
			}

			// Create hardware-accelerated alpha writer using CodecConfig
			CodecConfig alphaCfg;
			alphaCfg.width = m_videoWidth;
			alphaCfg.height = m_videoHeight;
			alphaCfg.frameRate = m_reader->GetFrameRateDouble();
			alphaCfg.codecType = CodecConfig::CodecType::HEVC_NVENC;
			alphaCfg.preset = CodecConfig::Preset::SLOW; // p6
			alphaCfg.encodeAlpha = true;
			std::string alphaPath = "initial_alpha_hw.mp4";
			auto alphaWriter = DirectVideoWriter::Create(alphaPath, alphaCfg, m_context);
			if (!alphaWriter) {
				std::cerr << "Failed to create hardware alpha writer" << std::endl;
				return 1;
			}
			std::cout << "Using hardware-accelerated H.265 alpha encoding (Y carries alpha)" << std::endl;

			// Process all frames: read directly into model input buffer
			size_t rgbSize = static_cast<size_t>(m_videoWidth) * m_videoHeight * 3 * sizeof(float);
			while (true) {
				double timestamp = m_reader->ReadFrame((CUdeviceptr)m_initialImageMatter->GetInputBuffer(), rgbSize, m_processingStream);
				if (timestamp < 0.0)
					break;

				// Run inference on the frame that was just read
				if (!m_initialImageMatter->Infer()) {
					std::cerr << "Failed to run initial alpha matting inference" << std::endl;
					return 1;
				}

				// Synchronize after inference to ensure it completes before writing
				// This ensures the output buffer is ready
				DRIVER_API_CHECK(cuStreamSynchronize(m_processingStream));

				// SaveAlphaMatteToPNG("Videos\\input_alpha1.png", m_initialImageMatter->GetOutputBuffer(), m_videoWidth, m_videoHeight);

				// Write alpha (grayscale float) frame
				alphaWriter->WriteGrayFrame((const float*)m_initialImageMatter->GetOutputBuffer(), m_processingStream);
				frameIndex++;

				if (!UpdateProgress(frameIndex, m_totalFrames * m_totalProgressSteps)) {
					std::cout << "Processing cancelled by user" << std::endl;
					return 1;
				}
			}

			// Finalize alpha encoding
			alphaWriter->Finalize();
			std::cout << "Alpha encoding completed successfully!" << std::endl;

			// Explicitly reset the model to force immediate cleanup
			m_initialImageMatter.reset();
			std::cout << "Initial alpha model explicitly destroyed" << std::endl;

			// Force CUDA context synchronization to ensure cleanup is complete
			DRIVER_API_CHECK(cuCtxSynchronize());

		} // End of scoped block - model and writer destructors called here

		// Force CUDA memory cleanup before Pass 2
		DRIVER_API_CHECK(cuCtxSynchronize());
		cudaDeviceSynchronize();

		// Clean up initial alpha step to free memory
		// Reset reader for next pass - using safer approach to avoid deadlock
		if (!SafeResetReader()) {
			std::cerr << "Failed to reset video reader" << std::endl;
			return 1;
		}

		std::cout << "Pass 1 completed: " << frameIndex << " frames processed" << std::endl;
		return 0;
	}
	catch (const std::exception& e) {
		std::cerr << "Error in ProcessPass1_InitialAlpha: " << e.what() << std::endl;
		return 1;
	}
}
int VideoBackgroundRemover::ProcessPass2_HeadDetection() {
	try {
		std::cout << "\n=== Pass 2: Head Detection ===" << std::endl;


		// Initialize head detector
		auto headDetector = std::make_unique<HeadDetector>();
		if (!headDetector->Initialize(m_videoWidth, m_videoHeight, m_processingStream)) {
			std::cerr << "Failed to initialize head detector" << std::endl;
			return false;
		}

		// Prepare RAM storage for head detections
		std::vector<std::vector<Box>> allHeadDetections(m_totalFrames);

		// Allocate reusable frame buffer on device for detection using RAII
		size_t frameBufSize = static_cast<size_t>(m_videoWidth) * m_videoHeight * 3 * sizeof(float);
		CudaMemoryRAII d_frameBuf(frameBufSize);

		// Process all frames for head detection
		int frameIndex = 0;
		if (!SafeResetReader()) {
			std::cerr << "Failed to reset video reader for head detection" << std::endl;
			return 1;
		}
		while (true) {
			double timestamp = m_reader->ReadFrame(d_frameBuf.get(), frameBufSize, m_processingStream);

			if (timestamp < 0.0)
				break; // End of video

			if (!headDetector->DetectHeads(d_frameBuf.as_float(), m_videoWidth, m_videoHeight, allHeadDetections[frameIndex], 0.5f)) {
				std::cerr << "Failed to detect heads" << std::endl;
				return false;
			}

			frameIndex++;

			// Progress callback for pass 2
			if (!UpdateProgress(m_totalFrames + frameIndex, m_totalFrames * m_totalProgressSteps)) {
				std::cout << "Processing cancelled by user" << std::endl;
				return 1;
			}
		}

		// Buffer automatically freed by RAII destructor


		// Clean up head detector to free memory
		headDetector.reset();
		std::cout << "HeadDetector cleanup completed" << std::endl;

		// Force CUDA memory cleanup after head detection
		DRIVER_API_CHECK(cuCtxSynchronize());
		cudaDeviceSynchronize();

		std::cout << "Pass 2 completed: " << frameIndex << " frames processed" << std::endl;

		// ===== HEAD SIZE ANALYSIS =====
		std::cout << "\n=== Analyzing Head Sizes ===" << std::endl;

		// Convert allHeadDetections to m_headInfos
		m_headInfos.resize(m_totalFrames);
		for (int indexFrame = 0; indexFrame < m_totalFrames; ++indexFrame) {
			for (const auto& box : allHeadDetections[indexFrame]) {
				HeadInfo headInfo;
				headInfo.box = box;
				m_headInfos[indexFrame].push_back(headInfo);
			}
		}

		// Free the memory used by allHeadDetections
		std::vector<std::vector<Box>>().swap(allHeadDetections);

		// Get all available InsPyReNet model sizes
		auto availableModelSizes = ImageMattingFactory::GetAvailableModelSizes(ModelType::INSPYRENET);

		std::cout << "Available InsPyReNet model sizes: ";
		for (const auto& size : availableModelSizes) {
			std::cout << size.first << "x" << size.second << " ";
		}
		std::cout << std::endl;

		// Determine required model sizes
		for (int indexFrame = 0; indexFrame < m_totalFrames; ++indexFrame) {
			auto& headInfoFrame = m_headInfos[indexFrame];
			for (int indexHead = 0; indexHead < headInfoFrame.size(); ++indexHead) {
				auto& head = headInfoFrame[indexHead];
				std::pair<int, int> bestModel = { 0, 0 };
				int bestModelArea = INT_MAX;
				for (const auto& modelSize : availableModelSizes) {
					int modelWidth = modelSize.first;
					int modelHeight = modelSize.second;
					int modelArea = modelWidth * modelHeight;
					if (modelWidth >= head.box.width && modelHeight >= head.box.height) {
						// Prefer smaller models that still fit
						if (modelArea < bestModelArea) {
							bestModel = modelSize;
							bestModelArea = modelArea;
						}
					}
				}
				if (m_modelSizesRequired.find(bestModel) == m_modelSizesRequired.end())
					m_modelSizesRequired.insert(std::make_pair(bestModel.first, bestModel.second));
				head.modelSize = bestModel;
			}
		}

		std::cout << "Required model sizes: ";
		for (const auto& size : m_modelSizesRequired) {
			std::cout << size.first << "x" << size.second << " ";
		}
		std::cout << std::endl;


		return 0;
	}
	catch (const std::exception& e) {
		std::cerr << "Error in ProcessPass2_HeadDetection: " << e.what() << std::endl;
		return 1;
	}
}
int VideoBackgroundRemover::ProcessPass3_RefineHeadRegions() {
	try {
		std::cout << "\n=== Pass 3+: Head Region Refinement ===" << std::endl;

		int passNumber = 3;

		int headModelIndex = 0;
		for (const auto& headModelSize : m_modelSizesRequired) {
			int modelWidth = headModelSize.first;
			int modelHeight = headModelSize.second;
			std::cout << "\n=== Pass " << passNumber << ": Head Region Processing " << headModelIndex
					  << " (size " << modelWidth << "x" << modelHeight << ") ===" << std::endl;

			// Declare headFrameCount outside scoped block for later use
			int headFrameCount = 0;

			// Create head alpha writer for this model size
			{
				// Create hardware writer for head regions
				CodecConfig headCfg;
				headCfg.width = modelWidth;
				headCfg.height = modelHeight;
				headCfg.frameRate = m_reader->GetFrameRateDouble();
				headCfg.codecType = CodecConfig::CodecType::HEVC_NVENC;
				headCfg.preset = CodecConfig::Preset::SLOW;
				headCfg.encodeAlpha = true;
				std::string headWriterPath = "head_alpha_" + std::to_string(headModelIndex) + ".mp4";
				auto headWriter = DirectVideoWriter::Create(headWriterPath, headCfg, m_context);
				if (!headWriter) {
					std::cerr << "Failed to create hardware head writer" << std::endl;
					return 1;
				}

				// Create model instance in inner scope to ensure immediate cleanup
				auto m_inspyreNet = ImageMattingFactory::Init(ModelType::INSPYRENET, modelWidth, modelHeight, m_processingStream);
				if (!m_inspyreNet) {
					std::cerr << "Failed to create InsPyReNet model for size " << modelWidth << "x" << modelHeight << std::endl;
					return 1;
				}

				// Allocate reusable frame buffer for extraction using RAII
				size_t frameBufSize = static_cast<size_t>(m_videoWidth) * m_videoHeight * 3 * sizeof(float);
				CudaMemoryRAII d_frameBuf(frameBufSize);

				// Process all frames for this head size
				if (!SafeResetReader()) {
					std::cerr << "Failed to reset video reader for pass 3" << std::endl;
					return 1;
				}
				int frameIndex = 0;
				while (true) {
					double timestamp = m_reader->ReadFrame(d_frameBuf.get(), frameBufSize, m_processingStream);
					if (timestamp < 0.0)
						break; // End of video

					auto& headInfoFrame = m_headInfos[frameIndex];
					for (int indexHead = 0; indexHead < headInfoFrame.size(); ++indexHead) {
						auto& headInfo = headInfoFrame[indexHead];
						if (headInfo.modelSize == headModelSize) {

							launchExtractRegion(d_frameBuf.as_float(), (float*)m_inspyreNet->GetInputBuffer(), m_videoWidth, m_videoHeight,
								(int)headInfo.box.x, (int)headInfo.box.y, headModelSize.first, headModelSize.second, 3, m_processingStream);

							// Run InsPyReNet inference for refined alpha
							if (!m_inspyreNet->Infer()) {
								std::cerr << "Failed to run InsPyReNet inference for frame index " << frameIndex
										  << " head region index " << indexHead << std::endl;
								continue;
							}
							headWriter->WriteGrayFrame((const float*)m_inspyreNet->GetOutputBuffer(), m_processingStream);

							headInfo.headFileIndex = headModelIndex;
							headInfo.frameIndexHeadFile = headFrameCount;
							headFrameCount++;
						}
					}

					frameIndex++;

					// Progress callback for head processing passes
					if (!UpdateProgress((m_totalFrames * 2) + frameIndex, m_totalFrames * m_totalProgressSteps)) {
						std::cout << "Processing cancelled by user" << std::endl;
						return 1;
					}
				}

				// Buffer automatically freed by RAII destructor

				// Finalize head alpha file
				headWriter->Finalize();

				// Explicitly reset the model to force immediate cleanup
				m_inspyreNet.reset();
				std::cout << "Model " << modelWidth << "x" << modelHeight << " explicitly destroyed and GPU memory freed" << std::endl;

				// Force CUDA context synchronization to ensure cleanup is complete
				DRIVER_API_CHECK(cuCtxSynchronize());
			} // End of scoped block - model and writer destructors called here

			std::cout << "Pass " << passNumber << " completed for head region: " << modelWidth << "x" << modelHeight
					  << " with " << headFrameCount << " head frames processed" << std::endl;
			headModelIndex++;
			passNumber++;
		}

		return 0;
	}
	catch (const std::exception& e) {
		std::cerr << "Error in ProcessPass3_HeadRegionProcessing: " << e.what() << std::endl;
		return 1;
	}
}
int VideoBackgroundRemover::ProcessPass4_RefineBodyRegions() {
	try {
		std::cout << "\n=== Pass 4: Body Region Refinement ===" << std::endl;

		// Initialize ProcessBodyRegions
		auto bodyProcessor = std::make_unique<ProcessBodyRegions>();
		if (!bodyProcessor->Initialize(m_videoWidth, m_videoHeight, m_processingStream)) {
			std::cerr << "Failed to initialize body region processor" << std::endl;
			return 1;
		}

		// Open initial alpha file for reading
		auto alphaReader = DirectVideoReader::Create("initial_alpha_hw.mp4", m_context);
		if (!alphaReader) {
			std::cerr << "Failed to create reader for initial alpha" << std::endl;
			return 1;
		}

		// Create writer for body-refined alpha output
		CodecConfig bodyCfg;
		bodyCfg.width = m_videoWidth;
		bodyCfg.height = m_videoHeight;
		bodyCfg.frameRate = m_reader->GetFrameRateDouble();
		bodyCfg.codecType = CodecConfig::CodecType::HEVC_NVENC;
		bodyCfg.preset = CodecConfig::Preset::SLOW;
		bodyCfg.encodeAlpha = true;
		auto bodyAlphaWriter = DirectVideoWriter::Create("body_refined_alpha_hw.mp4", bodyCfg, m_context);
		if (!bodyAlphaWriter) {
			std::cerr << "Failed to create writer for body-refined alpha" << std::endl;
			return 1;
		}

		// Allocate GPU buffers for processing using RAII
		size_t rgbSize = static_cast<size_t>(m_videoWidth) * m_videoHeight * 3 * sizeof(float);
		size_t alphaSize = static_cast<size_t>(m_videoWidth) * m_videoHeight * sizeof(float);

		CudaMemoryRAII d_rgbBuffer(rgbSize);
		CudaMemoryRAII d_alphaBuffer(alphaSize);

		// Reset the main video reader to read RGB frames
		if (!SafeResetReader()) {
			std::cerr << "Failed to reset video reader for body region processing" << std::endl;
			return 1;
		}

		int frameIndex = 0;
		while (frameIndex < m_totalFrames) {
			// Read RGB frame
			double timestamp = m_reader->ReadFrame(d_rgbBuffer.get(), rgbSize, m_processingStream);
			if (timestamp < 0.0) {
				std::cerr << "Unexpected end of video at frame " << frameIndex << std::endl;
				break;
			}

			// Read corresponding alpha frame
			if (alphaReader->ReadAlphaFrame(d_alphaBuffer.as_float(), m_processingStream) < 0.0) {
				std::cerr << "Failed to read alpha frame " << frameIndex << std::endl;
				break;
			}

			// Set head regions for this frame (to avoid processing head areas)
			if (frameIndex < m_headInfos.size()) {
				std::vector<Box> headBoxes;
				for (const auto& headInfo : m_headInfos[frameIndex]) {
					headBoxes.push_back(headInfo.box);
				}
				bodyProcessor->SetHeadRegions(headBoxes);
			}

			// Process body regions (this modifies d_alphaBuffer in-place)
			if (!bodyProcessor->ProcessFrame(d_rgbBuffer.get(), d_alphaBuffer.get())) {
				std::cerr << "Failed to process body regions for frame " << frameIndex << std::endl;
				// Continue with next frame even if this one failed
			}

			// Write the processed alpha frame
			bodyAlphaWriter->WriteGrayFrame(d_alphaBuffer.as_float(), m_processingStream);

			frameIndex++;

			// Progress callback for body processing
			if (!UpdateProgress((m_totalFrames * 3) + frameIndex, m_totalFrames * m_totalProgressSteps)) {
				std::cout << "Body region processing cancelled by user" << std::endl;
				return 1;
			}
		}

		// Finalize body-refined alpha output
		bodyAlphaWriter->Finalize();

		// Buffers automatically freed by RAII destructors

		// Clean up body processor
		bodyProcessor->Cleanup();
		bodyProcessor.reset();

		// Force CUDA memory cleanup
		DRIVER_API_CHECK(cuCtxSynchronize());
		cudaDeviceSynchronize();

		std::cout << "Pass 4 completed: " << frameIndex << " frames processed for body regions" << std::endl;
		return 0;
	}
	catch (const std::exception& e) {
		std::cerr << "Error in ProcessPass4_BodyRegions: " << e.what() << std::endl;
		return 1;
	}
}
int VideoBackgroundRemover::ProcessPass5_ReplaceHeadAlphaWithRefined() {
	try {
		std::cout << "\n=== Pass 5: Alpha Combining - Combining head alphas with body-refined alpha ===" << std::endl;

		// Open all head alpha files for reading
		std::vector<std::shared_ptr<DirectVideoReader>> headAlphaReaders;
		std::vector<int> headAlphaCurrentFrame;

		for (int i = 0; i < m_modelSizesRequired.size(); ++i) {
			std::string headAlphaFile = "head_alpha_" + std::to_string(i) + ".mp4";
			auto reader = DirectVideoReader::Create(headAlphaFile, m_context);
			if (!reader) {
				std::cerr << "Failed to create reader for " << headAlphaFile << std::endl;
				return 1;
			}
			headAlphaReaders.push_back(reader);
			headAlphaCurrentFrame.push_back(0);
		}

		// Open body-refined alpha file for reading (changed from initial_alpha_hw.mp4)
		auto bodyAlphaReader = DirectVideoReader::Create("body_refined_alpha_hw.mp4", m_context);
		if (!bodyAlphaReader) {
			std::cerr << "Failed to create reader for body-refined alpha" << std::endl;
			return 1;
		}

		// Create writer for final refined alpha output using AlphaCodecConfig
		CodecConfig refinedCfg;
		refinedCfg.width = m_videoWidth;
		refinedCfg.height = m_videoHeight;
		refinedCfg.frameRate = m_reader->GetFrameRateDouble();
		refinedCfg.codecType = CodecConfig::CodecType::HEVC_NVENC;
		refinedCfg.encodeAlpha = true;
		auto refinedAlphaWriter = DirectVideoWriter::Create("final_refined_alpha_hw.mp4", refinedCfg, m_context);
		if (!refinedAlphaWriter) {
			std::cerr << "Failed to create writer for final refined alpha" << std::endl;
			return 1;
		}

		// Allocate GPU buffers for alpha using RAII
		CudaMemoryRAII d_Alpha(static_cast<size_t>(m_videoWidth) * m_videoHeight * sizeof(float));

		// Find the maximum model size to allocate head alpha buffer once
		int maxModelWidth = 0, maxModelHeight = 0;
		for (const auto& modelSize : m_modelSizesRequired) {
			maxModelWidth = std::max(maxModelWidth, modelSize.first);
			maxModelHeight = std::max(maxModelHeight, modelSize.second);
		}
		CudaMemoryRAII d_headAlpha(static_cast<size_t>(maxModelWidth) * maxModelHeight * sizeof(float));

		// Process each frame
		int frameIndex = 0;
		int result = 0; // Track return code

		while (frameIndex < m_totalFrames) {
			// Read original alpha for this frame
			if (bodyAlphaReader->ReadAlphaFrame(d_Alpha.as_float(), m_processingStream) < 0.0) {
				std::cerr << "Failed to read original alpha for frame " << frameIndex << std::endl;
				result = 1;
				break;
			}

			// Get head info for this frame
			const auto& headInfoFrame = m_headInfos[frameIndex];

			// Process each head in this frame
			for (const auto& headInfo : headInfoFrame) {
				if (headInfo.headFileIndex >= 0 && headInfo.headFileIndex < headAlphaReaders.size() &&
					headInfo.frameIndexHeadFile >= 0) {

					int& currentFrame = headAlphaCurrentFrame[headInfo.headFileIndex];

					// Skip frames if we're behind
					while (currentFrame < headInfo.frameIndexHeadFile) {
						if (headAlphaReaders[headInfo.headFileIndex]->ReadAlphaFrame(d_headAlpha.as_float(), m_processingStream) < 0.0) {
							std::cerr << "Failed to skip to frame " << headInfo.frameIndexHeadFile
									  << " in head alpha file " << headInfo.headFileIndex << std::endl;
							break;
						}
						currentFrame++;
					}

					// Read the head alpha frame
					if (currentFrame == headInfo.frameIndexHeadFile) {
						if (headAlphaReaders[headInfo.headFileIndex]->ReadAlphaFrame(d_headAlpha.as_float(), m_processingStream) >= 0.0) {
							currentFrame++;

							// Use the model size dimensions that were used during extraction
							// The head was extracted at modelSize dimensions in Pass 3
							int regionWidth = headInfo.modelSize.first;
							int regionHeight = headInfo.modelSize.second;

							// Overwrite main alpha with the head alpha frame starting at box.x, box.y
							// The region was extracted at model size, so we must use model size when combining back
							launchCombineAlphaRegion(
								d_Alpha.as_float(),
								d_headAlpha.as_float(),
								m_videoWidth, m_videoHeight,
								static_cast<int>(headInfo.box.x), static_cast<int>(headInfo.box.y),
								regionWidth, regionHeight,
								m_processingStream);
						}
						else {
							std::cerr << "Failed to read head alpha for frame " << frameIndex
									  << " from file " << headInfo.headFileIndex << std::endl;
						}
					}
				}
			}

			// Write the blended alpha frame
			refinedAlphaWriter->WriteGrayFrame((const float*)d_Alpha.as_float(), m_processingStream);

			frameIndex++;

			if (!UpdateProgress((m_totalFrames * 4) + frameIndex, m_totalFrames * m_totalProgressSteps)) {
				std::cout << "Alpha combining cancelled by user" << std::endl;
				result = 1;
				break;
			}
		}

		// Buffers automatically freed by RAII when going out of scope

		// Finalize refined alpha output
		refinedAlphaWriter->Finalize();

		// Clean up head alpha readers
		headAlphaReaders.clear();

		if (result == 0) {
			std::cout << "Alpha combining completed: " << frameIndex << " frames processed" << std::endl;
		}

		return result;
	}
	catch (const std::exception& e) {
		std::cerr << "Error in ReplaceHeadAlphaWithRefined: " << e.what() << std::endl;

		// RAII buffers will clean themselves up on scope exit

		return 1;
	}
}
int VideoBackgroundRemover::ProcessPass6_EstimateBackground() {
	try {
		std::cout << "\n=== Pass 6: Estimating the background alpha ===" << std::endl;

		// Open body-refined alpha file for reading (changed from initial_alpha_hw.mp4)
		auto refinedAlphaReader = DirectVideoReader::Create("final_refined_alpha_hw.mp4", m_context);
		if (!refinedAlphaReader) {
			std::cerr << "Failed to create reader for final-refined alpha" << std::endl;
			return 1;
		}

		// Create writer for final rgb
		CodecConfig refinedCfg;
		refinedCfg.width = m_videoWidth;
		refinedCfg.height = m_videoHeight;
		refinedCfg.frameRate = m_reader->GetFrameRateDouble();
		refinedCfg.codecType = CodecConfig::CodecType::HEVC_NVENC;
		refinedCfg.encodeAlpha = false;
		auto finalRgbWriter = DirectVideoWriter::Create("final_rgb_hw.mp4", refinedCfg, m_context);
		if (!finalRgbWriter) {
			std::cerr << "Failed to create writer for final rgb" << std::endl;
			return 1;
		}

		// Process all frames for head detection
		int frameIndex = 0;
		if (!SafeResetReader()) {
			std::cerr << "Failed to reset video reader for head detection" << std::endl;
			return 1;
		}

		// Allocate GPU buffers using RAII
		CudaMemoryRAII d_Alpha(static_cast<size_t>(m_videoWidth) * m_videoHeight * sizeof(float));
		size_t frameBufSize = static_cast<size_t>(m_videoWidth) * m_videoHeight * 3 * sizeof(float);
		CudaMemoryRAII d_frameBuf(frameBufSize);

		size_t backgroundDataSize = getBackgroundDataBufferSize(m_videoWidth, m_videoHeight);
		CudaMemoryRAII d_horizontalBgData(backgroundDataSize);
		CudaMemoryRAII d_verticalBgData(backgroundDataSize);
		size_t outputRgbaBufferSize = static_cast<size_t>(m_videoWidth) * m_videoHeight * 3 * sizeof(float);
		CudaMemoryRAII d_outputRgbBuffer(outputRgbaBufferSize);

		// Process each frame
		frameIndex = 0; // Remove duplicate declaration
		while (frameIndex < m_totalFrames) {

			// Read original rgb for this frame
			double timestamp = m_reader->ReadFrame(d_frameBuf.get(), frameBufSize, m_processingStream);
			if (timestamp < 0.0)
				break;

			// Read refined alpha for this frame
			if (refinedAlphaReader->ReadAlphaFrame(d_Alpha.as_float(), m_processingStream) < 0.0) {
				std::cerr << "Failed to read original alpha for frame " << frameIndex << std::endl;
				break;
			}

			// Kernel 1: Horizontal background estimation
			launchEstimateBackgroundHorizontal(
				d_Alpha.as_float(),	   // Alpha matte
				d_frameBuf.as_float(), // Original RGB data (planar format)
				d_horizontalBgData.as_void(),
				m_videoWidth, m_videoHeight,
				m_processingStream);

			// Kernel 2: Vertical background estimation
			launchEstimateBackgroundVertical(
				d_Alpha.as_float(),	   // Alpha matte
				d_frameBuf.as_float(), // Original RGB data (planar format)
				d_verticalBgData.as_void(),
				m_videoWidth, m_videoHeight,
				m_processingStream);

			// Kernel 3: Extract foreground using estimated background and alpha compositing formula
			launchExtractForeground(
				d_Alpha.as_float(),	   // Alpha matte
				d_frameBuf.as_float(), // Original RGB data (planar format)
				d_horizontalBgData.as_void(),
				d_verticalBgData.as_void(),
				d_outputRgbBuffer.as_float(),
				m_videoWidth, m_videoHeight,
				m_processingStream);

			DRIVER_API_CHECK(cuStreamSynchronize(m_processingStream));
			SaveAlphaMatteToPNG("Videos\\output_alpha.png", d_Alpha, m_videoWidth, m_videoHeight);
			SavePlanarFloatImageToPNG("Videos\\output_rgb.png", (CUdeviceptr)d_outputRgbBuffer.get(), m_videoWidth, m_videoHeight, false, m_processingStream);
			// Write final RGB frame
			finalRgbWriter->WriteRgbFrame(d_outputRgbBuffer.as_float(), m_processingStream);

			frameIndex++;

			// Progress callback for blending
			if (!UpdateProgress((m_totalFrames * 5) + frameIndex, m_totalFrames * m_totalProgressSteps)) {
				std::cout << "Alpha blending cancelled by user" << std::endl;
				// Finalize writer to ensure proper cleanup
				if (finalRgbWriter)
					finalRgbWriter->Finalize();

				return 1;
			}
		}

		// Finalize refined alpha output
		finalRgbWriter->Finalize();

		// GPU buffers automatically freed by RAII destructors

		std::cout << "Alpha blending completed: " << frameIndex << " frames processed" << std::endl;
		return 0;
	}
	catch (const std::exception& e) {
		std::cerr << "Error in ProcessPass6_EstimateBackground: " << e.what() << std::endl;
		// RAII buffers automatically clean up in exception handler
		return 1;
	}
}
void VideoBackgroundRemover::Cleanup() {
	// 1) Ensure all readers/writers are closed while CUDA context is still alive
	if (m_reader) {
		// Ensure the right context is current for CUDA frees inside Close()
		cuCtxSetCurrent(m_context);
		m_reader->Close();
		m_reader.reset();
	}

	// 2) Destroy your processing stream
	if (m_processingStream) {
		cuStreamDestroy(m_processingStream);
		m_processingStream = nullptr;
	}

	// 3) Unload kernels/modules that depend on the context
	KernelManager::GetInstance().Cleanup();

	// 4) Finally, destroy the CUDA context
	if (m_context) {
		cuCtxDestroy(m_context);
		m_context = nullptr;
	}

	// 5) Clear containers
	m_headInfos.clear();
	m_modelSizesRequired.clear();
}

bool VideoBackgroundRemover::UpdateProgress(int currentFrame, int totalSteps) {
	if (m_progressCb) {
		return m_progressCb(currentFrame, totalSteps, m_userData);
	}
	return true; // Continue if no callback
}

int VideoBackgroundRemover::RenameFinalFilesAndCleanup() {
	try {
		std::cout << "\n=== Renaming final files and cleaning up temporary files ===" << std::endl;

		// Convert wide string path to regular string
		std::string outputPathStr = Helpers::WStringToString(m_outputPath);
		std::filesystem::path outputPath(outputPathStr);
		
		// Get the directory, stem, and extension
		std::filesystem::path outputDir = outputPath.parent_path();
		std::string stem = outputPath.stem().string();
		std::string ext = outputPath.extension().string();
		
		// Create final paths with " rgb" and " alpha" added before extension
		std::filesystem::path finalRgbPath = outputDir / (stem + " rgb" + ext);
		std::filesystem::path finalAlphaPath = outputDir / (stem + " alpha" + ext);
		
		// Copy final files to their destinations
		std::error_code ec;
		
		// Copy RGB file
		if (std::filesystem::exists("final_rgb_hw.mp4")) {
			std::filesystem::copy_file("final_rgb_hw.mp4", finalRgbPath, 
				std::filesystem::copy_options::overwrite_existing, ec);
			if (ec) {
				std::cerr << "Failed to copy RGB file to " << finalRgbPath << ": " << ec.message() << std::endl;
				return 1;
			}
			std::cout << "RGB output saved to: " << finalRgbPath << std::endl;
		} else {
			std::cerr << "Error: final_rgb_hw.mp4 not found" << std::endl;
			return 1;
		}
		
		// Copy Alpha file
		if (std::filesystem::exists("final_refined_alpha_hw.mp4")) {
			std::filesystem::copy_file("final_refined_alpha_hw.mp4", finalAlphaPath, 
				std::filesystem::copy_options::overwrite_existing, ec);
			if (ec) {
				std::cerr << "Failed to copy Alpha file to " << finalAlphaPath << ": " << ec.message() << std::endl;
				return 1;
			}
			std::cout << "Alpha output saved to: " << finalAlphaPath << std::endl;
		} else {
			std::cerr << "Error: final_refined_alpha_hw.mp4 not found" << std::endl;
			return 1;
		}
		
		// Delete all temporary files
		std::cout << "Cleaning up temporary files..." << std::endl;
		
		// List of temporary files to delete
		std::vector<std::string> tempFiles = {
			"initial_alpha_hw.mp4",
			"body_refined_alpha_hw.mp4",
			"final_refined_alpha_hw.mp4",
			"final_rgb_hw.mp4"
		};
		
		// Add head alpha files
		for (int i = 0; i < m_modelSizesRequired.size(); ++i) {
			tempFiles.push_back("head_alpha_" + std::to_string(i) + ".mp4");
		}
		
		// Delete each temporary file
		for (const auto& file : tempFiles) {
			if (std::filesystem::exists(file)) {
				std::filesystem::remove(file, ec);
				if (ec) {
					std::cout << "Warning: Failed to delete " << file << ": " << ec.message() << std::endl;
				} else {
					std::cout << "Deleted: " << file << std::endl;
				}
			}
		}
		
		// Also delete any debug PNG files if they exist
		if (std::filesystem::exists("Videos")) {
			for (const auto& entry : std::filesystem::directory_iterator("Videos")) {
				if (entry.path().extension() == ".png") {
					std::filesystem::remove(entry.path(), ec);
				}
			}
		}
		
		std::cout << "File renaming and cleanup completed successfully" << std::endl;
		return 0;
	}
	catch (const std::exception& e) {
		std::cerr << "Error in RenameFinalFilesAndCleanup: " << e.what() << std::endl;
		return 1;
	}
}

bool VideoBackgroundRemover::SafeResetReader() {
	if (!m_reader) {
		std::cerr << "Reader is null, cannot reset" << std::endl;
		return false;
	}

	try {
		// Store reader properties before recreating
		int width = m_reader->GetWidth();
		int height = m_reader->GetHeight();
		double duration = m_reader->GetDuration();
		AVRational frameRate = m_reader->GetFrameRate();

		std::cout << "Safely resetting video reader..." << std::endl;

		// Close the current reader
		m_reader->Close();

		// Recreate the reader
		std::string inputPathStr = Helpers::WStringToString(m_inputPath);
		m_reader = DirectVideoReader::Create(inputPathStr, m_context);

		if (!m_reader) {
			std::cerr << "Failed to recreate video reader" << std::endl;
			return false;
		}

		// Verify the properties are the same
		if (m_reader->GetWidth() != width || m_reader->GetHeight() != height) {
			std::cerr << "Reader properties changed after reset" << std::endl;
			return false;
		}

		std::cout << "Video reader reset successfully" << std::endl;
		return true;
	}
	catch (const std::exception& e) {
		std::cerr << "Exception during SafeResetReader: " << e.what() << std::endl;
		return false;
	}
}
