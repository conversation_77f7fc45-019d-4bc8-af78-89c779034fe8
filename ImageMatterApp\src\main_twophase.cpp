// CUDA ProRes 4444 Encoding with AI Alpha Matting - Two-Phase Processing
//
// This application processes video files using a two-phase approach:
// Phase 1: Initial alpha matte generation and compression
// Phase 2: Alpha refinement with IndexNet and background estimation
//
// This approach manages GPU memory constraints more efficiently by freeing
// the initial matting model between phases.

#define NOMINMAX // Disable the min/max macros from windows.h which interfere with std::min/max
#include <Windows.h>
#include <iostream>
#include <string>
#include "VideoBackgroundRemover.h"

// Progress callback for demonstration
bool __stdcall MyProgressCallback(int currentFrame, int totalFrames, void* userData) {
    if (currentFrame % 10 == 0) {
        if (totalFrames > 0) {
            std::cout << "Progress: " << currentFrame << "/" << totalFrames
                     << " (" << (currentFrame * 100 / totalFrames) << "%)" << std::endl;
        } else {
            std::cout << "Processed " << currentFrame << " frames" << std::endl;
        }
    }
    return true; // Continue processing
}

// Test function using the shared VideoBackgroundRemover function
bool TestTwoPhaseProcessing() {
    std::wstring inputPath = L"Videos\\input short.mp4";
    std::wstring outputPath = L"Videos\\output_twophase_prores.mov";

    // Call the shared function with multi-pass processing
    int result = VideoBackgroundRemover(
        inputPath,
        outputPath,
        ENGINE_AUTO,  // Use automatic engine selection
        MyProgressCallback,
        nullptr
    );

    return result == 0;
}

int main(int argc, char* argv[]) {
    std::cout << "=== Two-Phase CUDA ProRes 4444 Encoding with AI Alpha Matting ===" << std::endl;

    // Test the two-phase processing
    if (TestTwoPhaseProcessing()) {
        std::cout << "Two-phase processing completed successfully!" << std::endl;
        return 0;
    } else {
        std::cout << "Two-phase processing failed!" << std::endl;
        return 1;
    }
}
