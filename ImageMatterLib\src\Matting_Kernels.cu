#include "Matting_Kernels.cuh"
#include "KernelManager.h"
#include <device_launch_parameters.h>
#define NOMINMAX // Disable the min/max macros from windows.h which interfere with std::min/max
#include <Windows.h>
#include <iostream>
#include <exception>

#ifdef __CUDACC__

// CUDA kernel for preprocessing an RGB/RGBA buffer with standard image normalization for inferring
extern "C" __global__ void PreprocessBufferKernel(float* outputBuffer, const float* inputBuffer, int width, int height, bool isRGBA, NormalizationParams normParams) {
    const int x = blockIdx.x * blockDim.x + threadIdx.x;
    const int y = blockIdx.y * blockDim.y + threadIdx.y;

    if (x < width && y < height) {
        int index = y * width + x;
        int planeSize = width * height;

        // Read pixel from input buffer (RGB or RGBA format)
        int pixelIndex = y * width + x;

        float r = inputBuffer[pixelIndex];                     // R plane
        float g = inputBuffer[pixelIndex + planeSize];         // G plane
        float b = inputBuffer[pixelIndex + 2 * planeSize];     // B plane
        float a = (isRGBA ? inputBuffer[pixelIndex + 3 * planeSize] : 0);  // A plane if exists

        // Normalize and store in output buffer (planar format: all R, then all G, then all B)
        outputBuffer[index] = (r - normParams.meanR) / normParams.stdR;                 // R channel
        outputBuffer[index + planeSize] = (g - normParams.meanG) / normParams.stdG;     // G channel
        outputBuffer[index + 2 * planeSize] = (b - normParams.meanB) / normParams.stdB; // B channel
        if (isRGBA) outputBuffer[index + 3 * planeSize] = a; // A channel
    }
}

// Launcher function for the PreprocessBufferKernel
CUresult LaunchPreprocessBufferKernel(float* outputBuffer, const float* inputBuffer, int width, int height, bool isRGBA, const NormalizationParams& normParams, CUstream stream) {
    // Define block and grid dimensions for the kernel launch
    unsigned int blockSizeX = 16;
    unsigned int blockSizeY = 16;
    unsigned int gridSizeX = (width + blockSizeX - 1) / blockSizeX;
    unsigned int gridSizeY = (height + blockSizeY - 1) / blockSizeY;

    if (gridSizeX == 0 || gridSizeY == 0) {
        std::cerr << "Invalid kernel configuration!" << std::endl;
        return CUDA_ERROR_INVALID_VALUE;
    }

    try {
        CUfunction kernel = KernelHelpers::GetKernelFunction("PreprocessBufferKernel");
        void* kernelArgs[] = { &outputBuffer, &inputBuffer, &width, &height, &isRGBA, (void*)&normParams };
        CUresult result = KernelHelpers::LaunchKernel(kernel,
            gridSizeX, gridSizeY, 1, // Grid dimensions
            blockSizeX, blockSizeY, 1, // Block dimensions
            0, stream, kernelArgs);
            
        if (result != CUDA_SUCCESS) {
            const char* errorStr;
            cuGetErrorString(result, &errorStr);
            std::cerr << "LaunchPreprocessBufferKernel: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
        }
        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "LaunchPreprocessBufferKernel: Exception: " << e.what() << std::endl;
        return CUDA_ERROR_LAUNCH_FAILED;
    }
}


#endif // __CUDACC__