// D3D11VideoReader.cpp
#include "D3D11VideoReader.h"
#include "FFmpegIOKernels.cuh"
#include <stdexcept>
#include <iostream>

static void CheckCudaError(CUresult error) {
    if (error != CUDA_SUCCESS) {
        const char* errorStr;
        cuGetErrorString(error, &errorStr);
        throw std::runtime_error(std::string("CUDA error: ") + (errorStr ? errorStr : "unknown"));
    }
}

std::shared_ptr<D3D11VideoReader> D3D11VideoReader::Create(ID3D11Device1* d3dDevice, const std::string& filePath) {
    std::shared_ptr<D3D11VideoReader> reader(new D3D11VideoReader());
    if (!reader->Initialize(d3dDevice, filePath)) {
        return nullptr;
    }
    return reader;
}

D3D11VideoReader::D3D11VideoReader()
    : m_d3dDevice(nullptr), m_d3dContext(nullptr),
      m_cudaContext(nullptr), m_cudaDevice(0), m_cudaStream(nullptr),
      m_tempRgbBuffer(0), m_tempAlphaBuffer(0), m_tempBgraBuffer(0),
      m_tempRgbBufferSize(0), m_tempAlphaBufferSize(0), m_tempBgraBufferSize(0),
      m_width(0), m_height(0), m_duration(0.0), m_frameRate({0,1}), m_timeBase({0,1}),
      m_textureFormat(Helpers::TextureFormat::Unknown), m_isInitialized(false) {}

D3D11VideoReader::~D3D11VideoReader() {
    Close();
}

bool D3D11VideoReader::Initialize(ID3D11Device1* d3dDevice, const std::string& filePath) {
    try {
        if (!d3dDevice) return false;
        m_d3dDevice = d3dDevice;
        m_d3dDevice->GetImmediateContext1(&m_d3dContext);

        // Initialize CUDA with D3D11 interop
        int deviceCount = 0;
        cuInit(0);
        CheckCudaError(cuDeviceGetCount(&deviceCount));
        if (deviceCount <= 0) {
            std::cerr << "No CUDA devices found" << std::endl;
            return false;
        }
        CheckCudaError(cuDeviceGet(&m_cudaDevice, 0));
        CheckCudaError(cuCtxCreate(&m_cudaContext, 0, m_cudaDevice));
        CheckCudaError(cuStreamCreate(&m_cudaStream, CU_STREAM_NON_BLOCKING));

        // Initialize video reader using this CUDA context
        m_videoReader = MultiVideoReader::Create(filePath, m_cudaContext);
        if (!m_videoReader) {
            std::cerr << "Failed to create MultiVideoReader" << std::endl;
            return false;
        }

        // Cache properties
        m_width = m_videoReader->GetWidth();
        m_height = m_videoReader->GetHeight();
        m_duration = m_videoReader->GetDuration();
        m_frameRate = m_videoReader->GetFrameRate();
        m_timeBase = m_videoReader->GetTimeBase();
        m_textureFormat = m_videoReader->GetTextureFormat();

        // Allocate temp buffers
        m_tempRgbBufferSize = (size_t)m_width * m_height * 3 * sizeof(float);
        m_tempAlphaBufferSize = (size_t)m_width * m_height * sizeof(float);
        m_tempBgraBufferSize = (size_t)m_width * m_height * 4; // 4 bytes per pixel

        CheckCudaError(cuMemAlloc(&m_tempRgbBuffer, m_tempRgbBufferSize));
        CheckCudaError(cuMemAlloc(&m_tempAlphaBuffer, m_tempAlphaBufferSize));
        CheckCudaError(cuMemAlloc(&m_tempBgraBuffer, m_tempBgraBufferSize));

        m_isInitialized = true;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "Error initializing D3D11VideoReader: " << e.what() << std::endl;
        Close();
        return false;
    }
}

bool D3D11VideoReader::RegisterTextureWithCuda(ID3D11Texture2D* texture, cudaGraphicsResource_t& resource) {
    if (!texture) return false;

    // Use appropriate flags for shared resources
    unsigned int flags = cudaGraphicsRegisterFlagsNone;
    if (this->IsSharedTexture(texture)) {
        // For shared resources, we may want to use surface load/store
        flags = cudaGraphicsRegisterFlagsSurfaceLoadStore;
    }

    cudaError_t err = cudaGraphicsD3D11RegisterResource(&resource, texture, flags);
    if (err != cudaSuccess) {
        std::cerr << "cudaGraphicsD3D11RegisterResource failed: " << cudaGetErrorString(err) << std::endl;
        return false;
    }
    return true;
}


bool D3D11VideoReader::ValidateTexture(ID3D11Texture2D* texture, DXGI_FORMAT expectedFormat) {
    if (!texture) return false;
    D3D11_TEXTURE2D_DESC desc = {};
    texture->GetDesc(&desc);
    if (desc.Format != expectedFormat) {
        std::cerr << "Texture format mismatch. Expected: " << expectedFormat << std::endl;
        return false;
    }
    if ((int)desc.Width != m_width || (int)desc.Height != m_height) {
        std::cerr << "Texture size mismatch. Expected: " << m_width << "x" << m_height << std::endl;
        return false;
    }
    return true;
}

bool D3D11VideoReader::CopyToD3D11Texture(CUdeviceptr cudaBuffer, ID3D11Texture2D* d3dTexture, size_t dataSize, bool isRgba) {
    if (!cudaBuffer || !d3dTexture) return false;

    // Handle keyed mutex for shared textures
    bool isShared = this->IsSharedTexture(d3dTexture);
    if (isShared) {
        // Acquire mutex with key 0 (you can make this configurable if needed)
        if (!AcquireKeyedMutex(d3dTexture, 0, 5000)) { // 5 second timeout
            std::cerr << "Failed to acquire keyed mutex for shared texture" << std::endl;
            return false;
        }
    }

    cudaGraphicsResource_t resource = nullptr;
    if (!RegisterTextureWithCuda(d3dTexture, resource)) {
        if (isShared) ReleaseKeyedMutex(d3dTexture, 1);
        return false;
    }

    cudaError_t err = cudaGraphicsMapResources(1, &resource, 0);
    if (err != cudaSuccess) {
        std::cerr << "cudaGraphicsMapResources failed: " << cudaGetErrorString(err) << std::endl;
        cudaGraphicsUnmapResources(1, &resource, 0);
        cudaGraphicsUnregisterResource(resource);
        if (isShared) ReleaseKeyedMutex(d3dTexture, 1);
        return false;
    }

    cudaArray_t array = nullptr;
    err = cudaGraphicsSubResourceGetMappedArray(&array, resource, 0, 0);
    if (err != cudaSuccess) {
        std::cerr << "cudaGraphicsSubResourceGetMappedArray failed: " << cudaGetErrorString(err) << std::endl;
        cudaGraphicsUnmapResources(1, &resource, 0);
		cudaGraphicsUnregisterResource(resource);
        if (isShared) ReleaseKeyedMutex(d3dTexture, 1);
        return false;
    }

    // Copy data to the mapped array
    // Determine pitch based on data size and image dimensions
    size_t pitch;
    if (dataSize == (size_t)m_width * m_height) {
        // Single channel byte data (R8_UNORM for alpha)
        pitch = m_width;
    } else if (dataSize == (size_t)m_width * m_height * 4) {
        // 4-channel byte data (BGRA8_UNORM)
        pitch = m_width * 4;
    } else if (dataSize == (size_t)m_width * m_height * sizeof(float)) {
        // Single channel float data (R32_FLOAT)
        pitch = m_width * sizeof(float);
    } else if (dataSize == (size_t)m_width * m_height * 4 * sizeof(float)) {
        // 4-channel float data (RGBA32_FLOAT)
        pitch = m_width * 4 * sizeof(float);
    } else {
        // Fallback based on isRgba flag
        pitch = isRgba ? m_width * 16 : m_width * 4;
    }

    err = cudaMemcpy2DToArray(array, 0, 0, (void*)cudaBuffer, pitch, pitch, m_height, cudaMemcpyDeviceToDevice);

    if (err != cudaSuccess) {
        std::cerr << "cudaMemcpy2DToArray failed: " << cudaGetErrorString(err) << std::endl;
        cudaGraphicsUnmapResources(1, &resource, 0);
        cudaGraphicsUnregisterResource(resource);
        if (isShared) ReleaseKeyedMutex(d3dTexture, 1);
        return false;
    }

    cudaGraphicsUnmapResources(1, &resource, 0);
    cudaGraphicsUnregisterResource(resource);

    // Release keyed mutex for shared textures
    if (isShared) {
        if (!ReleaseKeyedMutex(d3dTexture, 1)) { // Release with key 1 for next user
            std::cerr << "Failed to release keyed mutex" << std::endl;
        }
    }

    return true;
}

bool D3D11VideoReader::Seek(double timeInSeconds) {
    if (!m_isInitialized) return false;
    return m_videoReader->Seek(timeInSeconds);
}

double D3D11VideoReader::ReadFrame(ID3D11Texture2D* d3dTexture) {
    if (!m_isInitialized || !d3dTexture) return -1.0;
    if (!ValidateTexture(d3dTexture, DXGI_FORMAT_B8G8R8A8_UNORM)) return -1.0;

    // ReadTransparentFrame returns BGRA byte format
    double ts = m_videoReader->ReadTransparentFrame(m_tempBgraBuffer, m_tempBgraBufferSize, 0);
    if (ts < 0.0) return -1.0;

    if (!CopyToD3D11Texture(m_tempBgraBuffer, d3dTexture, m_tempBgraBufferSize, false)) {
        return -1.0;
    }

    return ts;
}

double D3D11VideoReader::ReadAlphaFrame(ID3D11Texture2D* d3dTexture) {
    if (!m_isInitialized || !d3dTexture) return -1.0;
    if (!ValidateTexture(d3dTexture, DXGI_FORMAT_R8_UNORM)) return -1.0;

    // Allocate temporary byte buffer for alpha
    size_t byteAlphaSize = (size_t)m_width * m_height;
    CUdeviceptr tempByteAlpha = 0;
    Helpers::CheckCudaError(cuMemAlloc(&tempByteAlpha, byteAlphaSize));

    // Read BGRA frame and extract alpha channel
    double ts = m_videoReader->ReadTransparentFrame(m_tempBgraBuffer, m_tempBgraBufferSize, 0);
    if (ts < 0.0) {
        cuMemFree(tempByteAlpha);
        return -1.0;
    }

    // Extract alpha channel from BGRA (every 4th byte starting from offset 3)
    // TODO: Add a CUDA kernel to extract alpha channel from BGRA
    // For now, we'll use a simple approach
    launchExtractAlphaFromBgra(reinterpret_cast<uint8_t*>(m_tempBgraBuffer),
                               reinterpret_cast<uint8_t*>(tempByteAlpha),
                               m_width, m_height, 0);

    if (!CopyToD3D11Texture(tempByteAlpha, d3dTexture, byteAlphaSize, false)) {
        cuMemFree(tempByteAlpha);
        return -1.0;
    }

    cuMemFree(tempByteAlpha);
    return ts;
}

double D3D11VideoReader::ReadTransparentFrame(ID3D11Texture2D* d3dTexture) {
    if (!m_isInitialized || !d3dTexture) return -1.0;
    if (!ValidateTexture(d3dTexture, DXGI_FORMAT_B8G8R8A8_UNORM)) return -1.0;

    double ts = m_videoReader->ReadTransparentFrame(m_tempBgraBuffer, m_tempBgraBufferSize, 0);
    if (ts < 0.0) return -1.0;

    if (!CopyToD3D11Texture(m_tempBgraBuffer, d3dTexture, m_tempBgraBufferSize, false)) return -1.0;
    return ts;
}

void D3D11VideoReader::Close() {

    if (m_tempRgbBuffer) { cuMemFree(m_tempRgbBuffer); m_tempRgbBuffer = 0; m_tempRgbBufferSize = 0; }
    if (m_tempAlphaBuffer) { cuMemFree(m_tempAlphaBuffer); m_tempAlphaBuffer = 0; m_tempAlphaBufferSize = 0; }
    if (m_tempBgraBuffer) { cuMemFree(m_tempBgraBuffer); m_tempBgraBuffer = 0; m_tempBgraBufferSize = 0; }

    if (m_cudaStream) { cuStreamDestroy(m_cudaStream); m_cudaStream = nullptr; }
    if (m_cudaContext) { cuCtxDestroy(m_cudaContext); m_cudaContext = nullptr; }

    if (m_d3dContext) { m_d3dContext->Release(); m_d3dContext = nullptr; }

    m_videoReader.reset();
    m_isInitialized = false;
}

Helpers::TextureFormat D3D11VideoReader::GetVideoStreamFormat() const {
    if (!m_videoReader) return Helpers::TextureFormat::Unknown;
    return m_videoReader->GetVideoStreamFormat();
}

bool D3D11VideoReader::IsSharedTexture(ID3D11Texture2D* texture) {
    if (!texture) return false;

    D3D11_TEXTURE2D_DESC desc;
    texture->GetDesc(&desc);

    return (desc.MiscFlags & (D3D11_RESOURCE_MISC_SHARED |
                              D3D11_RESOURCE_MISC_SHARED_NTHANDLE |
                              D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX)) != 0;
}

bool D3D11VideoReader::AcquireKeyedMutex(ID3D11Texture2D* texture, UINT64 key, DWORD timeout) {
    if (!texture) return false;

    IDXGIKeyedMutex* keyedMutex = nullptr;
    HRESULT hr = texture->QueryInterface(__uuidof(IDXGIKeyedMutex), (void**)&keyedMutex);

    if (SUCCEEDED(hr) && keyedMutex) {
        hr = keyedMutex->AcquireSync(key, timeout);
        keyedMutex->Release();
        return SUCCEEDED(hr);
    }

    // If no keyed mutex interface, it's probably not a keyed mutex resource
    return true; // Allow operation to continue
}

bool D3D11VideoReader::ReleaseKeyedMutex(ID3D11Texture2D* texture, UINT64 key) {
    if (!texture) return false;

    IDXGIKeyedMutex* keyedMutex = nullptr;
    HRESULT hr = texture->QueryInterface(__uuidof(IDXGIKeyedMutex), (void**)&keyedMutex);

    if (SUCCEEDED(hr) && keyedMutex) {
        hr = keyedMutex->ReleaseSync(key);
        keyedMutex->Release();
        return SUCCEEDED(hr);
    }

    // If no keyed mutex interface, it's probably not a keyed mutex resource
    return true; // Allow operation to continue
}
