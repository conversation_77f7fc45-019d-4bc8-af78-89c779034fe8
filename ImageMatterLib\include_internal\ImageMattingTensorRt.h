#pragma once

#define NOMINMAX // Disable the min/max macros from windows.h which interfere with std::min/max
#include <Windows.h>
#include <cuda.h>
#include <NvInfer.h>
#include <NvInferRuntime.h>
#include <memory>
#include <string>
#include <ostream>
#include <vector>
#include <iostream>
#include "ImageMatting.h"
#include "Matting_Kernels.cuh"

// TensorRT Logger class
class TensorRtLogger : public nvinfer1::ILogger {
public:
    void log(Severity severity, const char* msg) noexcept override {
        // Only log warnings and errors to reduce verbosity
        if (severity <= Severity::kWARNING) {
            std::cout << "[TensorRT] " << msg << std::endl;
        }
    }
};

class ImageMattingTensorRt : public ImageMatting {
public:
    ImageMattingTensorRt();
    ~ImageMattingTensorRt();

    /**
     * Initialize the image matting with a TensorRT engine file
     * Uses CUDA resources for operations and allocates input/output buffers
     * @param enginePath Path to the TensorRT engine file (.trt or .engine)
     * @param modelWidth Width of the model input (required)
     * @param modelHeight Height of the model input (required)
     * @param imageWidth Width of the images that will be processed
     * @param imageHeight Height of the images that will be processed
     * @param normParams Normalization parameters for preprocessing (mean and std values)
     * @param isRgba Whether the input buffer will be in RGBA format (true) or RGB format (false)
     * @param externalStream CUDA stream to use for operations
     * @return True on success, false on failure
     */
    bool Init(
        const wchar_t* enginePath,
        int modelWidth,
        int modelHeight,
        int imageWidth,
        int imageHeight,
        const NormalizationParams& normParams,
        bool isRgba = false,
        ResizeMethod resizeMethod = ResizeMethod::EXTEND_SHRINK_LANCZOS,
        CUstream externalStream = nullptr);

    /**
     * Run inference on the current loaded model
     * Performs inference using internally allocated CUDA buffers
     * The caller should copy data to the input buffer (obtained via GetInputBuffer())
     * and read results from the output buffer (obtained via GetOutputBuffer()) after inference
     * @return True on success, false on failure
     */
    bool Infer() override;

    /**
     * Get pointer to the input buffer for copying image data
     * @return CUDA device pointer to the input buffer (RGB/RGBA float format)
     */
    CUdeviceptr GetInputBuffer() const override { return m_deviceInputBuffer; }

    /**
     * Get pointer to the output buffer for reading alpha mask results
     * @return CUDA device pointer to the output buffer (single channel float format)
     */
    CUdeviceptr GetOutputBuffer() const override { return m_deviceOutputBuffer; }

    /**
     * Get the size of the input buffer in bytes
     * @return Size of input buffer in bytes
     */
    size_t GetInputBufferSize() const override { return m_inputBufferSize; }

    /**
     * Get the size of the output buffer in bytes
     * @return Size of output buffer in bytes
     */
    size_t GetOutputBufferSize() const override { return m_outputBufferSize; }

    /**
     * Shutdown and release all resources
     */
    void Shutdown() override;

    /**
     * Check if the instance is initialized
     * @return True if initialized, false otherwise
     */
    bool IsInitialized() const override { return m_initialized; }

    // Add getter functions for model dimensions
    int GetModelWidth() const override { return m_modelInputWidth; }
    int GetModelHeight() const override { return m_modelInputHeight; }

    // Add getter functions for image dimensions
    int GetImageWidth() const override { return m_imageWidth; }
    int GetImageHeight() const override { return m_imageHeight; }

    // Get the number of channels in the input format
    int GetInputChannels() const override { return m_isRgba ? 4 : 3; }

    // Get the CUDA stream for synchronization if needed
    CUstream GetCudaStream() const override { return m_cudaStream; }

    /**
     * Get the implementation type as a string for debugging/logging
     * @return String identifying the implementation ("TensorRT")
     */
    const char* GetImplementationType() const override { return "TensorRT"; }

private:
    // Note: Preprocessing and postprocessing now use base class implementations

    // TensorRT resources
    std::unique_ptr<TensorRtLogger> m_logger;
    std::unique_ptr<nvinfer1::IRuntime> m_runtime;
    std::unique_ptr<nvinfer1::ICudaEngine> m_engine;
    std::unique_ptr<nvinfer1::IExecutionContext> m_context;

    // Model information
    std::string m_inputName;
    std::string m_outputName;
    int m_inputIndex;
    int m_outputIndex;
    int m_modelInputWidth;
    int m_modelInputHeight;

    // Image dimensions
    int m_imageWidth;
    int m_imageHeight;
    float m_scaleRatioX;
    float m_scaleRatioY;

    // CUDA resources
    CUdeviceptr m_deviceInputBuffer;         // Input buffer at original image size
    CUdeviceptr m_deviceOutputBuffer;        // Output buffer at original image size
    CUdeviceptr m_deviceResizedInputBuffer;  // Resized RGB for model input
    CUdeviceptr m_deviceResizedOutputBuffer; // Resized alpha from model output
    CUdeviceptr m_preprocessedBuffer;        // Preprocessed buffer at original size
    size_t m_inputBufferSize;
    size_t m_outputBufferSize;
    size_t m_preprocessedBufferSize;    // Size of the preprocessed buffer
    CUstream m_cudaStream;          // CUDA stream for operations

    // Input format
    bool m_isRgba;  // Whether input is in RGBA format

    // Normalization parameters
    NormalizationParams m_normalizationParams;

    // Resize method
    ResizeMethod m_resizeMethod;

    bool m_initialized;

    // Helper methods
    bool LoadEngine(const wchar_t* enginePath);
    bool SetupBindings();
    void CleanupResources();
};
