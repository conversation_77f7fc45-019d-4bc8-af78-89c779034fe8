// D3D11VideoReaderAPI.cpp - C API implementation for C# interop
#include "../../include/D3D11VideoReader.h"  // Public API header
#include "D3D11VideoReader.h"                // Internal implementation header
#include <memory>
#include <string>
#include <iostream>

// Internal structure to hold the C++ class instance
struct D3D11VideoReaderWrapper {
    std::shared_ptr<D3D11VideoReader> reader;
};

// Helper function to safely cast handle
static D3D11VideoReaderWrapper* GetWrapper(D3D11VideoReaderHandle handle) {
    return static_cast<D3D11VideoReaderWrapper*>(handle);
}

// ============================================================================
// Core Functions Implementation
// ============================================================================

int D3D11VideoReader_Create(
    ID3D11Device1* device,
    const char* filePath,
    D3D11VideoReaderHandle* outHandle) {
    
    if (!device || !filePath || !outHandle) {
        return D3D11_VIDEO_READER_ERROR_INVALID_PARAMETER;
    }

    try {
        // Create the C++ reader instance
        auto reader = D3D11VideoReader::Create(device, std::string(filePath));
        
        if (!reader) {
            return D3D11_VIDEO_READER_ERROR_INITIALIZATION_FAILED;
        }

        // Wrap it in our handle structure
        auto wrapper = new D3D11VideoReaderWrapper();
        wrapper->reader = reader;
        
        *outHandle = wrapper;
        return D3D11_VIDEO_READER_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "D3D11VideoReader_Create error: " << e.what() << std::endl;
        return D3D11_VIDEO_READER_ERROR_INITIALIZATION_FAILED;
    }
    catch (...) {
        return D3D11_VIDEO_READER_ERROR_UNKNOWN;
    }
}

void D3D11VideoReader_Destroy(D3D11VideoReaderHandle handle) {
    if (!handle) return;
    
    try {
        auto wrapper = GetWrapper(handle);
        if (wrapper) {
            if (wrapper->reader) {
                wrapper->reader->Close();
            }
            delete wrapper;
        }
    }
    catch (...) {
        // Suppress exceptions in destructor
    }
}

// ============================================================================
// Video Properties Implementation
// ============================================================================

int D3D11VideoReader_GetProperties(
    D3D11VideoReaderHandle handle,
    D3D11VideoReaderProperties* outProperties) {
    
    if (!handle || !outProperties) {
        return D3D11_VIDEO_READER_ERROR_INVALID_PARAMETER;
    }

    try {
        auto wrapper = GetWrapper(handle);
        if (!wrapper || !wrapper->reader) {
            return D3D11_VIDEO_READER_ERROR_INVALID_PARAMETER;
        }

        outProperties->width = wrapper->reader->GetWidth();
        outProperties->height = wrapper->reader->GetHeight();
        outProperties->duration = wrapper->reader->GetDuration();
        outProperties->frameRate = wrapper->reader->GetFrameRateDouble();
        outProperties->totalFrames = static_cast<int>(
            outProperties->duration * outProperties->frameRate + 0.5);
        
        return D3D11_VIDEO_READER_SUCCESS;
    }
    catch (...) {
        return D3D11_VIDEO_READER_ERROR_UNKNOWN;
    }
}

int D3D11VideoReader_GetWidth(D3D11VideoReaderHandle handle) {
    if (!handle) return 0;
    
    try {
        auto wrapper = GetWrapper(handle);
        if (wrapper && wrapper->reader) {
            return wrapper->reader->GetWidth();
        }
    }
    catch (...) {
    }
    return 0;
}

int D3D11VideoReader_GetHeight(D3D11VideoReaderHandle handle) {
    if (!handle) return 0;
    
    try {
        auto wrapper = GetWrapper(handle);
        if (wrapper && wrapper->reader) {
            return wrapper->reader->GetHeight();
        }
    }
    catch (...) {
    }
    return 0;
}

double D3D11VideoReader_GetDuration(D3D11VideoReaderHandle handle) {
    if (!handle) return 0.0;
    
    try {
        auto wrapper = GetWrapper(handle);
        if (wrapper && wrapper->reader) {
            return wrapper->reader->GetDuration();
        }
    }
    catch (...) {
    }
    return 0.0;
}

double D3D11VideoReader_GetFrameRate(D3D11VideoReaderHandle handle) {
    if (!handle) return 0.0;
    
    try {
        auto wrapper = GetWrapper(handle);
        if (wrapper && wrapper->reader) {
            return wrapper->reader->GetFrameRateDouble();
        }
    }
    catch (...) {
    }
    return 0.0;
}

// ============================================================================
// Seeking Implementation
// ============================================================================

int D3D11VideoReader_Seek(
    D3D11VideoReaderHandle handle,
    double timeInSeconds) {
    
    if (!handle) {
        return D3D11_VIDEO_READER_ERROR_INVALID_PARAMETER;
    }

    try {
        auto wrapper = GetWrapper(handle);
        if (!wrapper || !wrapper->reader) {
            return D3D11_VIDEO_READER_ERROR_INVALID_PARAMETER;
        }

        bool result = wrapper->reader->Seek(timeInSeconds);
        return result ? D3D11_VIDEO_READER_SUCCESS : D3D11_VIDEO_READER_ERROR_SEEK_FAILED;
    }
    catch (const std::exception& e) {
        std::cerr << "D3D11VideoReader_Seek error: " << e.what() << std::endl;
        return D3D11_VIDEO_READER_ERROR_SEEK_FAILED;
    }
    catch (...) {
        return D3D11_VIDEO_READER_ERROR_UNKNOWN;
    }
}

// ============================================================================
// Frame Reading Implementation
// ============================================================================

int D3D11VideoReader_ReadFrame(
    D3D11VideoReaderHandle handle,
    ID3D11Texture2D* texture,
    double* outTimestamp) {
    
    if (!handle || !texture) {
        return D3D11_VIDEO_READER_ERROR_INVALID_PARAMETER;
    }

    try {
        auto wrapper = GetWrapper(handle);
        if (!wrapper || !wrapper->reader) {
            return D3D11_VIDEO_READER_ERROR_INVALID_PARAMETER;
        }

        double timestamp = wrapper->reader->ReadFrame(texture);
        
        if (timestamp < 0.0) {
            return D3D11_VIDEO_READER_ERROR_READ_FAILED;
        }

        if (outTimestamp) {
            *outTimestamp = timestamp;
        }

        return D3D11_VIDEO_READER_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "D3D11VideoReader_ReadFrame error: " << e.what() << std::endl;
        return D3D11_VIDEO_READER_ERROR_READ_FAILED;
    }
    catch (...) {
        return D3D11_VIDEO_READER_ERROR_UNKNOWN;
    }
}

int D3D11VideoReader_ReadAlphaFrame(
    D3D11VideoReaderHandle handle,
    ID3D11Texture2D* texture,
    double* outTimestamp) {
    
    if (!handle || !texture) {
        return D3D11_VIDEO_READER_ERROR_INVALID_PARAMETER;
    }

    try {
        auto wrapper = GetWrapper(handle);
        if (!wrapper || !wrapper->reader) {
            return D3D11_VIDEO_READER_ERROR_INVALID_PARAMETER;
        }

        double timestamp = wrapper->reader->ReadAlphaFrame(texture);
        
        if (timestamp < 0.0) {
            return D3D11_VIDEO_READER_ERROR_READ_FAILED;
        }

        if (outTimestamp) {
            *outTimestamp = timestamp;
        }

        return D3D11_VIDEO_READER_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "D3D11VideoReader_ReadAlphaFrame error: " << e.what() << std::endl;
        return D3D11_VIDEO_READER_ERROR_READ_FAILED;
    }
    catch (...) {
        return D3D11_VIDEO_READER_ERROR_UNKNOWN;
    }
}

int D3D11VideoReader_ReadTransparentFrame(
    D3D11VideoReaderHandle handle,
    ID3D11Texture2D* texture,
    double* outTimestamp) {
    
    if (!handle || !texture) {
        return D3D11_VIDEO_READER_ERROR_INVALID_PARAMETER;
    }

    try {
        auto wrapper = GetWrapper(handle);
        if (!wrapper || !wrapper->reader) {
            return D3D11_VIDEO_READER_ERROR_INVALID_PARAMETER;
        }

        double timestamp = wrapper->reader->ReadTransparentFrame(texture);
        
        if (timestamp < 0.0) {
            return D3D11_VIDEO_READER_ERROR_READ_FAILED;
        }

        if (outTimestamp) {
            *outTimestamp = timestamp;
        }

        return D3D11_VIDEO_READER_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "D3D11VideoReader_ReadTransparentFrame error: " << e.what() << std::endl;
        return D3D11_VIDEO_READER_ERROR_READ_FAILED;
    }
    catch (...) {
        return D3D11_VIDEO_READER_ERROR_UNKNOWN;
    }
}
