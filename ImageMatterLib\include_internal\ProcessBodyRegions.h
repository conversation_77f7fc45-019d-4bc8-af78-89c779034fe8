#pragma once

#include <cuda.h>
#include <memory>
#include <vector>
#include "Helpers_Heads.h"
#include "ImageMattingFactory.h"

// Forward declarations
class IImageMatter;

class ProcessBodyRegions {
public:
	ProcessBodyRegions();
	virtual ~ProcessBodyRegions();

	// Initialize the processor with video dimensions and CUDA stream
	bool Initialize(int width, int height, CUstream stream);

	// Takes device buffers, returns processed alpha
	// d_rgbBuffer: input RGB data (planar float format) on device
	// d_Alpha: input/output alpha buffer on device (will be modified in-place)
	bool ProcessFrame(CUdeviceptr d_rgbBuffer, CUdeviceptr d_Alpha);

	// Set head detection results for region filtering
	void SetHeadRegions(const std::vector<Box>& headBoxes);

	// Cleanup resources
	void Cleanup();

private:
	// Internal processing methods
	bool AllocateBuffers();
	void DeallocateBuffers();

	// Video properties
	int videoWidth;
	int videoHeight;
	int regionSizeBody;
	int NumVerticalRegionCountBody;

	// Processing parameters
	static const int IndexNetModelInputSize = 320;
	static const int MaxUncertainRegionWidthForBody = 5;

	// CUDA resources
	CUstream processingStream;

	std::unique_ptr<ImageMatting> m_indexNetImageMatter;

	// Device buffers
	CUdeviceptr d_regionsBufferBody;
	CUdeviceptr d_trimapBuffer;
	CUdeviceptr d_headBoxes;
	CUtexObject d_originalAlphaTexture;
	CUarray d_alphaArray;

	// Host buffers
	unsigned char* h_regionsBufferBody;

	// Buffer sizes
	size_t regionsBufferSizeBody;

	// Input data pointers (set during ProcessFrame)
	CUdeviceptr d_inputRgbBuffer;
	CUdeviceptr d_inputAlphaBuffer;

	// Head detection results
	std::vector<Box> headDetectionResults;
	static const int MaxHeadCount = 50;
};