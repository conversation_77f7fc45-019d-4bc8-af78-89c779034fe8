#pragma once

#include <vector>
#include "HeadDetector.h"
#include "Helpers_Kernels.cuh"

bool DrawHeadDetectionDebugOverlay(float* d_rgbBuffer, int width, int height,
    const std::vector<Box>& results);

bool LaunchDrawRectangleKernel(float* image, int width, int height,
    int x1, int y1, int x2, int y2,
    float r, float g, float b,
    int thickness,
    CUstream stream = nullptr);

bool LaunchDrawNumberKernel(float* image, int width, int height,
    int x, int y, int number,
    float r, float g, float b,
    CUstream stream = nullptr);