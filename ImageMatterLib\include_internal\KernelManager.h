// KernelManager.h - CUDA Driver API Kernel Module Loading System
#pragma once

#include <cuda.h>
#include <string>
#include <unordered_map>
#include <memory>

class KernelManager {
public:
    // Singleton pattern for global kernel management
    static KernelManager& GetInstance();
    
    // Initialize the kernel manager with a module path
    bool Initialize(const std::string& modulePath);
    
    // Initialize the kernel manager with embedded PTX data
    bool InitializeFromPTX(const char* ptxData);
    
    // Load a specific kernel function by name
    CUfunction GetKernel(const std::string& kernelName);
    
    // Check if a kernel is loaded
    bool IsKernelLoaded(const std::string& kernelName) const;
    
    // Cleanup resources
    void Cleanup();
    
    // Get the loaded module handle
    CUmodule GetModule() const { return m_module; }

private:
    KernelManager() = default;
    ~KernelManager();
    
    // Disable copy constructor and assignment operator
    KernelManager(const KernelManager&) = delete;
    KernelManager& operator=(const KernelManager&) = delete;
    
    CUmodule m_module = nullptr;
    std::unordered_map<std::string, CUfunction> m_kernelCache;
    bool m_initialized = false;
    
    // Helper function for error checking
    void CheckCudaError(CUresult error, const std::string& operation);
};

// Global helper functions for kernel launching
namespace KernelHelpers {
    // Initialize kernel manager with embedded PTX or load from file
    bool InitializeKernels();
    
    // Get a kernel function safely
    CUfunction GetKernelFunction(const std::string& kernelName);
    
    // Launch a kernel with error checking
    CUresult LaunchKernel(CUfunction kernel, 
                         unsigned int gridX, unsigned int gridY, unsigned int gridZ,
                         unsigned int blockX, unsigned int blockY, unsigned int blockZ,
                         unsigned int sharedMemBytes, CUstream stream,
                         void** kernelParams);
    
    // Launch a kernel with automatic error checking and throwing
    void LaunchKernelChecked(const std::string& kernelName,
                           unsigned int gridX, unsigned int gridY, unsigned int gridZ,
                           unsigned int blockX, unsigned int blockY, unsigned int blockZ,
                           unsigned int sharedMemBytes, CUstream stream,
                           void** kernelParams);
}
