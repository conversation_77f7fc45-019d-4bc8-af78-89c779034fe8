#pragma once

#include <cuda.h>
#define NOMINMAX // Disable the min/max macros from windows.h which interfere with std::min/max

struct Box {
    float x;  // Center x coordinate
    float y;  // Center y coordinate
    float width;  // Width
    float height;  // Height
    float confidence;  // Detection confidence
};

#ifndef __CUDACC__

#include <wrl/client.h>
#include <vector>
#include <memory>
#include <onnxruntime_cxx_api.h>
using Microsoft::WRL::ComPtr;

class HeadDetector {
public:
    HeadDetector();
    ~HeadDetector();

    bool Initialize(
        int imageWidth,
        int imageHeight,
        CUstream externalStream = nullptr);
    bool DetectHeads(float* inputBuffer, int width, int height, std::vector<Box>& results, float confidenceThreshold);
    void Release();

private:
    // ONNX Runtime members
    std::unique_ptr<Ort::Env> m_env;
    std::unique_ptr<Ort::Session> m_session;
    std::unique_ptr<Ort::MemoryInfo> m_memory_info_cuda;

    std::string m_modelInputName;
    std::vector<std::string> m_modelOutputNames;

    int m_modelInputWidth;
    int m_modelInputHeight;
    int m_imageWidth;
    int m_imageHeight;
    size_t m_inputBufferSize;

    // Input processing buffers (still pre-allocated to avoid CPU copies)
    float* m_preprocessedBuffer;
    float* m_deviceResizedInputBuffer;

    // Input tensor (pre-allocated on GPU)
    std::unique_ptr<Ort::Value> m_inputTensor;

    // REMOVED: Output buffers and tensors - ONNX will manage these
    // REMOVED: IoBinding - using regular Run() method

    // cudaExternalMemory_t m_inputExternalMemory; // Removed for Driver API compatibility
    bool m_initialized;

    CUstream m_cudaStream;          // CUDA stream for operations


    // Helper functions
    Ort::Value CreateOrtValueFromDeviceMemory(void* deviceBuffer, const std::vector<int64_t>& shape,
        ONNXTensorElementDataType dataType, const Ort::MemoryInfo& memoryInfo);
};

#endif
