#include "ProcessBodyRegions.h"
#include "main_Kernels.cuh"
#include "Helpers.h"
#include "ImageMattingFactory.h"
#include <iostream>
#include <algorithm>
#include <cuda.h>

ProcessBodyRegions::ProcessBodyRegions()
	: videoWidth(0), videoHeight(0), regionSizeBody(0), NumVerticalRegionCountBody(0), processingStream(nullptr), d_regionsBufferBody(0), d_trimapBuffer(0), d_headBoxes(0), d_originalAlphaTexture(0), d_alphaArray(nullptr), h_regionsBufferBody(nullptr), regionsBufferSizeBody(0), d_inputRgbBuffer(0), d_inputAlphaBuffer(0), headDetectionResults(MaxHeadCount) {
}

ProcessBodyRegions::~ProcessBodyRegions() {
	Cleanup();
}

bool ProcessBodyRegions::Initialize(int width, int height, CUstream stream) {
	videoWidth = width;
	videoHeight = height;
	processingStream = stream;

	// Calculate region size for body processing
	regionSizeBody = IndexNetModelInputSize - 2 * MaxUncertainRegionWidthForBody;

	// Calculate number of vertical regions
	NumVerticalRegionCountBody = (videoHeight + regionSizeBody - 1) / regionSizeBody;

	    // Initialize IndexNet for body region refinement
	m_indexNetImageMatter = ImageMattingFactory::Init(
		ModelType::INDEXNET,
		IndexNetModelInputSize,
		IndexNetModelInputSize,
		processingStream,
		InferenceBackend::AUTO,
		ImageMattingFactory::BestModelSelectionMethod::ASPECT_RATIO_CLOSEST_FIT,
		true);

	if (!m_indexNetImageMatter) {
		std::cerr << "Failed to initialize IndexNet model for body region processing" << std::endl;
		return false;
	}
	// Allocate buffers
	if (!AllocateBuffers()) {
		std::cerr << "Failed to allocate buffers for ProcessBodyRegions" << std::endl;
		return false;
	}

	return true;
}

bool ProcessBodyRegions::ProcessFrame(CUdeviceptr d_rgbBuffer, CUdeviceptr d_Alpha) {
	if (!d_rgbBuffer || !d_Alpha) {
		std::cerr << "Invalid input buffers for ProcessBodyRegions" << std::endl;
		return false;
	}

	// Store input buffer pointers
	d_inputRgbBuffer = d_rgbBuffer;
	d_inputAlphaBuffer = d_Alpha;

	// Copy alpha buffer to texture for processing
	CUDA_MEMCPY2D memcpy2D = { 0 };
	memcpy2D.srcMemoryType = CU_MEMORYTYPE_DEVICE;
	memcpy2D.srcDevice = d_Alpha;
	memcpy2D.srcPitch = videoWidth * sizeof(float);
	memcpy2D.dstMemoryType = CU_MEMORYTYPE_ARRAY;
	memcpy2D.dstArray = d_alphaArray;
	memcpy2D.WidthInBytes = videoWidth * sizeof(float);
	memcpy2D.Height = videoHeight;
	DRIVER_API_CHECK(cuMemcpy2D(&memcpy2D));

	// Generate trimap from alpha
	launchGenerateTrimap(static_cast<float*>(reinterpret_cast<void*>(d_trimapBuffer)), static_cast<const float*>(reinterpret_cast<void*>(d_Alpha)), videoWidth, videoHeight, processingStream);
	DRIVER_API_CHECK(cuStreamSynchronize(processingStream));

	//SaveAlphaMatteToPNG("Videos\\trimap.png", d_trimapBuffer, videoWidth, videoHeight);

	// Copy head boxes to device if we have any
	if (!headDetectionResults.empty()) {
		DRIVER_API_CHECK(cuMemcpyHtoD(d_headBoxes, headDetectionResults.data(),
			headDetectionResults.size() * sizeof(Box)));
	}

	// Process body regions
	// Initialize the regions buffer to ensure clean state
	DRIVER_API_CHECK(cuMemsetD8Async(d_regionsBufferBody, 0, regionsBufferSizeBody, processingStream));
	DRIVER_API_CHECK(cuStreamSynchronize(processingStream));

	//SaveAlphaMatteToPNG("Videos\\input_alpha_buffer.png", d_inputAlphaBuffer, videoWidth, videoHeight);

	// Detect alpha border pixels in vertical sections for body
	launchDetectUncertainRegions(
		static_cast<float*>(reinterpret_cast<void*>(d_inputAlphaBuffer)),
		static_cast<unsigned char*>(reinterpret_cast<void*>(d_regionsBufferBody)),
		videoWidth,
		videoHeight,
		regionSizeBody,
		static_cast<const Box*>(reinterpret_cast<void*>(d_headBoxes)),
		static_cast<int>(headDetectionResults.size()),
		processingStream);
	DRIVER_API_CHECK(cuStreamSynchronize(processingStream));

	// Copy regions buffer from device to host
	DRIVER_API_CHECK(cuMemcpyDtoH(h_regionsBufferBody, d_regionsBufferBody, regionsBufferSizeBody));

    //SaveAlphaMatteToPNG("Videos\\alpha_regions_body.png", h_regionsBufferBody, videoWidth, NumVerticalRegionCountBody);

	// Process regular regions (non-head regions)
	for (int yRegion = 0; yRegion < NumVerticalRegionCountBody; yRegion++) {
		for (int x = 0; x < videoWidth; x++) {
			if (h_regionsBufferBody[yRegion * videoWidth + x]) {

			 	//SavePlanarFloatImageToPNG("Videos\\temp00.png", d_inputRgbBuffer, videoWidth, videoHeight, false, processingStream);
				//SaveAlphaMatteToPNG("Videos\\trimap.png", d_trimapBuffer, videoWidth, videoHeight);

				// Prepare input for IndexNet inference using trimap
				launchPrepareIndexNetInput(
					static_cast<float*>(reinterpret_cast<void*>(m_indexNetImageMatter->GetInputBuffer())),
					static_cast<const float*>(reinterpret_cast<void*>(d_inputRgbBuffer)),
					static_cast<const float*>(reinterpret_cast<void*>(d_trimapBuffer)),
					x, yRegion * regionSizeBody,
					videoWidth, videoHeight,
					IndexNetModelInputSize, IndexNetModelInputSize,
					MaxUncertainRegionWidthForBody,
					processingStream);
				DRIVER_API_CHECK(cuStreamSynchronize(processingStream));

				// SavePlanarFloatImageToPNG("Videos\\temp0.png", m_indexNetImageMatter->GetInputBuffer(), IndexNetModelInputSize, IndexNetModelInputSize, true, processingStream);

				// IndexNet inference
				if (!m_indexNetImageMatter->Infer()) {
					std::cerr << "Failed to process IndexNet matting at region " << x << "," << yRegion << std::endl;
					continue;
				}

				//SaveAlphaMatteToPNG("Videos\\temp1.png", m_indexNetImageMatter->GetOutputBuffer(), IndexNetModelInputSize, IndexNetModelInputSize);
				 
				// Update alpha buffer region
				launchUpdateAlphaBufferRegion(
					static_cast<float*>(reinterpret_cast<void*>(d_inputAlphaBuffer)),
					d_originalAlphaTexture,
					static_cast<const float*>(reinterpret_cast<void*>(d_trimapBuffer)), // Use trimap for blending guidance
					static_cast<const float*>(reinterpret_cast<void*>(m_indexNetImageMatter->GetOutputBuffer())),
					x, yRegion * regionSizeBody,
					videoWidth, videoHeight,
					IndexNetModelInputSize,
					MaxUncertainRegionWidthForBody,
					processingStream);
				DRIVER_API_CHECK(cuStreamSynchronize(processingStream));

				//SaveAlphaMatteToPNG("Videos\\Refined_Image.png", d_inputAlphaBuffer, videoWidth, videoHeight);

				// Skip processed region
				x += IndexNetModelInputSize - 2 * MaxUncertainRegionWidthForBody;
			}
		}
	}
	return true;
}

void ProcessBodyRegions::SetHeadRegions(const std::vector<Box>& headBoxes) {
	headDetectionResults = headBoxes;
	// Resize to MaxHeadCount if needed
	if (headDetectionResults.size() > MaxHeadCount) {
		headDetectionResults.resize(MaxHeadCount);
	}
}

bool ProcessBodyRegions::AllocateBuffers() {
	// Calculate buffer sizes
	regionsBufferSizeBody = NumVerticalRegionCountBody * videoWidth * sizeof(unsigned char);
	size_t alphaBufferSize = videoWidth * videoHeight * sizeof(float);

	// Allocate device buffers
	DRIVER_API_CHECK(cuMemAlloc(&d_regionsBufferBody, regionsBufferSizeBody));
	DRIVER_API_CHECK(cuMemAlloc(&d_trimapBuffer, alphaBufferSize));
	DRIVER_API_CHECK(cuMemAlloc(&d_headBoxes, MaxHeadCount * sizeof(Box)));

	// Allocate host buffers
	h_regionsBufferBody = new unsigned char[NumVerticalRegionCountBody * videoWidth];

	// Create alpha texture
	CUDA_ARRAY_DESCRIPTOR ad;
	ad.Width = videoWidth;
	ad.Height = videoHeight;
	ad.Format = CU_AD_FORMAT_FLOAT;
	ad.NumChannels = 1;
	DRIVER_API_CHECK(cuArrayCreate(&d_alphaArray, &ad));

	CUDA_RESOURCE_DESC resDesc = {};
	resDesc.resType = CU_RESOURCE_TYPE_ARRAY;
	resDesc.res.array.hArray = d_alphaArray;

	CUDA_TEXTURE_DESC texDesc = {};
	texDesc.addressMode[0] = CU_TR_ADDRESS_MODE_CLAMP;
	texDesc.addressMode[1] = CU_TR_ADDRESS_MODE_CLAMP;
	texDesc.filterMode = CU_TR_FILTER_MODE_LINEAR;
	texDesc.flags = CU_TRSF_READ_AS_INTEGER;

	DRIVER_API_CHECK(cuTexObjectCreate(&d_originalAlphaTexture, &resDesc, &texDesc, nullptr));

	return true;
}

void ProcessBodyRegions::DeallocateBuffers() {
	if (d_regionsBufferBody) {
		cuMemFree(d_regionsBufferBody);
		d_regionsBufferBody = 0;
	}
	if (d_trimapBuffer) {
		cuMemFree(d_trimapBuffer);
		d_trimapBuffer = 0;
	}
	if (d_headBoxes) {
		cuMemFree(d_headBoxes);
		d_headBoxes = 0;
	}
	if (d_originalAlphaTexture) {
		cuTexObjectDestroy(d_originalAlphaTexture);
		d_originalAlphaTexture = 0;
	}
	if (d_alphaArray) {
		cuArrayDestroy(d_alphaArray);
		d_alphaArray = nullptr;
	}

	// Free host memory
	if (h_regionsBufferBody) {
		delete[] h_regionsBufferBody;
		h_regionsBufferBody = nullptr;
	}
}

void ProcessBodyRegions::Cleanup() {
	DeallocateBuffers();
}