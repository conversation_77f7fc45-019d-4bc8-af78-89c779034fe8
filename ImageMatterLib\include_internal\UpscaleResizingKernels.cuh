#ifndef UPSCALE_KERNELS_H
#define UPSCALE_KERNELS_H

#include <cuda.h>

#ifdef __cplusplus
extern "C" {
#endif

    // Bicubic upscaling kernel functions (smooth, natural results)
    CUresult LaunchBicubicUpscaleKernel(float* output, int outputWidth, int outputHeight,
        const float* input, int inputWidth, int inputHeight,
        int channels);

    CUresult BicubicUpscaleKernelLauncher(float* output, int outputWidth, int outputHeight,
        const float* input, int inputWidth, int inputHeight,
        int channels, CUstream stream);

    // Edge-directed upscaling kernel functions (preserves edges, reduces artifacts)
    CUresult LaunchEdgeDirectedUpscaleKernel(float* output, int outputWidth, int outputHeight,
        const float* input, int inputWidth, int inputHeight,
        int channels);

    CUresult EdgeDirectedUpscaleKernelLauncher(float* output, int outputWidth, int outputHeight,
        const float* input, int inputWidth, int inputHeight,
        int channels, CUstream stream);

    // Lanczos upscaling kernel functions (sharp, detailed results)
    CUresult LaunchLanczosUpscaleKernel(float* output, int outputWidth, int outputHeight,
        const float* input, int inputWidth, int inputHeight,
        int channels);

    CUresult LanczosUpscaleKernelLauncher(float* output, int outputWidth, int outputHeight,
        const float* input, int inputWidth, int inputHeight,
        int channels, CUstream stream);

#ifdef __cplusplus
}
#endif

#endif // UPSCALE_KERNELS_H
