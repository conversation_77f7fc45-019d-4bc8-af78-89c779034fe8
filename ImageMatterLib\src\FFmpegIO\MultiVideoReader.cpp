// MultiVideoReader.cpp
#include "FFmpegIO/MultiVideoReader.h"
#include "FFmpegIO/FFmpegIOKernels.cuh"
#include "Helpers.h"
#include <iostream>
#include <filesystem>

// Helper function for CUDA error handling
static void CheckCudaError(CUresult error) {
    if (error != CUDA_SUCCESS) {
        const char* errorStr;
        cuGetErrorString(error, &errorStr);
        throw std::runtime_error(std::string("CUDA error: ") + errorStr);
    }
}

MultiVideoReader::MultiVideoReader()
    : m_cudaContext(nullptr),
      m_cudaStream(nullptr),
      m_tempRgbNv12Buffer(0),
      m_tempAlphaNv12Buffer(0),
      m_tempRgbNv12BufferSize(0),
      m_tempAlphaNv12BufferSize(0),
      m_width(0),
      m_height(0),
      m_duration(0.0),
      m_frameRate({ 0, 1 }),
      m_timeBase({ 0, 1 }),
      m_textureFormat(Helpers::TextureFormat::Unknown),
      m_isInitialized(false) {
}

MultiVideoReader::~MultiVideoReader() {
    Close();
}

std::shared_ptr<MultiVideoReader> MultiVideoReader::Create(
    const std::string& filePath,
    CUcontext cudaContext) {

    std::shared_ptr<MultiVideoReader> reader(new MultiVideoReader());

    if (!reader->Initialize(filePath, cudaContext)) {
        return nullptr;
    }

    return reader;
}

void MultiVideoReader::DeriveRgbAlphaPaths(const std::string& basePath,
                                           std::string& rgbPath,
                                           std::string& alphaPath) {
    std::filesystem::path p(basePath);
    std::filesystem::path dir = p.parent_path();
    std::string stem = p.stem().string();
    std::string ext = p.extension().string();

    // Create paths for "video rgb.mp4" and "video alpha.mp4"
    rgbPath = (dir / (stem + " rgb" + ext)).string();
    alphaPath = (dir / (stem + " alpha" + ext)).string();
}

bool MultiVideoReader::Initialize(const std::string& filePath, CUcontext cudaContext) {
    try {
        m_cudaContext = cudaContext;

        // Derive RGB and Alpha file paths
        std::string rgbPath, alphaPath;
        DeriveRgbAlphaPaths(filePath, rgbPath, alphaPath);

        std::cout << "MultiVideoReader: Opening RGB video: " << rgbPath << std::endl;
        std::cout << "MultiVideoReader: Opening Alpha video: " << alphaPath << std::endl;

        // Create readers for both videos
        m_rgbReader = NV12DirectVideoReader::Create(rgbPath, cudaContext);
        m_alphaReader = NV12DirectVideoReader::Create(alphaPath, cudaContext);

        if (!m_rgbReader || !m_alphaReader) {
            std::cerr << "Failed to open RGB or Alpha video files" << std::endl;
            return false;
        }

        // Get video properties from RGB reader
        m_width = m_rgbReader->GetWidth();
        m_height = m_rgbReader->GetHeight();
        m_duration = m_rgbReader->GetDuration();
        m_frameRate = m_rgbReader->GetFrameRate();
        m_timeBase = m_rgbReader->GetTimeBase();
        m_textureFormat = m_rgbReader->GetTextureFormat();

        // Validate that both videos have the same dimensions
        if (m_alphaReader->GetWidth() != m_width ||
            m_alphaReader->GetHeight() != m_height) {
            std::cerr << "Error: RGB and Alpha videos have different dimensions" << std::endl;
            std::cerr << "RGB: " << m_width << "x" << m_height << std::endl;
            std::cerr << "Alpha: " << m_alphaReader->GetWidth() << "x" << m_alphaReader->GetHeight() << std::endl;
            return false;
        }

        // Validate that both videos have the same duration (with small tolerance)
        double durDiff = std::abs(m_alphaReader->GetDuration() - m_duration);
        if (durDiff > 0.1) { // 100ms tolerance
            std::cerr << "Warning: RGB and Alpha videos have different durations" << std::endl;
            std::cerr << "RGB duration: " << m_duration << " seconds" << std::endl;
            std::cerr << "Alpha duration: " << m_alphaReader->GetDuration() << " seconds" << std::endl;
        }

        // Create CUDA stream for operations
        CheckCudaError(cuStreamCreate(&m_cudaStream, CU_STREAM_NON_BLOCKING));

        // Allocate temporary NV12 buffers for RGB and Alpha data
        m_tempRgbNv12BufferSize = m_width * m_height * 3 / 2;   // NV12 is 1.5 bytes per pixel
        m_tempAlphaNv12BufferSize = m_width * m_height * 3 / 2; // NV12 is 1.5 bytes per pixel

        CheckCudaError(cuMemAlloc(&m_tempRgbNv12Buffer, m_tempRgbNv12BufferSize));
        CheckCudaError(cuMemAlloc(&m_tempAlphaNv12Buffer, m_tempAlphaNv12BufferSize));

        m_isInitialized = true;
        std::cout << "MultiVideoReader initialized successfully: " << m_width << "x" << m_height
                  << ", duration: " << m_duration << "s" << std::endl;
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Error initializing MultiVideoReader: " << e.what() << std::endl;
        Close();
        return false;
    }
}

bool MultiVideoReader::Seek(double timeInSeconds) {
    if (!m_isInitialized)
        return false;

    // Seek both readers to the same timestamp
    bool rgbResult = m_rgbReader->Seek(timeInSeconds);
    bool alphaResult = m_alphaReader->Seek(timeInSeconds);

    if (!rgbResult || !alphaResult) {
        std::cerr << "MultiVideoReader: Seek failed for "
                  << (rgbResult ? "Alpha" : "RGB") << " reader" << std::endl;
        return false;
    }

    return true;
}

double MultiVideoReader::ReadTransparentFrame(CUdeviceptr cudaBgraBuffer, size_t bufferSize, CUstream stream) {
    if (!m_isInitialized || !cudaBgraBuffer)
        return -1.0;

    // Check buffer size
    size_t expectedSize = m_width * m_height * 4; // 4 bytes per pixel for BGRA
    if (bufferSize < expectedSize) {
        std::cerr << "Output buffer too small for BGRA data. Expected: " << expectedSize
                  << ", Got: " << bufferSize << std::endl;
        return -1.0;
    }

    // Read RGB frame into temporary NV12 buffer
    size_t rgbNv12Pitch;
    double rgbTimestamp = m_rgbReader->ReadFrame(m_tempRgbNv12Buffer, m_tempRgbNv12BufferSize, &rgbNv12Pitch, 0);
    if (rgbTimestamp < 0.0) {
        std::cerr << "Failed to read RGB frame" << std::endl;
        return -1.0;
    }

    // Read Alpha frame into temporary NV12 buffer
    size_t alphaNv12Pitch;
    double alphaTimestamp = m_alphaReader->ReadFrame(m_tempAlphaNv12Buffer, m_tempAlphaNv12BufferSize, &alphaNv12Pitch, 0);
    if (alphaTimestamp < 0.0) {
        std::cerr << "Failed to read Alpha frame" << std::endl;
        return -1.0;
    }

    // Optional: Check if timestamps are reasonably close
    double tsDiff = std::abs(rgbTimestamp - alphaTimestamp);
    if (tsDiff > 0.1) { // More than 100ms difference
        std::cerr << "Warning: RGB and Alpha frame timestamps differ by " << tsDiff
                  << " seconds (RGB: " << rgbTimestamp << ", Alpha: " << alphaTimestamp << ")" << std::endl;
    }

    // Combine RGB NV12 and Alpha NV12 into BGRA using CUDA kernel
    launchCombineNv12ToBgra(
        reinterpret_cast<unsigned char*>(m_tempRgbNv12Buffer),
        reinterpret_cast<unsigned char*>(m_tempAlphaNv12Buffer),
        reinterpret_cast<unsigned char*>(cudaBgraBuffer),
        m_width, m_height,
        rgbNv12Pitch, // Assuming both have the same pitch
        m_cudaStream);

    // Synchronize with the provided stream (as per convention)
    if (stream) {
        CheckCudaError(cuStreamSynchronize(stream));
    } else {
        CheckCudaError(cuStreamSynchronize(m_cudaStream));
    }

    // Return the RGB timestamp (they should be the same)
    return rgbTimestamp;
}

Helpers::TextureFormat MultiVideoReader::GetVideoStreamFormat() const {
    if (!m_isInitialized)
        return Helpers::TextureFormat::Unknown;
    return m_rgbReader->GetVideoStreamFormat();
}

void MultiVideoReader::Close() {
    // Release CUDA resources
    if (m_tempRgbNv12Buffer) {
        cuMemFree(m_tempRgbNv12Buffer);
        m_tempRgbNv12Buffer = 0;
        m_tempRgbNv12BufferSize = 0;
    }

    if (m_tempAlphaNv12Buffer) {
        cuMemFree(m_tempAlphaNv12Buffer);
        m_tempAlphaNv12Buffer = 0;
        m_tempAlphaNv12BufferSize = 0;
    }

    if (m_cudaStream) {
        cuStreamDestroy(m_cudaStream);
        m_cudaStream = nullptr;
    }

    // Close video readers
    if (m_rgbReader) {
        m_rgbReader->Close();
        m_rgbReader.reset();
    }

    if (m_alphaReader) {
        m_alphaReader->Close();
        m_alphaReader.reset();
    }

    // Reset properties
    m_width = 0;
    m_height = 0;
    m_duration = 0.0;
    m_frameRate = { 0, 1 };
    m_timeBase = { 0, 1 };
    m_textureFormat = Helpers::TextureFormat::Unknown;
    m_isInitialized = false;
}
