// DirectVideoWriter.h
#pragma once

#include <string>
#include <iostream>
#include <memory>
#include <map>
#include <cuda.h>
// Removed cuda_runtime.h as we're using CUDA Driver API
#include "../../ImageMatterLib/include_internal/Helpers.h"

// For AVRational
extern "C" {
#include <libavutil/rational.h>
}

// For hardware frame handling
extern "C" {
#include <libavutil/hwcontext.h>
#include <libavutil/hwcontext_cuda.h>
}

// Forward declarations of FFmpeg structures
struct AVFormatContext;
struct AVCodecContext;
struct AVStream;
struct AVFrame;
struct AVPacket;
struct AVRational;

class DirectVideoWriter {
public:
    // Output configuration struct with essential settings
    struct OutputConfig {
    private:
        std::map<std::string, std::string> encoderOptions; // Encoder options
    public:
        int width = 1920;                   // Default width (Full HD)
        int height = 1080;                  // Default height (Full HD)
        AVRational frameRate = { 30, 1 };      // Default frame rate
        int bitRate = 5000000;               // Default bit rate (5 Mbps)
        int gopSize = 30;                    // Default GOP size
        std::string encoder = "h264_nvenc";  // Default encoder (NVENC H.264)

        // Helper methods to set options
        void SetPreset(const std::string& preset) { encoderOptions["preset"] = preset; }
        void SetQuality(int crf) { encoderOptions["crf"] = std::to_string(crf); }
        void SetProfile(const std::string& profile) { encoderOptions["profile:v"] = profile; }
        void SetTune(const std::string& tune) { encoderOptions["tune"] = tune; }
        void AddOption(const std::string& key, const std::string& value) { encoderOptions[key] = value; }

        std::string GetPreset() const {
            auto it = encoderOptions.find("preset");
            return (it != encoderOptions.end()) ? it->second : "default";
        }

        int GetQuality() const {
            auto it = encoderOptions.find("crf");
            if (it != encoderOptions.end()) {
                try {
                    return std::stoi(it->second);
                }
                catch (const std::exception&) {
                    std::cerr << "Error: 'crf' value is not a valid integer.\n";
                }
            }
            return -1; // Default error value
        }

        std::string GetProfile() const {
            auto it = encoderOptions.find("profile:v");
            return (it != encoderOptions.end()) ? it->second : "baseline";
        }

        std::string GetTune() const {
            auto it = encoderOptions.find("tune");
            return (it != encoderOptions.end()) ? it->second : "none";
        }

        bool IsPresetSet() const { return encoderOptions.find("preset") != encoderOptions.end(); }
        bool IsQualitySet() const { return encoderOptions.find("crf") != encoderOptions.end(); }
        bool IsProfileSet() const { return encoderOptions.find("profile:v") != encoderOptions.end(); }
        bool IsTuneSet() const { return encoderOptions.find("tune") != encoderOptions.end(); }

        // Print all currently set encoder options
        void PrintOptions() const {
            std::cout << "Encoder Options:\n";
            for (const auto& [key, value] : encoderOptions) {
                std::cout << "  " << key << " = " << value << "\n";
            }
        }
    };


    // Factory method to create a DirectVideoWriter instance using a provided CUDA context
    static std::unique_ptr<DirectVideoWriter> Create(const std::wstring& videoPath, OutputConfig& config, CUcontext cudaContext);

    // Destructor
    ~DirectVideoWriter();

    // Write a frame from CUDA memory asynchronously
    bool WriteFrame(void* cudaBuffer, size_t bufferSize, size_t bufferPitch);

    // Finalize the video file (waits for all pending operations to complete)
    bool Finalize();

    // Close and release resources
    void Close();

protected:
    DirectVideoWriter();
    bool Initialize(const std::wstring& videoPath, OutputConfig& config, CUcontext cudaContext);
    bool PrepareFrame();
    bool TransferCudaToFrame(void* cudaBuffer, size_t bufferSize, size_t bufferPitch);
    bool EncodeFrame();

private:
    // FFmpeg context
    AVFormatContext* m_formatContext = nullptr;
    AVCodecContext* m_codecContext = nullptr;
    AVFrame* m_hwFrame = nullptr; // The hardware frame living on the GPU
    AVPacket* m_packet = nullptr;
    AVStream* m_videoStream = nullptr;

    // CUDA context and resources
    CUcontext m_cudaContext = nullptr;
    CUstream m_cudaStream = nullptr;  // Stream for memory operations
    CUstream m_encoderStream = nullptr;  // Dedicated stream for NVENC operations

    // Video properties
    int m_width = 0;
    int m_height = 0;
    int m_bitRate = 0;
    AVRational* m_frameRate = nullptr;
    int m_gopSize = 0;
    int m_quality = 0;
    int64_t m_pts = 0;

    bool m_isInitialized = false;
    bool m_isFinalized = false;

    friend class std::unique_ptr<DirectVideoWriter>;
};
