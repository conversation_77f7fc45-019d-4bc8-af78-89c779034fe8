// NV12DirectVideoReader.h
#pragma once

#include <string>
#include <memory>
#include <cuda.h>
#include <vector>

#include "Helpers.h"
#include "FFmpegIO/FFmpegIOKernels.cuh"

// Include AVRational definition
extern "C" {
#include <libavutil/rational.h>
}

// Forward declarations of FFmpeg structures
struct AVFormatContext;
struct AVCodecContext;
struct AVFrame;
struct AVPacket;
struct SwsContext;

class NV12DirectVideoReader {
public:
    // Initialize and open a video file, using an existing CUDA context
    static std::shared_ptr<NV12DirectVideoReader> Create(
        const std::string& filePath,
        CUcontext cudaContext);

    // Destructor - will handle cleanup. Called automatically by std::shared_ptr.
    ~NV12DirectVideoReader();

    // Seek to a specific time position (in seconds)
    bool Seek(double timeInSeconds);

    // Read the next frame into CUDA memory as NV12 format, returns the frame timestamp
    // cudaNv12Buffer: CUDA device pointer to output NV12 data
    // bufferSize: Size of the output buffer (must be at least width*height*1.5 bytes)
    // nv12Pitch: Output parameter for the pitch/stride of the NV12 data
    // Returns the frame timestamp, or -1.0 on error/EOF
    double ReadFrame(CUdeviceptr cudaNv12Buffer, size_t bufferSize, size_t* nv12Pitch, CUstream stream = 0);

    // Read the next frame as alpha data (extracts Y channel from NV12)
    // d_alphaBuffer: CUDA device pointer to output float alpha data [0.0-1.0]
    // Returns the frame timestamp, or -1.0 on error/EOF
    double ReadAlphaFrame(float* d_alphaBuffer, CUstream stream = 0);

    // Get video properties
    int GetWidth() const { return m_width; }
    int GetHeight() const { return m_height; }
    double GetDuration() const { return m_duration; }
    AVRational GetFrameRate() const { return m_frameRate; }
    AVRational GetTimeBase() const { return m_timeBase; }
    Helpers::TextureFormat GetTextureFormat() const { return m_textureFormat; }
    Helpers::TextureFormat GetVideoStreamFormat() const;

    // Check if we've reached the end of the file
    bool IsEof() const { return m_isEof; }

    // Close the video and release resources
    void Close();

private:
    // Private constructor - use Create() to create instances
    NV12DirectVideoReader();

    // Initialize FFmpeg and CUDA resources
    bool Initialize(const std::string& filePath, CUcontext cudaContext);

    // Decode a single packet
    bool DecodePacket();

    // Convert frame timestamp to seconds
    double ConvertTimestampToSeconds(int64_t timestamp) const;

private:
    // FFmpeg state
    AVFormatContext* m_formatContext;
    AVCodecContext* m_codecContext;
    AVFrame* m_hwFrame;
    AVPacket* m_packet;
    int m_videoStreamIndex;
    bool m_isHardwareAccelerated;

    // CUDA state
    CUcontext m_cudaContext;
    CUstream m_cudaStream;

    // Internal NV12 buffer for format conversion
    CUdeviceptr m_internalNv12Buffer;
    size_t m_internalNv12BufferSize;
    size_t m_internalNv12Pitch;

    // YUV420P to NV12 conversion buffer (only used if source is YUV420P)
    CUdeviceptr m_yuv420pConversionBuffer;
    size_t m_yuv420pConversionBufferSize;

    // Video properties
    int m_width;
    int m_height;
    double m_duration;
    AVRational m_frameRate;
    AVRational m_timeBase;
    Helpers::TextureFormat m_textureFormat;
    Helpers::TextureFormat m_sourceFormat;

    // Current frame timestamp
    int64_t m_currentFrameTimestamp;

    // State tracking
    bool m_isInitialized;
    bool m_isEof;
};
