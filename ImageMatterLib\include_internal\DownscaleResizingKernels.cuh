#ifndef RESIZE_KERNELS_H
#define RESIZE_KERNELS_H

#include <cuda.h>

// Define CUDA Driver API types if not already defined
#ifndef CUDA_DRIVER_API_TYPES
#define CUDA_DRIVER_API_TYPES
typedef CUstream_st* CUstream;
typedef unsigned long long CUdeviceptr;
#endif

#ifdef __cplusplus
extern "C" {
#endif

// Lanczos-3 interpolation kernel functions
bool LaunchLanczosResizeDownKernel(float* output, int outputWidth, int outputHeight,
	float* input, int inputWidth, int inputHeight,
	int channels, CUstream stream);

CUresult LanczosResizeKernelLauncher(float* output, int outputWidth, int outputHeight,
	const float* input, int inputWidth, int inputHeight,
	int channels, CUstream stream);

// Mitchell-Netravali interpolation kernel functions
bool LaunchMitchellResizeDownKernel(float* output, int outputWidth, int outputHeight,
	float* input, int inputWidth, int inputHeight,
	int channels, CUstream stream);

bool MitchellResizeKernelLauncher(float* output, int outputWidth, int outputHeight,
	const float* input, int inputWidth, int inputHeight,
	int channels, CUstream stream);

#ifdef __cplusplus
}
#endif

#endif // RESIZE_KERNELS_H
