#include "ImageMattingOnnx.h"
#include "Matting_Kernels.cuh"
#include <vector>
#include <array>
#include <iostream>
#include <filesystem>
#include <regex>
#include <stringapiset.h>
#include <algorithm>
#include <Helpers.h>
#include <DownscaleResizingKernels.cuh>
#include <UpscaleResizingKernels.cuh>
#include <cuda.h>  // Include CUDA Driver API

ImageMattingOnnx::ImageMattingOnnx()
	: m_modelInputWidth(0)
	, m_modelInputHeight(0)
	, m_imageWidth(0)
	, m_imageHeight(0)
	, m_scaleRatioX(1.0f)
	, m_scaleRatioY(1.0f)
	, m_deviceInputBuffer(nullptr)
	, m_deviceOutputBuffer(nullptr)
	, m_deviceResizedInputBuffer(nullptr)
	, m_deviceResizedOutputBuffer(nullptr)
	, m_preprocessedBuffer(nullptr)
	, m_inputBufferSize(0)
	, m_outputBufferSize(0)
	, m_preprocessedBufferSize(0)
	, m_cudaStream(nullptr)
	, m_isRgba(false)
	, m_initialized(false)
	, m_normalizationParams(NormalizationParams::ImageNet())
	, m_resizeMethod(ResizeMethod::EXTEND_SHRINK_LANCZOS)
{
}

ImageMattingOnnx::~ImageMattingOnnx() {
	Shutdown();
}

// Updated Init method that takes explicit model dimensions
bool ImageMattingOnnx::Init(const wchar_t* modelPath, int modelWidth, int modelHeight, int imageWidth, int imageHeight,
	const NormalizationParams& normParams, bool isRgba, ResizeMethod resizeMethod, CUstream externalStream) {
	// Save current CUDA context (from main.cpp) to restore it after ONNX Runtime potentially changes it
	CUcontext savedContext = nullptr;
	CUresult cuResult = cuCtxGetCurrent(&savedContext);
	if (cuResult != CUDA_SUCCESS) {
		std::cerr << "Failed to get current CUDA context in Init(): " << cuResult << std::endl;
	} else {
		std::cout << "ImageMattingOnnx::Init() saving main.cpp CUDA context: " << savedContext << std::endl;
	}

	try {
		if (m_initialized) {
			std::cout << "Already initialized. Shutting down first." << std::endl;
			Shutdown();
		}

		// Store model and image dimensions and format
		m_modelInputWidth = modelWidth;
		m_modelInputHeight = modelHeight;
		m_imageWidth = imageWidth;
		m_imageHeight = imageHeight;
		m_isRgba = isRgba;
		m_normalizationParams = normParams;
		m_resizeMethod = resizeMethod;

		// Calculate scale ratios
		float modelAspectRatio = static_cast<float>(modelWidth) / modelHeight;
		float imageAspectRatio = static_cast<float>(imageWidth) / imageHeight;

		// Check if aspect ratios are compatible
		//const float ASPECT_RATIO_TOLERANCE = 0.05f; // 5% tolerance
		//if (std::abs(modelAspectRatio - imageAspectRatio) > ASPECT_RATIO_TOLERANCE) {
		//	std::cerr << "Error: Image aspect ratio (" << imageAspectRatio << ") does not match model aspect ratio ("
		//		<< modelAspectRatio << "). Cannot initialize." << std::endl;
		//	return false;
		//}

		// Calculate scale ratios
		m_scaleRatioX = static_cast<float>(modelWidth) / imageWidth;
		m_scaleRatioY = static_cast<float>(modelHeight) / imageHeight;

		// Check if resizing will be needed
		bool needsResize = (imageWidth != modelWidth || imageHeight != modelHeight);
		if (needsResize) {
			std::cout << "Resizing will be performed during inference. Image size: " << imageWidth << "x" << imageHeight
				<< ", Model size: " << modelWidth << "x" << modelHeight << std::endl;
		}

		// Use external stream if provided, otherwise use default stream
		m_cudaStream = externalStream;

		// Convert model path to char* for ONNX Runtime
		std::string modelPathStr = ConvertWCharToChar(modelPath);

		// Calculate buffer sizes
		int inputChannels = m_isRgba ? 4 : 3;
		m_inputBufferSize = m_imageWidth * m_imageHeight * inputChannels * sizeof(float);
		m_outputBufferSize = m_imageWidth * m_imageHeight * sizeof(float); // Alpha mask
		m_preprocessedBufferSize = m_imageWidth * m_imageHeight * inputChannels * sizeof(float);

		// Allocate input buffer at original image size for caller to use
		CUdeviceptr devicePtr;
		CUresult cudaStatus = cuMemAlloc(&devicePtr, m_inputBufferSize);
		m_deviceInputBuffer = (float*)devicePtr;
		if (cudaStatus != CUDA_SUCCESS) {
			const char* errorStr;
			cuGetErrorString(cudaStatus, &errorStr);
			std::cerr << "Failed to allocate input buffer: " << errorStr << std::endl;
			return false;
		}

		// Allocate output buffer at original image size for caller to read from
		cudaStatus = cuMemAlloc(&devicePtr, m_outputBufferSize);
		m_deviceOutputBuffer = (float*)devicePtr;
		if (cudaStatus != CUDA_SUCCESS) {
			const char* errorStr;
			cuGetErrorString(cudaStatus, &errorStr);
			std::cerr << "Failed to allocate output buffer: " << errorStr << std::endl;
			return false;
		}

		if (needsResize) {
			// Allocate the preprocessed buffer at original size (only when resizing is needed)
			cudaStatus = cuMemAlloc(&devicePtr, m_inputBufferSize);
			m_preprocessedBuffer = (float*)devicePtr;
			if (cudaStatus != CUDA_SUCCESS) {
				const char* errorStr;
				cuGetErrorString(cudaStatus, &errorStr);
				std::cerr << "Failed to allocate preprocessed buffer: " << errorStr << std::endl;
				return false;
			}

			// Allocate resizing buffers for model input/output
			size_t resizedInputSize = m_modelInputWidth * m_modelInputHeight * inputChannels * sizeof(float);
			cudaStatus = cuMemAlloc(&devicePtr, resizedInputSize);
			m_deviceResizedInputBuffer = (float*)devicePtr;
			if (cudaStatus != CUDA_SUCCESS) {
				const char* errorStr;
				cuGetErrorString(cudaStatus, &errorStr);
				std::cerr << "Failed to allocate resized input buffer: " << errorStr << std::endl;
				return false;
			}

			cudaStatus = cuMemAlloc(&devicePtr, m_modelInputWidth * m_modelInputHeight * sizeof(float));
			m_deviceResizedOutputBuffer = (float*)devicePtr;
			if (cudaStatus != CUDA_SUCCESS) {
				const char* errorStr;
				cuGetErrorString(cudaStatus, &errorStr);
				std::cerr << "Failed to allocate resized output buffer: " << errorStr << std::endl;
				return false;
			}
		} else {
			// When no resizing is needed, use the original buffers directly as model buffers
			m_preprocessedBuffer = nullptr;  // Not needed
			m_deviceResizedInputBuffer = m_deviceInputBuffer;   // Point to original input buffer
			m_deviceResizedOutputBuffer = m_deviceOutputBuffer; // Point to original output buffer
		}

		// Initialize ONNX Runtime environment
		m_env = std::make_unique<Ort::Env>(ORT_LOGGING_LEVEL_WARNING, "ImageMattingOnnx");

		// Create session options with optimized configuration
		Ort::SessionOptions sessionOptions;
		sessionOptions.SetGraphOptimizationLevel(GraphOptimizationLevel::ORT_ENABLE_ALL);

		// Keep verbose logging for debugging
		//sessionOptions.SetLogSeverityLevel(0); // 0 = Verbose logging

		// Create CUDA provider options
		OrtCUDAProviderOptionsV2* cuda_options = nullptr;
		Ort::GetApi().CreateCUDAProviderOptions(&cuda_options);

		// Define CUDA provider options (excluding user_compute_stream)
		std::vector<const char*> keys = {
			"device_id",
			"gpu_mem_limit",
			"arena_extend_strategy",
			"cudnn_conv_algo_search",
			"do_copy_in_default_stream",
			"has_user_compute_stream"
		};

		std::string memString = std::to_string(2ULL << 30);
		std::vector<const char*> values = {
			"0",  // GPU device ID
			memString.c_str(),  // GPU memory limit
			"1",  // Arena extend strategy (Next Power of Two)
			"HEURISTIC",  // cuDNN heuristic search (faster than EXHAUSTIVE)
			"1",  // Copy in default stream (better for context sharing)
			"0"   // No user compute stream (let ONNX manage its own)
		};

		// Update CUDA provider options
		Ort::GetApi().UpdateCUDAProviderOptions(cuda_options, keys.data(), values.data(), keys.size());

		// Don't set user_compute_stream - let ONNX Runtime manage its own stream
		// Setting external streams can cause cuDNN stream mismatch errors
		// ONNX Runtime will still use the same CUDA context, just with its own stream
		if (m_cudaStream != nullptr) {
			std::cout << "ImageMattingOnnx: ONNX Runtime will use same CUDA context with its own stream" << std::endl;
		}

		// Append CUDA Execution Provider to session options
		Ort::GetApi().SessionOptionsAppendExecutionProvider_CUDA_V2(
			static_cast<OrtSessionOptions*>(sessionOptions), cuda_options);

		sessionOptions.SetExecutionMode(ExecutionMode::ORT_PARALLEL);

		// Create session
		m_session = std::make_unique<Ort::Session>(*m_env, modelPath, sessionOptions);

		// Get model input/output names
		Ort::AllocatorWithDefaultOptions allocator;
		m_modelInputName = m_session->GetInputNameAllocated(0, allocator).get();
		m_modelOutputName = m_session->GetOutputNameAllocated(0, allocator).get();

		// Create binding and CUDA memory info
		m_binding = std::make_unique<Ort::IoBinding>(*m_session);
		m_memoryCuda = std::make_unique<Ort::MemoryInfo>("Cuda", OrtDeviceAllocator, 0, OrtMemTypeDefault);

		// Create input tensor with appropriate number of channels
		std::vector<int64_t> inputDims = { 1, inputChannels, m_modelInputHeight, m_modelInputWidth };
		m_inputTensor = std::make_unique<Ort::Value>(CreateOrtValueFromDeviceMemory(
			m_deviceResizedInputBuffer, inputDims, ONNX_TENSOR_ELEMENT_DATA_TYPE_FLOAT, *m_memoryCuda));

		// Create output tensor
		std::vector<int64_t> outputDims = { 1, 1, m_modelInputHeight, m_modelInputWidth };
		m_outputTensor = std::make_unique<Ort::Value>(CreateOrtValueFromDeviceMemory(
			m_deviceResizedOutputBuffer, outputDims, ONNX_TENSOR_ELEMENT_DATA_TYPE_FLOAT, *m_memoryCuda));

		// Bind input and output
		m_binding->BindInput(m_modelInputName.c_str(), *m_inputTensor);
		m_binding->BindOutput(m_modelOutputName.c_str(), *m_outputTensor);

		m_initialized = true;
		std::cout << "Initialization successful with " << (m_isRgba ? "RGBA" : "RGB") << " input format." << std::endl;
		std::cout << "Input buffer size: " << m_inputBufferSize << " bytes" << std::endl;
		std::cout << "Output buffer size: " << m_outputBufferSize << " bytes" << std::endl;

		// Restore main.cpp CUDA context (ONNX Runtime may have changed it)
		if (savedContext != nullptr) {
			CUresult restoreResult = cuCtxSetCurrent(savedContext);
			if (restoreResult != CUDA_SUCCESS) {
				std::cerr << "Failed to restore main.cpp CUDA context in Init(): " << restoreResult << std::endl;
			} else {
				std::cout << "Successfully restored main.cpp CUDA context: " << savedContext << std::endl;
			}
		}

		return true;
	}
	catch (const Ort::Exception& e) {
		std::cerr << "ONNX Runtime exception during Init: " << e.what() << std::endl;

		// Restore main.cpp CUDA context on exception
		if (savedContext != nullptr) {
			CUresult restoreResult = cuCtxSetCurrent(savedContext);
			if (restoreResult != CUDA_SUCCESS) {
				std::cerr << "Failed to restore main.cpp CUDA context in Init() exception handler: " << restoreResult << std::endl;
			}
		}

		Shutdown();
		return false;
	}
	catch (const std::exception& e) {
		std::cerr << "Standard C++ exception during Init: " << e.what() << std::endl;

		// Restore main.cpp CUDA context on exception
		if (savedContext != nullptr) {
			CUresult restoreResult = cuCtxSetCurrent(savedContext);
			if (restoreResult != CUDA_SUCCESS) {
				std::cerr << "Failed to restore main.cpp CUDA context in Init() exception handler: " << restoreResult << std::endl;
			}
		}

		Shutdown();
		return false;
	}
	catch (...) {
		std::cerr << "Unknown exception during Init." << std::endl;

		// Restore main.cpp CUDA context on exception
		if (savedContext != nullptr) {
			CUresult restoreResult = cuCtxSetCurrent(savedContext);
			if (restoreResult != CUDA_SUCCESS) {
				std::cerr << "Failed to restore main.cpp CUDA context in Init() exception handler: " << restoreResult << std::endl;
			}
		}

		Shutdown();
		return false;
	}
}

bool ImageMattingOnnx::Infer() {
	if (!m_initialized) {
		std::cerr << "Library not initialized. Call Init() first." << std::endl;
		return false;
	}

	if (!m_deviceInputBuffer || !m_deviceOutputBuffer) {
		std::cerr << "Input or output buffers not allocated" << std::endl;
		return false;
	}

	// Save current CUDA context (from main.cpp) to restore it after ONNX Runtime potentially changes it
	CUcontext savedContext = nullptr;
	CUresult cuResult = cuCtxGetCurrent(&savedContext);
	if (cuResult != CUDA_SUCCESS) {
		std::cerr << "Failed to get current CUDA context in Infer(): " << cuResult << std::endl;
	}

	try {
		// Preprocess input using common base class implementation
		CUresult preprocessResult = PreprocessInputBufferCommon(
			(CUdeviceptr)m_deviceInputBuffer,
			(CUdeviceptr)m_preprocessedBuffer,  // Will be nullptr when no resizing is needed
			(CUdeviceptr)m_deviceResizedInputBuffer,
			m_imageWidth,
			m_imageHeight,
			m_modelInputWidth,
			m_modelInputHeight,
			m_isRgba,
			m_normalizationParams,
			m_preprocessedBufferSize,
			m_resizeMethod,
			m_cudaStream
		);
		if (preprocessResult != CUDA_SUCCESS) {
			const char* errorStr;
			cuGetErrorString(preprocessResult, &errorStr);
			std::cerr << "Preprocessing failed: " << errorStr << std::endl;
			return false;
		}

		//SavePlanarFloatImageToPNG("Videos\\temp.png", m_deviceResizedInputBuffer, m_modelInputWidth, m_modelInputHeight, m_isRgba, m_cudaStream);

		// Run inference with optimized run options
		Ort::RunOptions runOptions;

		// Note: CUDA stream is already configured in the provider options during initialization
		// No need to set it again in RunOptions

		for (int i = 0; i < 1; i++) {
			auto start = std::chrono::high_resolution_clock::now();

			// Execute the model with IoBinding
			m_session->Run(runOptions, *m_binding);

			auto end = std::chrono::high_resolution_clock::now();
			std::chrono::duration<double> elapsed = end - start;
			std::cout << "ONNX inference time: " << elapsed.count() << " seconds" << std::endl;
		}

		//SaveAlphaMatteToPNG("Videos\\alpha_onnx_head.png", (CUdeviceptr)m_deviceResizedOutputBuffer, m_modelInputWidth, m_modelInputHeight);

		// Postprocess output using common base class implementation
		CUresult postprocessResult = PostprocessOutputBufferCommon(
			(CUdeviceptr)m_deviceResizedOutputBuffer, (CUdeviceptr)m_deviceOutputBuffer,
			m_modelInputWidth, m_modelInputHeight,
			m_imageWidth, m_imageHeight,
			m_resizeMethod,
			m_cudaStream);

		if (postprocessResult != CUDA_SUCCESS) {
			const char* errorStr;
			cuGetErrorString(postprocessResult, &errorStr);
			std::cerr << "Output postprocessing failed: " << errorStr << std::endl;
			return false;
		}

		// Synchronize CUDA stream
		CUresult cudaStatus = cuStreamSynchronize(m_cudaStream);
		if (cudaStatus != CUDA_SUCCESS) {
			const char* errorStr;
			cuGetErrorString(cudaStatus, &errorStr);
			std::cerr << "Failed to synchronize CUDA stream: " << errorStr << std::endl;

			// Restore main.cpp CUDA context on error
			if (savedContext != nullptr) {
				CUresult restoreResult = cuCtxSetCurrent(savedContext);
				if (restoreResult != CUDA_SUCCESS) {
					std::cerr << "Failed to restore main.cpp CUDA context in Infer() error handler: " << restoreResult << std::endl;
				}
			}

			return false;
		}

		// Restore main.cpp CUDA context (ONNX Runtime may have changed it)
		if (savedContext != nullptr) {
			CUresult restoreResult = cuCtxSetCurrent(savedContext);
			if (restoreResult != CUDA_SUCCESS) {
				std::cerr << "Failed to restore main.cpp CUDA context in Infer(): " << restoreResult << std::endl;
			}
		}

		return true;
	}
	catch (const Ort::Exception& e) {
		std::cerr << "ONNX Runtime exception during Infer: " << e.what() << std::endl;

		// Restore main.cpp CUDA context on exception
		if (savedContext != nullptr) {
			CUresult restoreResult = cuCtxSetCurrent(savedContext);
			if (restoreResult != CUDA_SUCCESS) {
				std::cerr << "Failed to restore main.cpp CUDA context in Infer() exception handler: " << restoreResult << std::endl;
			}
		}

		return false;
	}
	catch (const std::exception& e) {
		std::cerr << "Standard C++ exception during Infer: " << e.what() << std::endl;

		// Restore main.cpp CUDA context on exception
		if (savedContext != nullptr) {
			CUresult restoreResult = cuCtxSetCurrent(savedContext);
			if (restoreResult != CUDA_SUCCESS) {
				std::cerr << "Failed to restore main.cpp CUDA context in Infer() exception handler: " << restoreResult << std::endl;
			}
		}

		return false;
	}
	catch (...) {
		std::cerr << "Unknown exception during Infer" << std::endl;

		// Restore main.cpp CUDA context on exception
		if (savedContext != nullptr) {
			CUresult restoreResult = cuCtxSetCurrent(savedContext);
			if (restoreResult != CUDA_SUCCESS) {
				std::cerr << "Failed to restore main.cpp CUDA context in Infer() exception handler: " << restoreResult << std::endl;
			}
		}

		return false;
	}
}

void ImageMattingOnnx::Shutdown() {
	try {
		// Release ONNX Runtime resources
		m_binding.reset();
		m_outputTensor.reset();
		m_inputTensor.reset();
		m_session.reset();
		m_memoryCuda.reset();
		m_env.reset();

		// Release CUDA resources
		if (m_deviceInputBuffer) {
			cuMemFree((CUdeviceptr)m_deviceInputBuffer);
			m_deviceInputBuffer = nullptr;
		}

		if (m_deviceOutputBuffer) {
			cuMemFree((CUdeviceptr)m_deviceOutputBuffer);
			m_deviceOutputBuffer = nullptr;
		}

		// Only free resized buffers if they were separately allocated (not pointing to original buffers)
		if (m_deviceResizedInputBuffer && m_deviceResizedInputBuffer != m_deviceInputBuffer) {
			cuMemFree((CUdeviceptr)m_deviceResizedInputBuffer);
		}
		m_deviceResizedInputBuffer = nullptr;

		if (m_deviceResizedOutputBuffer && m_deviceResizedOutputBuffer != m_deviceOutputBuffer) {
			cuMemFree((CUdeviceptr)m_deviceResizedOutputBuffer);
		}
		m_deviceResizedOutputBuffer = nullptr;

		if (m_preprocessedBuffer) {
			cuMemFree((CUdeviceptr)m_preprocessedBuffer);
			m_preprocessedBuffer = nullptr;
		}

		// Don't destroy the stream as it may be external
		m_cudaStream = nullptr;

		m_initialized = false;
		std::cout << "Shutdown complete." << std::endl;
	}
	catch (const std::exception& e) {
		std::cerr << "Exception during shutdown: " << e.what() << std::endl;
	}
	catch (...) {
		std::cerr << "Unknown exception during shutdown" << std::endl;
	}
}