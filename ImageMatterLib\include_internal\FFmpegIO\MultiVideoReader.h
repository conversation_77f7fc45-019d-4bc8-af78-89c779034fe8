// MultiVideoReader.h
#pragma once

#include <string>
#include <memory>
#include <cstdint>
#include <cuda.h>
#include "Helpers.h"
#include "FFmpegIO/DirectVideoReader.h"

// Include AVRational definition
extern "C" {
#include <libavutil/rational.h>
}

// MultiVideoReader: reads RGB and Alpha from two separate video files
// For an input path like "video.mp4", it opens "video rgb.mp4" for RGB and
// "video alpha.mp4" for Alpha (alpha stored in Y plane). Both videos are
// expected to have identical resolution, duration, GOP size and framerate.
class MultiVideoReader {
public:
    // Initialize and open both RGB and Alpha video files
    static std::shared_ptr<MultiVideoReader> Create(
        const std::string& filePath,
        CUcontext cudaContext);

    // Destructor
    ~MultiVideoReader();

    // Seek both readers to a specific time position (in seconds)
    bool Seek(double timeInSeconds);

    // Read the next frame as byte-interleaved BGRA (8 bits per channel)
    // Buffer size must be width*height*4 bytes
    // Returns the frame timestamp, or -1.0 on error/EOF
    double ReadTransparentFrame(CUdeviceptr cudaBgraBuffer, size_t bufferSize, CUstream stream = 0);

    // Get video properties (from RGB reader)
    int GetWidth() const { return m_width; }
    int GetHeight() const { return m_height; }
    double GetDuration() const { return m_duration; }
    AVRational GetFrameRate() const { return m_frameRate; }
    double GetFrameRateDouble() const { return m_frameRate.num / static_cast<double>(m_frameRate.den); }
    Helpers::TextureFormat GetTextureFormat() const { return m_textureFormat; }
    AVRational GetTimeBase() const { return m_timeBase; }
    double GetTimeBaseDouble() const { return m_timeBase.num / static_cast<double>(m_timeBase.den); }

    // Get underlying video stream format
    Helpers::TextureFormat GetVideoStreamFormat() const;

    // Close both video readers and release resources
    void Close();

private:
    // Private constructor - use Create() to create instances
    MultiVideoReader();

    // Initialize both video readers
    bool Initialize(const std::string& filePath, CUcontext cudaContext);

    // Helper to derive RGB and Alpha file paths from base path
    static void DeriveRgbAlphaPaths(const std::string& basePath, 
                                     std::string& rgbPath, 
                                     std::string& alphaPath);

private:
    // Video readers
    std::shared_ptr<DirectVideoReader> m_rgbReader;
    std::shared_ptr<DirectVideoReader> m_alphaReader;

    // CUDA state
    CUcontext m_cudaContext;
    CUstream m_cudaStream;

    // Temporary buffers for combining RGB and Alpha
    CUdeviceptr m_tempRgbBuffer;
    CUdeviceptr m_tempAlphaBuffer;
    size_t m_tempRgbBufferSize;
    size_t m_tempAlphaBufferSize;

    // Video properties (same for both videos)
    int m_width;
    int m_height;
    double m_duration;
    AVRational m_frameRate;
    AVRational m_timeBase;
    Helpers::TextureFormat m_textureFormat;

    // State tracking
    bool m_isInitialized;
};
