// D3D11VideoReader.h - Public C API for C# Interop
#pragma once

#ifdef _WIN32
    #ifdef IMAGEMATTERLIB_EXPORTS
        #define IMAGEMATTER_API extern "C" __declspec(dllexport)
    #else
        #define IMAGEMATTER_API extern "C" __declspec(dllimport)
    #endif
#else
    #define IMAGEMATTER_API extern "C"
#endif

#include <d3d11_1.h>

// Opaque handle for D3D11VideoReader instance
typedef void* D3D11VideoReaderHandle;

// Error codes
enum D3D11VideoReaderError {
    D3D11_VIDEO_READER_SUCCESS = 0,
    D3D11_VIDEO_READER_ERROR_INVALID_PARAMETER = -1,
    D3D11_VIDEO_READER_ERROR_INITIALIZATION_FAILED = -2,
    D3D11_VIDEO_READER_ERROR_FILE_NOT_FOUND = -3,
    D3D11_VIDEO_READER_ERROR_SEEK_FAILED = -4,
    D3D11_VIDEO_READER_ERROR_READ_FAILED = -5,
    D3D11_VIDEO_READER_ERROR_TEXTURE_INVALID = -6,
    D3D11_VIDEO_READER_ERROR_CUDA_FAILED = -7,
    D3D11_VIDEO_READER_ERROR_UNKNOWN = -99
};

// Video properties structure
struct D3D11VideoReaderProperties {
    int width;
    int height;
    double duration;        // in seconds
    double frameRate;       // frames per second
    int totalFrames;        // estimated total frames
};

// ============================================================================
// Core Functions
// ============================================================================

// Create a new D3D11VideoReader instance
// Parameters:
//   device: ID3D11Device1 pointer from your D3D11 application
//   filePath: Path to the video file (supports "video.mp4" which will open "video rgb.mp4" and "video alpha.mp4")
//   outHandle: Pointer to receive the handle
// Returns: Error code (0 for success)
IMAGEMATTER_API int D3D11VideoReader_Create(
    ID3D11Device1* device,
    const char* filePath,
    D3D11VideoReaderHandle* outHandle);

// Destroy a D3D11VideoReader instance
// Parameters:
//   handle: Handle returned by D3D11VideoReader_Create
IMAGEMATTER_API void D3D11VideoReader_Destroy(
    D3D11VideoReaderHandle handle);

// ============================================================================
// Video Properties
// ============================================================================

// Get video properties
// Parameters:
//   handle: Reader handle
//   outProperties: Pointer to receive properties
// Returns: Error code (0 for success)
IMAGEMATTER_API int D3D11VideoReader_GetProperties(
    D3D11VideoReaderHandle handle,
    D3D11VideoReaderProperties* outProperties);

// Get video width
IMAGEMATTER_API int D3D11VideoReader_GetWidth(
    D3D11VideoReaderHandle handle);

// Get video height
IMAGEMATTER_API int D3D11VideoReader_GetHeight(
    D3D11VideoReaderHandle handle);

// Get video duration in seconds
IMAGEMATTER_API double D3D11VideoReader_GetDuration(
    D3D11VideoReaderHandle handle);

// Get video frame rate
IMAGEMATTER_API double D3D11VideoReader_GetFrameRate(
    D3D11VideoReaderHandle handle);

// ============================================================================
// Seeking
// ============================================================================

// Seek to a specific time in the video
// Parameters:
//   handle: Reader handle
//   timeInSeconds: Target time in seconds
// Returns: Error code (0 for success)
IMAGEMATTER_API int D3D11VideoReader_Seek(
    D3D11VideoReaderHandle handle,
    double timeInSeconds);

// ============================================================================
// Frame Reading
// ============================================================================

// Read the next frame as BGRA into a D3D11 texture
// The texture must be DXGI_FORMAT_B8G8R8A8_UNORM format
// Supports textures with D3D11_RESOURCE_MISC_SHARED_NTHANDLE | D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX flags
// Parameters:
//   handle: Reader handle
//   texture: Target ID3D11Texture2D (must be correct format and size)
//   outTimestamp: Pointer to receive frame timestamp (can be NULL)
// Returns: Error code (0 for success, negative for error/EOF)
IMAGEMATTER_API int D3D11VideoReader_ReadTransparentFrame(
    D3D11VideoReaderHandle handle,
    ID3D11Texture2D* texture,
    double* outTimestamp);

// ============================================================================
// C# P/Invoke Example
// ============================================================================

/*
using System;
using System.Runtime.InteropServices;

public class D3D11VideoReader
{
    // Error codes
    public enum Error
    {
        Success = 0,
        InvalidParameter = -1,
        InitializationFailed = -2,
        FileNotFound = -3,
        SeekFailed = -4,
        ReadFailed = -5,
        TextureInvalid = -6,
        CudaFailed = -7,
        Unknown = -99
    }

    // Video properties structure
    [StructLayout(LayoutKind.Sequential)]
    public struct Properties
    {
        public int Width;
        public int Height;
        public double Duration;
        public double FrameRate;
        public int TotalFrames;
    }

    // P/Invoke declarations
    [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
    private static extern int D3D11VideoReader_Create(
        IntPtr device,
        [MarshalAs(UnmanagedType.LPStr)] string filePath,
        out IntPtr outHandle);

    [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
    private static extern void D3D11VideoReader_Destroy(IntPtr handle);

    [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
    private static extern int D3D11VideoReader_GetProperties(
        IntPtr handle,
        out Properties outProperties);

    [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
    private static extern int D3D11VideoReader_GetWidth(IntPtr handle);

    [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
    private static extern int D3D11VideoReader_GetHeight(IntPtr handle);

    [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
    private static extern double D3D11VideoReader_GetDuration(IntPtr handle);

    [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
    private static extern double D3D11VideoReader_GetFrameRate(IntPtr handle);

    [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
    private static extern int D3D11VideoReader_Seek(IntPtr handle, double timeInSeconds);

    [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
    private static extern int D3D11VideoReader_ReadTransparentFrame(
        IntPtr handle,
        IntPtr texture,
        out double outTimestamp);

    // C# wrapper class
    private IntPtr handle;
    private bool disposed = false;

    public Properties VideoProperties { get; private set; }

    public D3D11VideoReader(IntPtr d3d11Device, string filePath)
    {
        int result = D3D11VideoReader_Create(d3d11Device, filePath, out handle);
        if (result != 0)
        {
            throw new Exception($"Failed to create D3D11VideoReader: {(Error)result}");
        }

        Properties props;
        result = D3D11VideoReader_GetProperties(handle, out props);
        if (result == 0)
        {
            VideoProperties = props;
        }
    }

    public void Seek(double timeInSeconds)
    {
        int result = D3D11VideoReader_Seek(handle, timeInSeconds);
        if (result != 0)
        {
            throw new Exception($"Seek failed: {(Error)result}");
        }
    }

    public double ReadTransparentFrame(IntPtr texture)
    {
        double timestamp;
        int result = D3D11VideoReader_ReadTransparentFrame(handle, texture, out timestamp);
        if (result != 0)
        {
            throw new Exception($"ReadTransparentFrame failed: {(Error)result}");
        }
        return timestamp;
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!disposed)
        {
            if (handle != IntPtr.Zero)
            {
                D3D11VideoReader_Destroy(handle);
                handle = IntPtr.Zero;
            }
            disposed = true;
        }
    }

    ~D3D11VideoReader()
    {
        Dispose(false);
    }
}

// Usage example in C#:
// var reader = new D3D11VideoReader(devicePtr, @"C:\Videos\myvideo.mp4");
// Console.WriteLine($"Video: {reader.VideoProperties.Width}x{reader.VideoProperties.Height}");
// reader.ReadTransparentFrame(texturePtr);
// reader.Dispose();
*/
