#include "HeadDetectorKernels.cuh"
#include "KernelManager.h"
#include <device_launch_parameters.h>
#include <math_constants.h>
#include <iostream>
#include <exception>

#ifdef __CUDACC__

__device__ float lanczos_weight(float x, float a = 3.0f) {
    const float eps = 1e-6f;
    if (fabsf(x) < eps) return 1.0f;
    if (fabsf(x) >= a) return 0.0f;
    float pi_x = CUDART_PI_F * x;
    float pi_x_a = pi_x / a;
    return (sinf(pi_x) / pi_x) * (sinf(pi_x_a) / pi_x_a);
}
// NB: input is assumed to be a float interleaved RGB image.
// Input is now a planar RGB buffer: [RRR...][GGG...][BBB...]
extern "C" __global__ void LanczosResizeAndPadKernel(
    float* output, int outputWidth, int outputHeight,
    const float* input, int inputWidth, int inputHeight,
    float padValue)
{
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;
    int c = blockIdx.z * blockDim.z + threadIdx.z;

    // Only process R, G, B (skip alpha)
    if (x >= outputWidth || y >= outputHeight || c >= 3)  // c ∈ {0,1,2} (R,G,B)
        return;

    // Compute scale factor (preserve aspect ratio)
    float scale = fminf(
        (float)outputWidth / (float)inputWidth,
        (float)outputHeight / (float)inputHeight
    );

    // Compute resized dimensions (before padding)
    int resizedWidth = (int)(scale * inputWidth + 0.5f);
    int resizedHeight = (int)(scale * inputHeight + 0.5f);

    // Compute padding to center the resized image
    int padLeft = (outputWidth - resizedWidth) / 2;
    int padTop = (outputHeight - resizedHeight) / 2;

    // Check if current pixel is in the padding region
    if (x < padLeft || x >= (padLeft + resizedWidth) ||
        y < padTop || y >= (padTop + resizedHeight)) {
        int outIdx = (c * outputHeight + y) * outputWidth + x;
        output[outIdx] = padValue;
        return;
    }

    // Map output coordinates to input coordinates
    float srcX = (float)(x - padLeft) * (float)(inputWidth - 1) / (float)(resizedWidth - 1);
    float srcY = (float)(y - padTop) * (float)(inputHeight - 1) / (float)(resizedHeight - 1);

    // Lanczos interpolation
    const float a = 3.0f;
    int x_start = max(0, (int)floor(srcX - a + 0.5f));
    int x_end = min(inputWidth - 1, (int)floor(srcX + a + 0.5f));
    int y_start = max(0, (int)floor(srcY - a + 0.5f));
    int y_end = min(inputHeight - 1, (int)floor(srcY + a + 0.5f));

    float sum = 0.0f;
    float weight_sum = 0.0f;

    for (int j = y_start; j <= y_end; ++j) {
        float dy = srcY - j;
        float wy = lanczos_weight(dy, a);
        if (wy == 0.0f) continue;

        for (int i = x_start; i <= x_end; ++i) {
            float dx = srcX - i;
            float wx = lanczos_weight(dx, a);
            if (wx == 0.0f) continue;

            float weight = wx * wy;
            // Input is planar RGB → channel c is at offset (c * planeSize)
            int planeSize = inputWidth * inputHeight;
            float pixel = input[c * planeSize + j * inputWidth + i]; // Planar access
            sum += pixel * weight;
            weight_sum += weight;
        }
    }

    int outIdx = (c * outputHeight + y) * outputWidth + x;
    output[outIdx] = (weight_sum > 0.0f) ? (sum / weight_sum) : padValue;
}
CUresult LaunchLanczosResizeAndPadKernel(
    float* output, int outputWidth, int outputHeight,
    float* input, int inputWidth, int inputHeight)
{
    unsigned int blockSizeX = 16;
    unsigned int blockSizeY = 16;
    unsigned int blockSizeZ = 1;
    unsigned int gridSizeX = (outputWidth + blockSizeX - 1) / blockSizeX;
    unsigned int gridSizeY = (outputHeight + blockSizeY - 1) / blockSizeY;
    unsigned int gridSizeZ = 3; // Only R, G, B (no alpha)
    
    float padValue = 0.0f;

    try {
        CUfunction kernel = KernelHelpers::GetKernelFunction("LanczosResizeAndPadKernel");
        void* kernelArgs[] = { &output, &outputWidth, &outputHeight, &input, &inputWidth, &inputHeight, &padValue };
        CUresult result = KernelHelpers::LaunchKernel(kernel,
            gridSizeX, gridSizeY, gridSizeZ, // Grid dimensions
            blockSizeX, blockSizeY, blockSizeZ, // Block dimensions
            0, 0, kernelArgs); // No stream specified, using default
            
        if (result != CUDA_SUCCESS) {
            const char* errorStr;
            cuGetErrorString(result, &errorStr);
            std::cerr << "LaunchLanczosResizeAndPadKernel: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
        }
        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "LaunchLanczosResizeAndPadKernel: Exception: " << e.what() << std::endl;
        return CUDA_ERROR_LAUNCH_FAILED;
    }
}

#endif 