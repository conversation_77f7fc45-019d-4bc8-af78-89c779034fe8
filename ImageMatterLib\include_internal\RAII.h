#pragma once

#include <cuda.h>
#include <cuda_runtime.h>
#include <cstddef>
#include "Helpers.h"

//=============================================================================
// RAII Resource Management Classes for CUDA
//=============================================================================

/**
 * RAII wrapper for CUDA device memory allocation
 * Automatically frees memory when object goes out of scope
 */
class CudaMemoryRAII {
private:
	CUdeviceptr m_ptr;
	size_t m_size;

public:
	// Constructor - allocates memory
	explicit CudaMemoryRAII(size_t size = 0);
	
	// Destructor - frees memory
	~CudaMemoryRAII() noexcept;
	
	// Disable copy constructor and assignment
	CudaMemoryRAII(const CudaMemoryRAII&) = delete;
	CudaMemoryRAII& operator=(const CudaMemoryRAII&) = delete;
	
	// Enable move semantics
	CudaMemoryRAII(CudaMemoryRAII&& other) noexcept;
	CudaMemoryRAII& operator=(CudaMemoryRAII&& other) noexcept;
	
	// Reallocate with new size
	void reallocate(size_t new_size);
	
	// Release ownership of the memory (caller becomes responsible for freeing)
	CUdeviceptr release() noexcept;
	
	// Reset to new pointer (frees old memory if any)
	void reset(CUdeviceptr ptr = 0, size_t size = 0) noexcept;
	
	// Accessors
	CUdeviceptr get() const { return m_ptr; }
	size_t size() const { return m_size; }
	operator CUdeviceptr() const { return m_ptr; }
	
	// Explicit cast operators for convenience
	float* as_float() const { return reinterpret_cast<float*>(m_ptr); }
	void* as_void() const { return reinterpret_cast<void*>(m_ptr); }
	unsigned char* as_uchar() const { return reinterpret_cast<unsigned char*>(m_ptr); }
	
	// Check if memory is allocated
	bool is_allocated() const { return m_ptr != 0; }
};

/**
 * RAII wrapper for CUDA stream
 * Automatically destroys stream when object goes out of scope
 */
class CudaStreamRAII {
private:
	CUstream m_stream;

public:
	// Constructor - creates stream
	CudaStreamRAII();
	explicit CudaStreamRAII(unsigned int flags);
	
	// Destructor - destroys stream
	~CudaStreamRAII() noexcept;
	
	// Disable copy constructor and assignment
	CudaStreamRAII(const CudaStreamRAII&) = delete;
	CudaStreamRAII& operator=(const CudaStreamRAII&) = delete;
	
	// Enable move semantics
	CudaStreamRAII(CudaStreamRAII&& other) noexcept;
	CudaStreamRAII& operator=(CudaStreamRAII&& other) noexcept;
	
	// Stream operations
	void synchronize();
	
	// Release ownership of the stream
	CUstream release() noexcept;
	
	// Reset to new stream (destroys old stream if any)
	void reset(CUstream stream = nullptr) noexcept;
	
	// Accessors
	CUstream get() const { return m_stream; }
	operator CUstream() const { return m_stream; }
	
	// Check if stream is valid
	bool is_valid() const { return m_stream != nullptr; }
};

/**
 * RAII wrapper for CUDA context
 * Automatically destroys context when object goes out of scope
 */
class CudaContextRAII {
private:
	CUcontext m_context;
	bool m_initialized;

public:
	// Constructor - creates context
	CudaContextRAII();
	explicit CudaContextRAII(unsigned int flags, CUdevice device = 0);
	
	// Destructor - destroys context
	~CudaContextRAII() noexcept;
	
	// Disable copy constructor and assignment
	CudaContextRAII(const CudaContextRAII&) = delete;
	CudaContextRAII& operator=(const CudaContextRAII&) = delete;
	
	// Enable move semantics
	CudaContextRAII(CudaContextRAII&& other) noexcept;
	CudaContextRAII& operator=(CudaContextRAII&& other) noexcept;
	
	// Context operations
	void setCurrent();
	void synchronize();
	
	// Release ownership of the context
	CUcontext release() noexcept;
	
	// Reset to new context (destroys old context if any)
	void reset(CUcontext context = nullptr) noexcept;
	
	// Accessors
	CUcontext get() const { return m_context; }
	operator CUcontext() const { return m_context; }
	
	// Check if context is valid
	bool is_valid() const { return m_context != nullptr; }
};

/**
 * Helper class to ensure CUDA context is set for a scope
 */
class CudaContextGuard {
private:
	CUcontext m_previousContext;
	CUcontext m_targetContext;
	
public:
	explicit CudaContextGuard(CUcontext context);
	~CudaContextGuard() noexcept;
	
	// Disable copy and move
	CudaContextGuard(const CudaContextGuard&) = delete;
	CudaContextGuard& operator=(const CudaContextGuard&) = delete;
	CudaContextGuard(CudaContextGuard&&) = delete;
	CudaContextGuard& operator=(CudaContextGuard&&) = delete;
};
