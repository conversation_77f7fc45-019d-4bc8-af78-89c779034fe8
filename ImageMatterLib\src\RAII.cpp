#include "RAII.h"
#include <iostream>
#include <stdexcept>
#include <string>

//=============================================================================
// CudaMemoryRAII Implementation
//=============================================================================

CudaMemoryRAII::CudaMemoryRAII(size_t size) : m_ptr(0), m_size(size) {
	if (size > 0) {
		CUresult result = cuMemAlloc(&m_ptr, size);
		if (result != CUDA_SUCCESS) {
			const char* errStr = nullptr;
			cuGetErrorString(result, &errStr);
			throw std::runtime_error(std::string("CUDA memory allocation failed: ") + (errStr ? errStr : "Unknown error"));
		}
	}
}

CudaMemoryRAII::~CudaMemoryRAII() noexcept {
	if (m_ptr) {
		cuMemFree(m_ptr); // Don't use DRIVER_API_CHECK in destructor
	}
}

CudaMemoryRAII::CudaMemoryRAII(CudaMemoryRAII&& other) noexcept 
	: m_ptr(other.m_ptr), m_size(other.m_size) {
	other.m_ptr = 0;
	other.m_size = 0;
}

CudaMemoryRAII& CudaMemoryRAII::operator=(CudaMemoryRAII&& other) noexcept {
	if (this != &other) {
		if (m_ptr)
			cuMemFree(m_ptr);
		m_ptr = other.m_ptr;
		m_size = other.m_size;
		other.m_ptr = 0;
		other.m_size = 0;
	}
	return *this;
}

void CudaMemoryRAII::reallocate(size_t new_size) {
	if (m_ptr) {
		cuMemFree(m_ptr);
		m_ptr = 0;
	}
	m_size = new_size;
	if (new_size > 0) {
		CUresult result = cuMemAlloc(&m_ptr, new_size);
		if (result != CUDA_SUCCESS) {
			const char* errStr = nullptr;
			cuGetErrorString(result, &errStr);
			throw std::runtime_error(std::string("CUDA memory reallocation failed: ") + (errStr ? errStr : "Unknown error"));
		}
	}
}

CUdeviceptr CudaMemoryRAII::release() noexcept {
	CUdeviceptr ptr = m_ptr;
	m_ptr = 0;
	m_size = 0;
	return ptr;
}

void CudaMemoryRAII::reset(CUdeviceptr ptr, size_t size) noexcept {
	if (m_ptr) {
		cuMemFree(m_ptr);
	}
	m_ptr = ptr;
	m_size = size;
}

//=============================================================================
// CudaStreamRAII Implementation
//=============================================================================

CudaStreamRAII::CudaStreamRAII() : m_stream(nullptr) {
	CUresult result = cuStreamCreate(&m_stream, CU_STREAM_DEFAULT);
	if (result != CUDA_SUCCESS) {
		const char* errStr = nullptr;
		cuGetErrorString(result, &errStr);
		throw std::runtime_error(std::string("CUDA stream creation failed: ") + (errStr ? errStr : "Unknown error"));
	}
}

CudaStreamRAII::CudaStreamRAII(unsigned int flags) : m_stream(nullptr) {
	CUresult result = cuStreamCreate(&m_stream, flags);
	if (result != CUDA_SUCCESS) {
		const char* errStr = nullptr;
		cuGetErrorString(result, &errStr);
		throw std::runtime_error(std::string("CUDA stream creation failed: ") + (errStr ? errStr : "Unknown error"));
	}
}

CudaStreamRAII::~CudaStreamRAII() noexcept {
	if (m_stream) {
		cuStreamDestroy(m_stream);
	}
}

CudaStreamRAII::CudaStreamRAII(CudaStreamRAII&& other) noexcept 
	: m_stream(other.m_stream) {
	other.m_stream = nullptr;
}

CudaStreamRAII& CudaStreamRAII::operator=(CudaStreamRAII&& other) noexcept {
	if (this != &other) {
		if (m_stream)
			cuStreamDestroy(m_stream);
		m_stream = other.m_stream;
		other.m_stream = nullptr;
	}
	return *this;
}

void CudaStreamRAII::synchronize() {
	if (m_stream) {
		CUresult result = cuStreamSynchronize(m_stream);
		if (result != CUDA_SUCCESS) {
			const char* errStr = nullptr;
			cuGetErrorString(result, &errStr);
			throw std::runtime_error(std::string("CUDA stream synchronization failed: ") + (errStr ? errStr : "Unknown error"));
		}
	}
}

CUstream CudaStreamRAII::release() noexcept {
	CUstream stream = m_stream;
	m_stream = nullptr;
	return stream;
}

void CudaStreamRAII::reset(CUstream stream) noexcept {
	if (m_stream) {
		cuStreamDestroy(m_stream);
	}
	m_stream = stream;
}

//=============================================================================
// CudaContextRAII Implementation
//=============================================================================

CudaContextRAII::CudaContextRAII() : m_context(nullptr), m_initialized(false) {
	CUresult result = cuInit(0);
	if (result != CUDA_SUCCESS) {
		const char* errStr = nullptr;
		cuGetErrorString(result, &errStr);
		throw std::runtime_error(std::string("CUDA initialization failed: ") + (errStr ? errStr : "Unknown error"));
	}
	
	result = cuCtxCreate(&m_context, 0, 0);
	if (result != CUDA_SUCCESS) {
		const char* errStr = nullptr;
		cuGetErrorString(result, &errStr);
		throw std::runtime_error(std::string("CUDA context creation failed: ") + (errStr ? errStr : "Unknown error"));
	}
	
	result = cuCtxSetCurrent(m_context);
	if (result != CUDA_SUCCESS) {
		cuCtxDestroy(m_context); // Clean up on failure
		const char* errStr = nullptr;
		cuGetErrorString(result, &errStr);
		throw std::runtime_error(std::string("CUDA context set current failed: ") + (errStr ? errStr : "Unknown error"));
	}
	
	m_initialized = true;
}

CudaContextRAII::CudaContextRAII(unsigned int flags, CUdevice device) 
	: m_context(nullptr), m_initialized(false) {
	CUresult result = cuInit(0);
	if (result != CUDA_SUCCESS) {
		const char* errStr = nullptr;
		cuGetErrorString(result, &errStr);
		throw std::runtime_error(std::string("CUDA initialization failed: ") + (errStr ? errStr : "Unknown error"));
	}
	
	result = cuCtxCreate(&m_context, flags, device);
	if (result != CUDA_SUCCESS) {
		const char* errStr = nullptr;
		cuGetErrorString(result, &errStr);
		throw std::runtime_error(std::string("CUDA context creation failed: ") + (errStr ? errStr : "Unknown error"));
	}
	
	result = cuCtxSetCurrent(m_context);
	if (result != CUDA_SUCCESS) {
		cuCtxDestroy(m_context); // Clean up on failure
		const char* errStr = nullptr;
		cuGetErrorString(result, &errStr);
		throw std::runtime_error(std::string("CUDA context set current failed: ") + (errStr ? errStr : "Unknown error"));
	}
	
	m_initialized = true;
}

CudaContextRAII::~CudaContextRAII() noexcept {
	if (m_context) {
		cuCtxDestroy(m_context);
	}
}

CudaContextRAII::CudaContextRAII(CudaContextRAII&& other) noexcept 
	: m_context(other.m_context), m_initialized(other.m_initialized) {
	other.m_context = nullptr;
	other.m_initialized = false;
}

CudaContextRAII& CudaContextRAII::operator=(CudaContextRAII&& other) noexcept {
	if (this != &other) {
		if (m_context)
			cuCtxDestroy(m_context);
		m_context = other.m_context;
		m_initialized = other.m_initialized;
		other.m_context = nullptr;
		other.m_initialized = false;
	}
	return *this;
}

void CudaContextRAII::setCurrent() {
	if (m_context) {
		CUresult result = cuCtxSetCurrent(m_context);
		if (result != CUDA_SUCCESS) {
			const char* errStr = nullptr;
			cuGetErrorString(result, &errStr);
			throw std::runtime_error(std::string("CUDA context set current failed: ") + (errStr ? errStr : "Unknown error"));
		}
	}
}

void CudaContextRAII::synchronize() {
	if (m_context) {
		CUresult result = cuCtxSynchronize();
		if (result != CUDA_SUCCESS) {
			const char* errStr = nullptr;
			cuGetErrorString(result, &errStr);
			throw std::runtime_error(std::string("CUDA context synchronization failed: ") + (errStr ? errStr : "Unknown error"));
		}
	}
}

CUcontext CudaContextRAII::release() noexcept {
	CUcontext context = m_context;
	m_context = nullptr;
	m_initialized = false;
	return context;
}

void CudaContextRAII::reset(CUcontext context) noexcept {
	if (m_context) {
		cuCtxDestroy(m_context);
	}
	m_context = context;
	m_initialized = (context != nullptr);
}

//=============================================================================
// CudaContextGuard Implementation
//=============================================================================

CudaContextGuard::CudaContextGuard(CUcontext context) : m_targetContext(context) {
	cuCtxGetCurrent(&m_previousContext);
	if (m_targetContext && m_targetContext != m_previousContext) {
		CUresult result = cuCtxSetCurrent(m_targetContext);
		if (result != CUDA_SUCCESS) {
			const char* errStr = nullptr;
			cuGetErrorString(result, &errStr);
			throw std::runtime_error(std::string("CUDA context guard set current failed: ") + (errStr ? errStr : "Unknown error"));
		}
	}
}

CudaContextGuard::~CudaContextGuard() noexcept {
	if (m_previousContext && m_previousContext != m_targetContext) {
		cuCtxSetCurrent(m_previousContext); // Don't check in destructor
	}
}
