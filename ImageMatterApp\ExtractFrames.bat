rem ffmpeg -i "./Videos/Input short.mp4"  -frames:v 1 original.jpg
rem ffmpeg -i initial_alpha_hw.mp4  -frames:v 1 initial.jpg
rem ffmpeg -i body_refined_alpha_hw.mp4  -frames:v 1 refined.jpg
ffmpeg -i "./Videos/Input short.mp4" -vf "select=eq(n\,1)" -frames:v 1 input1.jpg
ffmpeg -i initial_alpha_hw.mp4 -vf "select=eq(n\,1)" -frames:v 1 alpha1.jpg
ffmpeg -i head_alpha_0.mp4 -vf "select=eq(n\,1)" -frames:v 1 head1.jpg
ffmpeg -i "./Videos/Input short.mp4" -vf "select=eq(n\,2)" -frames:v 1 input2.jpg
ffmpeg -i initial_alpha_hw.mp4 -vf "select=eq(n\,2)" -frames:v 1 alpha2.jpg
ffmpeg -i head_alpha_0.mp4 -vf "select=eq(n\,2)" -frames:v 1 head2.jpg
pause
