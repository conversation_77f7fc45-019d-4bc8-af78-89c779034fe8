Identity=src\BackgroundEstimationKernels.cu
AdditionalCompilerOptions=
AdditionalCompilerOptions=
AdditionalDependencies=
AdditionalDeps=
AdditionalLibraryDirectories=
AdditionalOptions=--expt-relaxed-constexpr
AdditionalOptions=--expt-relaxed-constexpr
CodeGeneration=compute_75,sm_75
CodeGeneration=compute_75,sm_75
CompileOut=F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterLib\ImageMatterLib\x64\Debug\BackgroundEstimationKernels.cu.obj
CudaRuntime=Static
CudaToolkitCustomDir=
DebugInformationFormat=ProgramDatabase
DebugInformationFormat=ProgramDatabase
Defines=;IMAGEMATTERLIB_EXPORTS;_CRT_SECURE_NO_WARNINGS;_WINDLL;_UNICODE;UNICODE;
Emulation=false
EnableVirtualArchInFatbin=true
ExtensibleWholeProgramCompilation=false
FastCompile=Off
FastMath=false
GenerateLineInfo=false
GenerateRelocatableDeviceCode=false
GPUDebugInfo=false
GPUDebugInfo=false
HostDebugInfo=true
Include=;include_internal\FFmpegIO;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include;F:\VccLibs\ffmpeg\include;F:\VccLibs\onnxruntime\include;F:\VccLibs\TensorRT\include;;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include
Inputs=
InterleaveSourceInPTX=false
Keep=false
KeepDir=ImageMatterLib\x64\Debug
LinkOut=
MaxRegCount=0
NvccCompilation=compile
NvccPath=
Optimization=Od
Optimization=Od
PerformDeviceLink=
PerformDeviceLinkTimeOptimization=
PtxAsOptionV=false
RequiredIncludes=
Runtime=MDd
Runtime=MDd
RuntimeChecks=RTC1
RuntimeChecks=RTC1
SplitCompile=Default
SplitCompileCustomThreads=
TargetMachinePlatform=64
TargetMachinePlatform=64
TypeInfo=
TypeInfo=
UseHostDefines=true
UseHostInclude=true
UseHostLibraryDependencies=
UseHostLibraryDirectories=
Warning=W3
Warning=W3
