// D3D11VideoReader.h
#pragma once

#include <string>
#include <memory>
#include <vector>
#include <d3d11_1.h>
#include <dxgi1_2.h>
#include <cuda.h>
#include <cuda_runtime.h>
#include <cuda_d3d11_interop.h>
#include "Helpers.h"
#include "FFmpegIO/MultiVideoReader.h"

// Include AVRational definition
extern "C" {
#include <libavutil/rational.h>
}

// D3D11VideoReader: Wraps MultiVideoReader to provide Direct3D 11 texture output
// Reads video frames directly into ID3D11Texture2D resources using CUDA-D3D11 interop
class D3D11VideoReader {
public:
    // Initialize with a D3D11 device and video file path
    static std::shared_ptr<D3D11VideoReader> Create(
        ID3D11Device1* d3dDevice,
        const std::string& filePath);

    // Destructor
    ~D3D11VideoReader();

    // Seek to a specific time position (in seconds)
    bool Seek(double timeInSeconds);

    // Read the next original RGB frame into a D3D11 texture (must be DXGI_FORMAT_B8G8R8A8_UNORM format)
    // Returns the frame timestamp, or -1.0 on error/EOF
    double ReadFrame(ID3D11Texture2D* d3dTexture);

    // Read the next frame as BGRA into a D3D11 texture (must be DXGI_FORMAT_B8G8R8A8_UNORM format)
    // Returns the frame timestamp, or -1.0 on error/EOF
    double ReadTransparentFrame(ID3D11Texture2D* d3dTexture);

    // Get video properties
    int GetWidth() const { return m_width; }
    int GetHeight() const { return m_height; }
    double GetDuration() const { return m_duration; }
    AVRational GetFrameRate() const { return m_frameRate; }
    double GetFrameRateDouble() const { return m_frameRate.num / static_cast<double>(m_frameRate.den); }
    Helpers::TextureFormat GetTextureFormat() const { return m_textureFormat; }
    AVRational GetTimeBase() const { return m_timeBase; }
    double GetTimeBaseDouble() const { return m_timeBase.num / static_cast<double>(m_timeBase.den); }

    // Get underlying video stream format
    Helpers::TextureFormat GetVideoStreamFormat() const;

    // Close the reader and release resources
    void Close();

private:
    // Private constructor - use Create() to create instances
    D3D11VideoReader();

    // Initialize the reader with D3D11 device and video file
    bool Initialize(ID3D11Device1* d3dDevice, const std::string& filePath);

    // Register a D3D11 texture with CUDA for interop
    bool RegisterTextureWithCuda(ID3D11Texture2D* texture, cudaGraphicsResource_t& resource);

    // Helper to validate texture format and dimensions
    bool ValidateTexture(ID3D11Texture2D* texture, DXGI_FORMAT expectedFormat);

    // Helper to copy data from CUDA buffer to D3D11 texture via interop
    bool CopyToD3D11Texture(CUdeviceptr cudaBuffer, ID3D11Texture2D* d3dTexture, 
                            size_t dataSize, bool isRgba = false);
    
    // Helper to check if texture is shared and handle keyed mutex
    bool IsSharedTexture(ID3D11Texture2D* texture);
    bool AcquireKeyedMutex(ID3D11Texture2D* texture, UINT64 key, DWORD timeout);
    bool ReleaseKeyedMutex(ID3D11Texture2D* texture, UINT64 key);

private:
    // D3D11 resources
    ID3D11Device1* m_d3dDevice;
    ID3D11DeviceContext1* m_d3dContext;

    // CUDA resources
    CUcontext m_cudaContext;
    CUdevice m_cudaDevice;
    CUstream m_cudaStream;

    // Video reader
    std::shared_ptr<MultiVideoReader> m_videoReader;

    // Temporary buffers for frame data
    CUdeviceptr m_tempRgbBuffer;       // For RGB float data
    CUdeviceptr m_tempAlphaBuffer;     // For alpha float data
    CUdeviceptr m_tempBgraBuffer;      // For BGRA byte data
    size_t m_tempRgbBufferSize;
    size_t m_tempAlphaBufferSize;
    size_t m_tempBgraBufferSize;


    // Video properties
    int m_width;
    int m_height;
    double m_duration;
    AVRational m_frameRate;
    AVRational m_timeBase;
    Helpers::TextureFormat m_textureFormat;

    // State tracking
    bool m_isInitialized;
};
