#pragma once

#include <cuda.h>
#include <cstdint>

// Only the launchers used by DirectVideoReader/DirectVideoWriter are kept

// NV12 (byte) to planar float RGB
void launchByteNv12ToPlanarFloatRgb(unsigned char* nv12Data, float* rgbData, int width, int height, size_t nv12Pitch, CUstream stream);

// YUV420P (planar) to planar float RGB
void launchYuv420pToPlanarFloatRgb(const unsigned char* y_plane, const unsigned char* u_plane,	const unsigned char* v_plane, float* rgb_output, int width, int height, int y_stride, int u_stride, int v_stride, CUstream stream);

// Float alpha [0..1] <-> YUV420 (Y carries alpha)
bool LaunchFloatAlphaToYuv420(const float* d_alphaData, uint8_t* d_yuvData,
	int width, int height, int yPitch, int uvPitch, CUstream stream);

bool LaunchYuv420ToFloatAlpha(const uint8_t* d_yuvData, float* d_alphaData,
	int width, int height, int yPitch, CUstream stream);

// Planar float RGB [0..1] -> YUV420
bool LaunchPlanarFloatRgbToYuv420(const float* d_rgbData, uint8_t* d_yuvData,
	int width, int height, int yPitch, int uvPitch, CUstream stream);

// Combine planar float RGB and float alpha to interleaved byte BGRA
void launchCombinePlanarFloatRgbAlphaToInterleavedByteBgra(
	const float* d_rgbPlanar, const float* d_alpha,
	uint8_t* d_bgraInterleaved,
	int width, int height, CUstream stream);

// Convert planar float RGB to interleaved float RGBA (alpha = 1.0)
void launchPlanarFloatRgbToInterleavedFloatRgba(
	const float* d_rgbPlanar,
	float* d_rgbaInterleaved,
	int width, int height, CUstream stream);

// Convert planar float RGB to interleaved byte RGBA (alpha = 255)
void launchPlanarFloatRgbToInterleavedByteRgba(
	const float* d_rgbPlanar,
	uint8_t* d_rgbaInterleaved,
	int width, int height, CUstream stream);

// Convert planar float RGB to interleaved byte BGRA (alpha = 255)
void launchPlanarFloatRgbToInterleavedByteBgra(
	const float* d_rgbPlanar,
	uint8_t* d_bgraInterleaved,
	int width, int height, CUstream stream);

// Convert float alpha [0-1] to byte alpha [0-255]
void launchFloatAlphaToByteAlpha(
	const float* d_floatAlpha,
	uint8_t* d_byteAlpha,
	int width, int height, CUstream stream);
