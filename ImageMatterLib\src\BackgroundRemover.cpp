#include "ImageMatterLib.h"
#include <string>
#include <memory>
#include <cstring>

#include "VideoBackgroundRemover.h"

	extern "C" {

	// VideoBackgroundRemover C interface implementations

	EngineType ConvertEngineType(EngineTypeExport exportEngine) {
		switch (exportEngine) {
		case ENGINE_ONNX_EXPORT:
			return ENGINE_ONNX;
		case ENGINE_TENSORRT_EXPORT:
			return ENGINE_TENSORRT;
		case ENGINE_AUTO_EXPORT:
			return ENGINE_AUTO;
		default:
			return ENGINE_AUTO;
		}
	}

	int RemoveBackground(
		const wchar_t* inputVideo,
		const wchar_t* outputVideo,
		EngineTypeExport engine,
		ProgressCallbackExport progressCb,
		void* userData) {
		if (!inputVideo || !outputVideo)
			return 1;

		try {
			// Convert wchar_t* to std::wstring
			std::wstring inputPath(inputVideo); 
			std::wstring outputPath(outputVideo);

			// Convert engine type and callback
			EngineType internalEngine = ConvertEngineType(engine);
			ProgressCallback internalCallback = reinterpret_cast<ProgressCallback>(progressCb);

			// Call the internal implementation
			VideoBackgroundRemover videoRemover;
			return videoRemover.Process(inputPath, outputPath, internalEngine, internalCallback, userData);
		}
		catch (...) {
			return 1;
		}
	}
}