#pragma once

#include <string>
#include <memory>
#include <cuda.h>
#include "CodecConfig.h"
#include "FFmpegIO/FFmpegIOKernels.cuh"


extern "C" {
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libavutil/hwcontext.h>
#include <libavutil/hwcontext_cuda.h>
#include <libavutil/rational.h>
}

class DirectVideoWriter {
public:
    // Factory method to create and initialize a DirectVideoWriter
    static std::shared_ptr<DirectVideoWriter> Create(
        const std::string& outputPath,
        const CodecConfig& config,
        CUcontext cudaContext
    );

    ~DirectVideoWriter();

    // Write a frame with grayscale data (for alpha channels)
    // d_grayData: CUDA device pointer to grayscale float data [0.0-1.0]
    bool WriteGrayFrame(const float* d_grayData, CUstream stream = 0);

    // Write a frame with RGB data  
    // d_rgbData: CUDA device pointer to planar float RGB data [0.0-1.0]
    bool WriteRgbFrame(const float* d_rgbData, CUstream stream = 0);

    // Finalize encoding and close the file
    bool Finalize();

    // Getters
    int GetWidth() const { return m_width; }
    int GetHeight() const { return m_height; }
    AVRational GetFrameRate() const { return m_frameRate; }
    bool IsHardwareAccelerated() const { return true; }

private:
    DirectVideoWriter();

    // New initialize accepting full AlphaCodecConfig
    bool Initialize(
        const std::string& outputPath,
        const CodecConfig& config,
        CUcontext cudaContext
    );

    void Cleanup();

    // Helper function to prepare frame with proper PTS
    bool PrepareFrame();

    // Internal encoding function
    bool EncodeFrame(AVFrame* frame);
    
    // Create hardware frame from CUDA data
    AVFrame* CreateHardwareFrame(const void* d_data, bool isGray, CUstream stream);

private:
    // FFmpeg contexts
    AVFormatContext* m_formatContext;
    AVCodecContext* m_codecContext;
    AVStream* m_stream;
    AVPacket* m_packet;
    AVFrame* m_hwFrame;
    
    // Hardware acceleration
    AVBufferRef* m_hwDeviceCtx;
    AVBufferRef* m_hwFramesCtx;
    
    // CUDA context
    CUcontext m_cudaContext;
    CUstream m_cudaStream;
    
    // Internal GPU buffers for format conversion (YUV420 for alpha storage: Y=alpha, U=V=128)
    CUdeviceptr m_internalYuv420Buffer;
    size_t m_internalYuv420BufferSize;
    int m_internalYuv420YPitch;
    int m_internalYuv420UVPitch;
    
    // Video properties
    int m_width;
    int m_height;
    AVRational m_frameRate;
    std::string m_codec;
    std::string m_outputPath;
    
    // Status flags
    bool m_isInitialized;
    bool m_isFinalized;
    
    // Frame counter
    int64_t m_frameIndex;
};
