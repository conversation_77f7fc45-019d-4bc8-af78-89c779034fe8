#pragma once

#define NOMINMAX // Disable the min/max macros from windows.h which interfere with std::min/max
#include <Windows.h>
#include <cuda.h>
#include "Matting_Kernels.cuh"


/**
 * Enumeration of resize methods for model input preprocessing
 */
enum class ResizeMethod {
    EXTEND_SHRINK_LANCZOS,      // Extend or shrink image as needed in each direction using Lanczos
    STRETCH_ASPECT_RATIO_PAD    // Stretch using Lanczos while keeping aspect ratio and pad with blank pixels
};

/**
 * Abstract base class for image matting implementations
 * Provides a common interface for both ONNX Runtime and TensorRT implementations
 */
class ImageMatting {
public:
    ImageMatting() = default;
    virtual ~ImageMatting() = default;

    /**
     * Initialize the image matting with a model file
     * Uses CUDA resources for operations and allocates input/output buffers
     * @param modelPath Path to the model file (.onnx for ONNX Runtime, .trt/.engine for TensorRT)
     * @param modelWidth Width of the model input (required)
     * @param modelHeight Height of the model input (required)
     * @param imageWidth Width of the images that will be processed
     * @param imageHeight Height of the images that will be processed
     * @param normParams Normalization parameters for preprocessing (mean and std values)
     * @param isRgba Whether the input buffer will be in RGBA format (true) or RGB format (false)
     * @param externalStream CUDA stream to use for operations
     * @return True on success, false on failure
     */
    virtual bool Init(
        const wchar_t* modelPath,
        int modelWidth,
        int modelHeight,
        int imageWidth,
        int imageHeight,
        const NormalizationParams& normParams,
        bool isRgba = false,
        ResizeMethod resizeMethod = ResizeMethod::EXTEND_SHRINK_LANCZOS,
        CUstream externalStream = nullptr) = 0;

    /**
     * Run inference on the current loaded model
     * Performs inference using internally allocated CUDA buffers
     * The caller should copy data to the input buffer (obtained via GetInputBuffer())
     * and read results from the output buffer (obtained via GetOutputBuffer()) after inference
     * @return True on success, false on failure
     */
    virtual bool Infer() = 0;

    /**
     * Get pointer to the input buffer for copying image data
     * @return CUDA device pointer to the input buffer (RGB/RGBA float format)
     */
    virtual CUdeviceptr GetInputBuffer() const = 0;

    /**
     * Get pointer to the output buffer for reading alpha mask results
     * @return CUDA device pointer to the output buffer (single channel float format)
     */
    virtual CUdeviceptr GetOutputBuffer() const = 0;

    /**
     * Get the size of the input buffer in bytes
     * @return Size of input buffer in bytes
     */
    virtual size_t GetInputBufferSize() const = 0;

    /**
     * Get the size of the output buffer in bytes
     * @return Size of output buffer in bytes
     */
    virtual size_t GetOutputBufferSize() const = 0;

    /**
     * Shutdown and release all resources
     */
    virtual void Shutdown() = 0;

    /**
     * Check if the instance is initialized
     * @return True if initialized, false otherwise
     */
    virtual bool IsInitialized() const = 0;

    // Getter functions for model dimensions
    virtual int GetModelWidth() const = 0;
    virtual int GetModelHeight() const = 0;

    // Getter functions for image dimensions
    virtual int GetImageWidth() const = 0;
    virtual int GetImageHeight() const = 0;

    // Get the number of channels in the input format
    virtual int GetInputChannels() const = 0;

    // Get the CUDA stream for synchronization if needed
    virtual CUstream GetCudaStream() const = 0;

    /**
     * Get the implementation type as a string for debugging/logging
     * @return String identifying the implementation ("ONNX" or "TensorRT")
     */
    virtual const char* GetImplementationType() const = 0;

protected:
    // Common helper functions that can be used by derived classes

    /**
     * Common preprocessing implementation used by both ONNX and TensorRT
     * Handles normalization and resizing if needed
     * @param inputBuffer Input buffer at original image size
     * @param preprocessedBuffer Temporary buffer for preprocessing at original size
     * @param outputBuffer Output buffer at model input size
     * @param imageWidth Original image width
     * @param imageHeight Original image height
     * @param modelWidth Model input width
     * @param modelHeight Model input height
     * @param isRgba Whether input is RGBA format
     * @param normParams Normalization parameters
     * @param preprocessedBufferSize Size of preprocessed buffer
     * @param resizeMethod Method to use for resizing (extend/shrink or stretch with aspect ratio)
     * @param stream CUDA stream for operations
     * @return CUDA_SUCCESS on success, error code on failure
     */
    CUresult PreprocessInputBufferCommon(
        const CUdeviceptr inputBuffer,
        CUdeviceptr preprocessedBuffer,
        CUdeviceptr outputBuffer,
        int imageWidth,
        int imageHeight,
        int modelWidth,
        int modelHeight,
        bool isRgba,
        const NormalizationParams& normParams,
        size_t preprocessedBufferSize,
        ResizeMethod resizeMethod,
        CUstream stream);

    /**
     * Common postprocessing implementation used by both ONNX and TensorRT
     * Handles resizing from model output size to original image size
     * @param modelOutput Output buffer from model at model resolution
     * @param outputBuffer Final output buffer at original image resolution
     * @param modelWidth Model output width
     * @param modelHeight Model output height
     * @param imageWidth Original image width
     * @param imageHeight Original image height
     * @param resizeMethod Method used for resizing (needed for proper reverse transformation)
     * @param stream CUDA stream for operations
     * @return CUDA_SUCCESS on success, error code on failure
     */
    CUresult PostprocessOutputBufferCommon(
        const CUdeviceptr modelOutput,
        CUdeviceptr outputBuffer,
        int modelWidth,
        int modelHeight,
        int imageWidth,
        int imageHeight,
        ResizeMethod resizeMethod,
        CUstream stream);
};
