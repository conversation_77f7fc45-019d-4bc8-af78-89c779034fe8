// NV12DirectVideoReader.cpp
#include "FFmpegIO/NV12DirectVideoReader.h"
#include "Helpers.h"
#include <iostream>
#include <stdexcept>

extern "C" {
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libavutil/hwcontext.h>
#include <libavutil/hwcontext_cuda.h>
#include <libavutil/pixdesc.h>
#include <libavutil/opt.h>
#include <libavutil/imgutils.h>
}

// Helper function to check FFmpeg errors
static void CheckFFmpegError(int ret, const std::string& operation = "") {
    if (ret < 0) {
        char errorStr[AV_ERROR_MAX_STRING_SIZE];
        av_strerror(ret, errorStr, AV_ERROR_MAX_STRING_SIZE);
        std::string message = "FFmpeg error";
        if (!operation.empty()) {
            message += " in " + operation;
        }
        message += ": " + std::string(errorStr);
        throw std::runtime_error(message);
    }
}

NV12DirectVideoReader::NV12DirectVideoReader() : m_formatContext(nullptr),
                                                 m_codecContext(nullptr),
                                                 m_hwFrame(nullptr),
                                                 m_packet(nullptr),
                                                 m_videoStreamIndex(-1),
                                                 m_isHardwareAccelerated(true),
                                                 m_cudaContext(nullptr),
                                                 m_cudaStream(nullptr),
                                                 m_internalNv12Buffer(0),
                                                 m_internalNv12BufferSize(0),
                                                 m_internalNv12Pitch(0),
                                                 m_yuv420pConversionBuffer(0),
                                                 m_yuv420pConversionBufferSize(0),
                                                 m_width(0),
                                                 m_height(0),
                                                 m_duration(0.0),
                                                 m_frameRate({ 0, 1 }),
                                                 m_timeBase({ 0, 1 }),
                                                 m_textureFormat(Helpers::TextureFormat::Unknown),
                                                 m_sourceFormat(Helpers::TextureFormat::Unknown),
                                                 m_currentFrameTimestamp(0),
                                                 m_isInitialized(false),
                                                 m_isEof(false) {
}

NV12DirectVideoReader::~NV12DirectVideoReader() {
    Close();
}

std::shared_ptr<NV12DirectVideoReader> NV12DirectVideoReader::Create(
    const std::string& filePath,
    CUcontext cudaContext) {

    std::shared_ptr<NV12DirectVideoReader> reader(new NV12DirectVideoReader());

    if (!reader->Initialize(filePath, cudaContext)) {
        return nullptr;
    }

    return reader;
}

Helpers::TextureFormat NV12DirectVideoReader::GetVideoStreamFormat() const {
    if (!m_isInitialized)
        return Helpers::TextureFormat::Unknown;
    return m_sourceFormat;
}

bool NV12DirectVideoReader::Initialize(const std::string& filePath, CUcontext cudaContext) {
    try {
        // Store CUDA context
        m_cudaContext = cudaContext;

        // Set current CUDA context
        CheckCudaError(cuCtxSetCurrent(m_cudaContext));

        // Open input file
        CheckFFmpegError(avformat_open_input(&m_formatContext, filePath.c_str(), nullptr, nullptr), "opening input file");

        // Find stream information
        CheckFFmpegError(avformat_find_stream_info(m_formatContext, nullptr), "finding stream info");

        // Find the video stream
        m_videoStreamIndex = av_find_best_stream(m_formatContext, AVMEDIA_TYPE_VIDEO, -1, -1, nullptr, 0);
        if (m_videoStreamIndex < 0) {
            throw std::runtime_error("No video stream found");
        }

        AVStream* videoStream = m_formatContext->streams[m_videoStreamIndex];
        const AVCodec* decoder = avcodec_find_decoder(videoStream->codecpar->codec_id);
        if (!decoder) {
            throw std::runtime_error("Decoder not found");
        }

        // Create codec context
        m_codecContext = avcodec_alloc_context3(decoder);
        if (!m_codecContext) {
            throw std::runtime_error("Could not allocate codec context");
        }

        // Copy codec parameters
        CheckFFmpegError(avcodec_parameters_to_context(m_codecContext, videoStream->codecpar), "copying codec parameters");

        // Store video properties
        m_width = m_codecContext->width;
        m_height = m_codecContext->height;
        m_frameRate = videoStream->r_frame_rate;
        m_timeBase = videoStream->time_base;
        m_duration = (double)m_formatContext->duration / AV_TIME_BASE;

        // Determine source format
        switch (m_codecContext->pix_fmt) {
            case AV_PIX_FMT_YUV420P:
                m_sourceFormat = Helpers::TextureFormat::YUV420P;
                break;
            case AV_PIX_FMT_NV12:
                m_sourceFormat = Helpers::TextureFormat::NV12;
                break;
            default:
                m_sourceFormat = Helpers::TextureFormat::Unknown;
                break;
        }

        // Set up hardware acceleration for CUDA
        AVBufferRef* hwDeviceCtx = nullptr;
        CheckFFmpegError(av_hwdevice_ctx_create(&hwDeviceCtx, AV_HWDEVICE_TYPE_CUDA, nullptr, nullptr, 0), "creating CUDA device context");

        // Set hardware device context
        m_codecContext->hw_device_ctx = av_buffer_ref(hwDeviceCtx);

        // Set NV12 as the target texture format (this refers to the decoder's output, not necessarily ReadFrame's final output)
        m_textureFormat = Helpers::TextureFormat::NV12;

        // Create CUDA stream within the provided context
        CheckCudaError(cuStreamCreate(&m_cudaStream, 0));

        // Allocate internal NV12 buffer for format conversion
        // Use proper pitch alignment for optimal memory access
        m_internalNv12Pitch = ((m_width + 255) / 256) * 256;           // Align to 256 bytes
        m_internalNv12BufferSize = m_internalNv12Pitch * m_height * 3 / 2; // NV12 with pitch
        CheckCudaError(cuMemAlloc(&m_internalNv12Buffer, m_internalNv12BufferSize));

        // Allocate YUV420P conversion buffer if needed
        if (m_sourceFormat == Helpers::TextureFormat::YUV420P) {
            m_yuv420pConversionBufferSize = m_width * m_height * 3 / 2; // YUV420P is 1.5 bytes per pixel
            CheckCudaError(cuMemAlloc(&m_yuv420pConversionBuffer, m_yuv420pConversionBufferSize));
        }

        // Allocate frames and packet
        m_hwFrame = av_frame_alloc();
        m_packet = av_packet_alloc();

        if (!m_hwFrame || !m_packet) {
            throw std::runtime_error("Could not allocate frame or packet");
        }

        // Open codec
        CheckFFmpegError(avcodec_open2(m_codecContext, decoder, nullptr), "opening codec");

        // Clean up hardware device context
        if (hwDeviceCtx) {
            av_buffer_unref(&hwDeviceCtx);
        }

        m_isInitialized = true;
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Error initializing NV12DirectVideoReader: " << e.what() << std::endl;
        Close();
        return false;
    }
}

bool NV12DirectVideoReader::Seek(double timeInSeconds) {
    if (!m_isInitialized) {
        return false;
    }

    try {
        // Convert time to stream time base
        int64_t timestamp = (int64_t)(timeInSeconds / av_q2d(m_timeBase));

        // Seek to the timestamp
        CheckFFmpegError(av_seek_frame(m_formatContext, m_videoStreamIndex, timestamp, AVSEEK_FLAG_BACKWARD), "seeking");

        // Flush codec buffers
        avcodec_flush_buffers(m_codecContext);

        m_isEof = false;
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Error seeking: " << e.what() << std::endl;
        return false;
    }
}

void NV12DirectVideoReader::Close() {
    // Free CUDA resources
    if (m_internalNv12Buffer) {
        cuMemFree(m_internalNv12Buffer);
        m_internalNv12Buffer = 0;
    }
    if (m_yuv420pConversionBuffer) {
        cuMemFree(m_yuv420pConversionBuffer);
        m_yuv420pConversionBuffer = 0;
    }
    if (m_cudaStream) {
        cuStreamDestroy(m_cudaStream);
        m_cudaStream = nullptr;
    }

    // Free FFmpeg resources
    if (m_hwFrame) {
        av_frame_free(&m_hwFrame);
    }
    if (m_packet) {
        av_packet_free(&m_packet);
    }
    if (m_codecContext) {
        avcodec_free_context(&m_codecContext);
    }
    if (m_formatContext) {
        avformat_close_input(&m_formatContext);
    }

    m_isInitialized = false;
    m_isEof = false;
}

double NV12DirectVideoReader::ConvertTimestampToSeconds(int64_t timestamp) const {
    return (double)timestamp * av_q2d(m_timeBase);
}

bool NV12DirectVideoReader::DecodePacket() {
    if (!m_isInitialized) {
        return false;
    }

    // Keep reading until we get a frame or reach EOF
    while (true) {
        int ret = av_read_frame(m_formatContext, m_packet);

        if (ret < 0) {
            // End of file or error
            if (ret == AVERROR_EOF) {
                m_isEof = true;
            }
            av_packet_unref(m_packet);
            return false;
        }

        // Skip non-video packets
        if (m_packet->stream_index != m_videoStreamIndex) {
            av_packet_unref(m_packet);
            continue;
        }

        // Send packet to decoder
        ret = avcodec_send_packet(m_codecContext, m_packet);

        // Unref packet as we don't need it anymore
        av_packet_unref(m_packet);

        if (ret < 0) {
            // Error sending packet
            return false;
        }

        // Get decoded frame
        ret = avcodec_receive_frame(m_codecContext, m_hwFrame);

        if (ret == AVERROR(EAGAIN)) {
            // Need more packets
            continue;
        }

        if (ret < 0) {
            // Error or EOF
            return false;
        }

        // Successfully decoded a frame
        m_currentFrameTimestamp = m_hwFrame->pts;
        return true;
    }
}

// ReadFrame implementation - returns NV12 data and the frame timestamp
double NV12DirectVideoReader::ReadFrame(CUdeviceptr cudaNv12Buffer, size_t bufferSize, size_t* nv12Pitch, CUstream stream) {
    if (!m_isInitialized) {
        return -1.0;
    }

    // Decode a new frame
    if (!DecodePacket()) {
        return -1.0;
    }

    // Calculate expected buffer size for NV12 (1.5 bytes per pixel)
    size_t expectedNv12Size = m_width * m_height * 3 / 2;
    if (bufferSize < expectedNv12Size) {
        std::cerr << "Output buffer too small for NV12 data. Expected: " << expectedNv12Size << ", Got: " << bufferSize << std::endl;
        return -1.0;
    }

    AVPixelFormat pixFormat = static_cast<AVPixelFormat>(m_hwFrame->format);

    try {
        if (pixFormat == AV_PIX_FMT_YUV420P) {
            // Case: Decoded frame is YUV420P. Convert to NV12.
            // Determine source pointers and strides based on whether the frame is on CPU or GPU.
            CUdeviceptr src_y_plane;
            CUdeviceptr src_u_plane;
            CUdeviceptr src_v_plane;
            int src_y_stride;
            int src_u_stride;
            int src_v_stride;

            if (!m_hwFrame->hw_frames_ctx) {
                // Frame is on CPU. Need to copy to GPU first.
                // Copy Y plane
                CUDA_MEMCPY2D m = { 0 };
                m.srcMemoryType = CU_MEMORYTYPE_HOST;
                m.srcHost = m_hwFrame->data[0];
                m.srcPitch = m_hwFrame->linesize[0];
                m.dstMemoryType = CU_MEMORYTYPE_DEVICE;
                m.dstDevice = m_yuv420pConversionBuffer;
                m.dstPitch = m_width;
                m.WidthInBytes = m_width;
                m.Height = m_height;
                CheckCudaError(cuMemcpy2DAsync(&m, stream ? stream : m_cudaStream));

                // Copy U plane
                m.srcHost = m_hwFrame->data[1];
                m.srcPitch = m_hwFrame->linesize[1];
                m.dstDevice = m_yuv420pConversionBuffer + m_width * m_height;
                m.dstPitch = m_width / 2;
                m.WidthInBytes = m_width / 2;
                m.Height = m_height / 2;
                CheckCudaError(cuMemcpy2DAsync(&m, stream ? stream : m_cudaStream));

                // Copy V plane
                m.srcHost = m_hwFrame->data[2];
                m.srcPitch = m_hwFrame->linesize[2];
                m.dstDevice = m_yuv420pConversionBuffer + m_width * m_height + (m_width / 2) * (m_height / 2);
                m.dstPitch = m_width / 2;
                m.WidthInBytes = m_width / 2;
                m.Height = m_height / 2;
                CheckCudaError(cuMemcpy2DAsync(&m, stream ? stream : m_cudaStream));

                // Set source pointers and strides to the GPU conversion buffer
                src_y_plane = m_yuv420pConversionBuffer;
                src_u_plane = m_yuv420pConversionBuffer + m_width * m_height;
                src_v_plane = m_yuv420pConversionBuffer + m_width * m_height + (m_width / 2) * (m_height / 2);
                src_y_stride = m_width;
                src_u_stride = m_width / 2;
                src_v_stride = m_width / 2;
            } else {
                // Frame is already on GPU
                src_y_plane = (CUdeviceptr)m_hwFrame->data[0];
                src_u_plane = (CUdeviceptr)m_hwFrame->data[1];
                src_v_plane = (CUdeviceptr)m_hwFrame->data[2];
                src_y_stride = m_hwFrame->linesize[0];
                src_u_stride = m_hwFrame->linesize[1];
                src_v_stride = m_hwFrame->linesize[2];
            }

            // Convert YUV420P to NV12
            launchYuv420pToNv12(
                (const unsigned char*)src_y_plane,
                (const unsigned char*)src_u_plane,
                (const unsigned char*)src_v_plane,
                (unsigned char*)cudaNv12Buffer,
                m_width,
                m_height,
                src_y_stride,
                src_u_stride,
                src_v_stride,
                m_width, // Use width as pitch for output NV12
                stream ? stream : m_cudaStream);

            if (nv12Pitch) {
                *nv12Pitch = m_width;
            }
        }
        else if (pixFormat == AV_PIX_FMT_CUDA) {
            // Case: Decoded frame is on GPU in NV12 format (common for hardware acceleration).
            // Copy the NV12 data directly from m_hwFrame->data to output buffer.

            // Copy Y plane (luma)
            CUDA_MEMCPY2D m = { 0 };
            m.srcMemoryType = CU_MEMORYTYPE_DEVICE;
            m.srcDevice = (CUdeviceptr)m_hwFrame->data[0];
            m.srcPitch = m_hwFrame->linesize[0];
            m.dstMemoryType = CU_MEMORYTYPE_DEVICE;
            m.dstDevice = cudaNv12Buffer;
            m.dstPitch = m_width;
            m.WidthInBytes = m_width;
            m.Height = m_height;
            CheckCudaError(cuMemcpy2DAsync(&m, stream ? stream : m_cudaStream));

            // Copy UV plane (chroma)
            m.srcDevice = (CUdeviceptr)m_hwFrame->data[1];
            m.srcPitch = m_hwFrame->linesize[1];
            m.dstDevice = cudaNv12Buffer + m_width * m_height;
            m.dstPitch = m_width;
            m.WidthInBytes = m_width;
            m.Height = m_height / 2;
            CheckCudaError(cuMemcpy2DAsync(&m, stream ? stream : m_cudaStream));

            if (nv12Pitch) {
                *nv12Pitch = m_width;
            }
        }
        else {
            // Handle other pixel formats or error out
            std::cerr << "Unsupported pixel format for ReadFrame: " << av_get_pix_fmt_name(pixFormat) << std::endl;
            return -1.0;
        }

        // Synchronize to ensure all CUDA operations are complete before returning
        if (stream) {
            CheckCudaError(cuStreamSynchronize(stream));
        } else {
            CheckCudaError(cuStreamSynchronize(m_cudaStream));
        }

        // Return the frame timestamp in seconds
        return ConvertTimestampToSeconds(m_currentFrameTimestamp);
    }
    catch (const std::exception& e) {
        std::cerr << "Error in ReadFrame: " << e.what() << std::endl;
        return -1;
    }
}
