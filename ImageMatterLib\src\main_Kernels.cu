#include "main_Kernels.cuh"
#include "KernelManager.h"
#include <cuda_runtime.h>
#include <iostream>
#include <device_launch_parameters.h>

#ifdef __CUDACC__

// Forward declarations for the kernels
extern "C" {
    __global__ void DetectUncertainRegionsKernel(float* alphaBuffer, unsigned char* alphaRegionsBuffer, int width, int height, int regionSize, const Box* headBoxes, int headCount);
    __global__ void generateTrimapKernel(float* outputTrimap, const float* inputAlpha, int width, int height);
    __global__ void prepareIndexNetInputKernel(float* outputBuffer, const float* inputRgbBuffer, const float* inputTrimap, int x, int y, int width, int height, int modelSizeX, int modelSizeY, int padding);
    __global__ void extractRegionKernel(const float* inputBuffer, float* outputBuffer, int inputWidth, int inputHeight, int regionX, int regionY, int regionWidth, int regionHeight, int channels);
    __global__ void combineAlphaRegionKernel(float* mainAlpha, const float* regionAlpha, int mainWidth, int mainHeight, int regionX, int regionY, int regionWidth, int regionHeight);
    __global__ void resizeAlphaKernel(const float* inputAlpha, float* outputAlpha, int inputWidth, int inputHeight, int outputWidth, int outputHeight);
}

// Kernel implementations

extern "C" __global__ void DetectUncertainRegionsKernel(float* alphaBuffer, unsigned char* alphaRegionsBuffer, int width, int height, int regionSize, const Box* headBoxes, int headCount) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int regionIdx = blockIdx.y; // Since we're using 1D blocks, regionIdx is just blockIdx.y

	int numberOfRowsInRegion = (height + regionSize - 1) / regionSize;

	if (x >= width || regionIdx >= numberOfRowsInRegion)
		return;

	// Initialize flags for detection
	bool hasUncertainPixel = false;

	// Calculate region boundaries
	int baseY = regionIdx * regionSize;

	int startY = max(0, baseY);
	int endY = min(height, startY + regionSize);

	for (int y = startY; y < endY; y++) {
		// Skip pixels inside any head box (Box uses top-left x,y with width,height)
		bool insideHead = false;
		for (int h = 0; h < headCount; ++h) {
			Box hb = headBoxes[h];
			if (hb.width <= 0.0f || hb.height <= 0.0f) continue;
			float left = hb.x;
			float top = hb.y;
			float right = hb.x + hb.width;
			float bottom = hb.y + hb.height;
			if (x >= (int)left && x < (int)right && y >= (int)top && y < (int)bottom) {
				insideHead = true;
				break;
			}
		}
		if (insideHead) continue;

		float alphaValue = alphaBuffer[y * width + x];
		if (alphaValue > 0.1f && alphaValue < 0.9f) {
			hasUncertainPixel = true;
			break;
		}
	}

	// Write result to output buffer
	int outputIdx = regionIdx * width + x;
	if (outputIdx < width * numberOfRowsInRegion) {
		alphaRegionsBuffer[outputIdx] = hasUncertainPixel ? 255 : 0;
	}
}

extern "C" __global__ void generateTrimapKernel(float* outputTrimap, const float* inputAlpha, int width, int height) {
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;
    
    if (x >= width || y >= height) return;
    
    int idx = y * width + x;
    float alpha = inputAlpha[idx];
    
    // Initial classification
    float trimap = 128.0f; // Unknown by default
    if (alpha <= 0.1f) trimap = 0.0f; // Background
    else if (alpha >= 0.9f) trimap = 255.0f; // Foreground
    
    // Expand unknown region by checking neighbors
    bool hasTransition = false;
    if (x > 0 && x < width-1 && y > 0 && y < height-1) {
        for (int dy = -1; dy <= 1; dy++) {
            for (int dx = -1; dx <= 1; dx++) {
                if (dx == 0 && dy == 0) continue;
                int neighborIdx = (y + dy) * width + (x + dx);
                float neighborAlpha = inputAlpha[neighborIdx];
                if (fabsf(alpha - neighborAlpha) > 0.3f) {
                    hasTransition = true;
                    break;
                }
            }
            if (hasTransition) break;
        }
    }
    
    if (hasTransition) {
        trimap = 128.0f; // Mark as unknown
    }
    
    outputTrimap[idx] = trimap / 255.0f; // Normalize to [0,1]
}

extern "C" __global__ void prepareIndexNetInputKernel(float* outputBuffer, const float* inputRgbBuffer,
                                                     const float* inputTrimap, int x, int y, int width, int height,
                                                     int modelSizeX, int modelSizeY, int padding) {
    int tx = blockIdx.x * blockDim.x + threadIdx.x;
    int ty = blockIdx.y * blockDim.y + threadIdx.y;
    
    if (tx >= modelSizeX || ty >= modelSizeY) return;
    
    int srcX = x + tx - padding;
    int srcY = y + ty - padding;
    
    // Handle boundary conditions with clamping
    srcX = max(0, min(width - 1, srcX));
    srcY = max(0, min(height - 1, srcY));
    
    int srcIdx = srcY * width + srcX;
    int dstIdx = ty * modelSizeX + tx;
    
    // Copy RGB channels (assumed to be in planar format)
    outputBuffer[dstIdx] = inputRgbBuffer[srcIdx];
    outputBuffer[dstIdx + modelSizeX * modelSizeY] = inputRgbBuffer[srcIdx + width * height];
    outputBuffer[dstIdx + 2 * modelSizeX * modelSizeY] = inputRgbBuffer[srcIdx + 2 * width * height];
    
    // Copy trimap as 4th channel
    outputBuffer[dstIdx + 3 * modelSizeX * modelSizeY] = inputTrimap[srcIdx];
}

extern "C" __global__ void extractRegionKernel(const float* inputBuffer, float* outputBuffer,
                                              int inputWidth, int inputHeight,
                                              int regionX, int regionY, int regionWidth, int regionHeight,
                                              int channels) {
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;
    
    if (x >= regionWidth || y >= regionHeight) return;
    
    int srcX = regionX + x;
    int srcY = regionY + y;
    
    if (srcX >= inputWidth || srcY >= inputHeight) return;
    
    for (int c = 0; c < channels; c++) {
        int srcIdx = c * inputWidth * inputHeight + srcY * inputWidth + srcX;
        int dstIdx = c * regionWidth * regionHeight + y * regionWidth + x;
        outputBuffer[dstIdx] = inputBuffer[srcIdx];
    }
}

extern "C" __global__ void combineAlphaRegionKernel(float* mainAlpha, const float* regionAlpha,
                                                 int mainWidth, int mainHeight,
                                                 int regionX, int regionY, int regionWidth, int regionHeight) {
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;
    
    if (x >= regionWidth || y >= regionHeight) return;
    
    int mainX = regionX + x;
    int mainY = regionY + y;
    
    if (mainX >= mainWidth || mainY >= mainHeight) return;
    
    int mainIdx = mainY * mainWidth + mainX;
    int regionIdx = y * regionWidth + x;
    
    mainAlpha[mainIdx] = regionAlpha[regionIdx];
}

// Kernel to resize alpha with bilinear interpolation
extern "C" __global__ void resizeAlphaKernel(const float* inputAlpha, float* outputAlpha,
                                          int inputWidth, int inputHeight,
                                          int outputWidth, int outputHeight) {
    int x = blockIdx.x * blockDim.x + threadIdx.x;
    int y = blockIdx.y * blockDim.y + threadIdx.y;
    
    if (x >= outputWidth || y >= outputHeight) return;
    
    // Calculate source coordinates
    float scaleX = (float)inputWidth / (float)outputWidth;
    float scaleY = (float)inputHeight / (float)outputHeight;
    
    float srcX = x * scaleX;
    float srcY = y * scaleY;
    
    // Get integer and fractional parts
    int x0 = (int)srcX;
    int y0 = (int)srcY;
    int x1 = min(x0 + 1, inputWidth - 1);
    int y1 = min(y0 + 1, inputHeight - 1);
    
    float fx = srcX - x0;
    float fy = srcY - y0;
    
    // Bilinear interpolation
    float v00 = inputAlpha[y0 * inputWidth + x0];
    float v10 = inputAlpha[y0 * inputWidth + x1];
    float v01 = inputAlpha[y1 * inputWidth + x0];
    float v11 = inputAlpha[y1 * inputWidth + x1];
    
    float v0 = v00 * (1.0f - fx) + v10 * fx;
    float v1 = v01 * (1.0f - fx) + v11 * fx;
    float result = v0 * (1.0f - fy) + v1 * fy;
    
    outputAlpha[y * outputWidth + x] = result;
}

// Kernel to update alpha buffer using trimap and improved alpha
extern "C" __global__ void updateAlphaBufferRegionKernelTexture(
    float* outputAlphaBuffer,
    CUtexObject originalAlphaTexture,
    const float* trimapBuffer,
    const float* improvedAlpha,
    int x, int y, int width, int height, int modelSize, int padding) {
    
    int tx = blockIdx.x * blockDim.x + threadIdx.x;
    int ty = blockIdx.y * blockDim.y + threadIdx.y;
	const int effectiveSize = modelSize - 2 * padding;

	// Early bounds check - single condition
	if (tx >= effectiveSize || ty >= effectiveSize)
		return;

    int globalX = x + tx;
    int globalY = y + ty;

    if (globalX >= width || globalY >= height) return;

    int globalIdx = globalY * width + globalX;

    // Get trimap value
    float trimap = trimapBuffer[globalIdx];
    
    // Only update unknown regions (trimap ~= 0.5)
    if (trimap > 0.4f && trimap < 0.6f) {
        int localIdx = (ty + padding) * modelSize + (tx + padding);
        outputAlphaBuffer[globalIdx] = improvedAlpha[localIdx];
    } else {
        // Keep original alpha value by reading from texture
        float2 coords;
        coords.x = (float)globalX + 0.5f;
        coords.y = (float)globalY + 0.5f;
        outputAlphaBuffer[globalIdx] = tex2D<float>(originalAlphaTexture, coords.x, coords.y);
    }
}

#endif // __CUDACC__

// Launcher implementations

void launchDetectUncertainRegions(float* alphaBuffer, unsigned char* alphaRegionsBuffer, int width, int height, int regionSize, const Box* headBoxes, int headCount, CUstream stream) {
	// Use a 1D block for x dimension to ensure we process every x coordinate
	dim3 blockSize(256, 1);
	int numRegions = (height + regionSize - 1) / regionSize; // Ceiling division

	// Calculate grid size to cover all x coordinates and regions
	dim3 gridSize(
		(width + blockSize.x - 1) / blockSize.x,
		numRegions // Each block handles one region
	);
    
    try {
        CUfunction kernel = KernelHelpers::GetKernelFunction("DetectUncertainRegionsKernel");
        void* kernelArgs[] = { &alphaBuffer, &alphaRegionsBuffer, &width, &height, &regionSize, &headBoxes, &headCount };
        CUresult result = KernelHelpers::LaunchKernel(kernel,
            gridSize.x, gridSize.y, 1,
            blockSize.x, blockSize.y, 1,
            0, stream, kernelArgs);
        if (result != CUDA_SUCCESS) {
            const char* errorStr;
            cuGetErrorString(result, &errorStr);
            std::cerr << "launchDetectUncertainRegions: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
        }
    } catch (const std::exception& e) {
        std::cerr << "launchDetectUncertainRegions: Exception: " << e.what() << std::endl;
    }
}

void launchGenerateTrimap(float* outputTrimap, const float* inputAlpha, int width, int height, CUstream stream) {
    dim3 blockSize(16, 16);
    dim3 gridSize((width + blockSize.x - 1) / blockSize.x, (height + blockSize.y - 1) / blockSize.y);
    
    try {
        CUfunction kernel = KernelHelpers::GetKernelFunction("generateTrimapKernel");
        void* kernelArgs[] = { &outputTrimap, &inputAlpha, &width, &height };
        CUresult result = KernelHelpers::LaunchKernel(kernel,
            gridSize.x, gridSize.y, 1,
            blockSize.x, blockSize.y, 1,
            0, stream, kernelArgs);
        if (result != CUDA_SUCCESS) {
            const char* errorStr;
            cuGetErrorString(result, &errorStr);
            std::cerr << "launchGenerateTrimap: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
        }
    } catch (const std::exception& e) {
        std::cerr << "launchGenerateTrimap: Exception: " << e.what() << std::endl;
    }
}

void launchPrepareIndexNetInput(float* outputBuffer, const float* inputRgbBuffer, const float* inputTrimap,
	int x, int y, int width, int height, int modelSizeX, int modelSizeY, int padding, CUstream stream) {
    
    dim3 blockSize(16, 16);
    dim3 gridSize((modelSizeX + blockSize.x - 1) / blockSize.x, (modelSizeY + blockSize.y - 1) / blockSize.y);
    
    try {
        CUfunction kernel = KernelHelpers::GetKernelFunction("prepareIndexNetInputKernel");
        void* kernelArgs[] = { &outputBuffer, &inputRgbBuffer, &inputTrimap, &x, &y, &width, &height, &modelSizeX, &modelSizeY, &padding };
        CUresult result = KernelHelpers::LaunchKernel(kernel,
            gridSize.x, gridSize.y, 1,
            blockSize.x, blockSize.y, 1,
            0, stream, kernelArgs);
        if (result != CUDA_SUCCESS) {
            const char* errorStr;
            cuGetErrorString(result, &errorStr);
            std::cerr << "launchPrepareIndexNetInput: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
        }
    } catch (const std::exception& e) {
        std::cerr << "launchPrepareIndexNetInput: Exception: " << e.what() << std::endl;
    }
}

void launchUpdateAlphaBufferRegion(float* outputAlphaBuffer, CUtexObject originalAlphaTexture,
                                  const float* trimapBuffer, const float* improvedAlpha,
                                  int x, int y, int width, int height, int modelSize, int padding, CUstream stream) {
    dim3 blockSize(16, 16);
	dim3 gridSize((modelSize - 2 * padding + blockSize.x - 1) / blockSize.x,
		(modelSize - 2 * padding + blockSize.y - 1) / blockSize.y);    
    try {
        CUfunction kernel = KernelHelpers::GetKernelFunction("updateAlphaBufferRegionKernelTexture");
        void* kernelArgs[] = { &outputAlphaBuffer, &originalAlphaTexture, &trimapBuffer, &improvedAlpha, &x, &y, &width, &height, &modelSize, &padding };
        CUresult result = KernelHelpers::LaunchKernel(kernel,
            gridSize.x, gridSize.y, 1,
            blockSize.x, blockSize.y, 1,
            0, stream, kernelArgs);
        if (result != CUDA_SUCCESS) {
            const char* errorStr;
            cuGetErrorString(result, &errorStr);
            std::cerr << "launchUpdateAlphaBufferRegion: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
        }
    } catch (const std::exception& e) {
        std::cerr << "launchUpdateAlphaBufferRegion: Exception: " << e.what() << std::endl;
    }
}

void launchExtractRegion(const float* inputBuffer, float* outputBuffer, int inputWidth, int inputHeight,
                        int regionX, int regionY, int regionWidth, int regionHeight, int channels, CUstream stream) {
    dim3 blockSize(16, 16);
    dim3 gridSize((regionWidth + blockSize.x - 1) / blockSize.x, (regionHeight + blockSize.y - 1) / blockSize.y);
    
    try {
        CUfunction kernel = KernelHelpers::GetKernelFunction("extractRegionKernel");
        void* kernelArgs[] = { &inputBuffer, &outputBuffer, &inputWidth, &inputHeight, &regionX, &regionY, &regionWidth, &regionHeight, &channels };
        CUresult result = KernelHelpers::LaunchKernel(kernel,
            gridSize.x, gridSize.y, 1,
            blockSize.x, blockSize.y, 1,
            0, stream, kernelArgs);
        if (result != CUDA_SUCCESS) {
            const char* errorStr;
            cuGetErrorString(result, &errorStr);
            std::cerr << "launchExtractRegion: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
        }
    } catch (const std::exception& e) {
        std::cerr << "launchExtractRegion: Exception: " << e.what() << std::endl;
    }
}

void launchCombineAlphaRegion(float* mainAlpha, const float* regionAlpha, int mainWidth, int mainHeight,
                           int regionX, int regionY, int regionWidth, int regionHeight, CUstream stream) {
    dim3 blockSize(16, 16);
    dim3 gridSize((regionWidth + blockSize.x - 1) / blockSize.x, (regionHeight + blockSize.y - 1) / blockSize.y);
    
    try {
        CUfunction kernel = KernelHelpers::GetKernelFunction("combineAlphaRegionKernel");
        void* kernelArgs[] = { &mainAlpha, &regionAlpha, &mainWidth, &mainHeight, &regionX, &regionY, &regionWidth, &regionHeight };
        CUresult result = KernelHelpers::LaunchKernel(kernel,
            gridSize.x, gridSize.y, 1,
            blockSize.x, blockSize.y, 1,
            0, stream, kernelArgs);
        if (result != CUDA_SUCCESS) {
            const char* errorStr;
            cuGetErrorString(result, &errorStr);
            std::cerr << "launchCombineAlphaRegion: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
        }
    } catch (const std::exception& e) {
        std::cerr << "launchCombineAlphaRegion: Exception: " << e.what() << std::endl;
    }
}

void launchResizeAlpha(const float* inputAlpha, float* outputAlpha,
                      int inputWidth, int inputHeight,
                      int outputWidth, int outputHeight, CUstream stream) {
    dim3 blockSize(16, 16);
    dim3 gridSize((outputWidth + blockSize.x - 1) / blockSize.x, (outputHeight + blockSize.y - 1) / blockSize.y);
    
    try {
        CUfunction kernel = KernelHelpers::GetKernelFunction("resizeAlphaKernel");
        void* kernelArgs[] = { &inputAlpha, &outputAlpha, &inputWidth, &inputHeight, &outputWidth, &outputHeight };
        CUresult result = KernelHelpers::LaunchKernel(kernel,
            gridSize.x, gridSize.y, 1,
            blockSize.x, blockSize.y, 1,
            0, stream, kernelArgs);
        if (result != CUDA_SUCCESS) {
            const char* errorStr;
            cuGetErrorString(result, &errorStr);
            std::cerr << "launchResizeAlpha: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
        }
    } catch (const std::exception& e) {
        std::cerr << "launchResizeAlpha: Exception: " << e.what() << std::endl;
    }
}
