#pragma once

#define NOMINMAX // Disable the min/max macros from windows.h which interfere with std::min/max

#include <string>
#include <stdexcept>
#include <cuda.h>
#include <vector>
#include <iostream>
#define WIN32_LEAN_AND_MEAN

#include <windows.h>
#include <onnxruntime_cxx_api.h>

// Error checking for CUDA Driver API calls
#define DRIVER_API_CHECK(call) \
    do { \
        CUresult error = call; \
        if (error != CUDA_SUCCESS) { \
            const char* errorStr; \
            cuGetErrorString(error, &errorStr); \
            std::cerr << "CUDA Driver API error at " << __FILE__ << ":" << __LINE__ << " - " \
                      << errorStr << " (code: " << error << ")" << std::endl; \
            return 1; \
        } \
    } while(0)

// Forward declarations for FFmpeg types
extern "C" {
    #include <libavutil/rational.h>
    #include <libavutil/error.h>
    #include <libavutil/mem.h>
}

namespace Helpers {
    // Texture format enumeration
    enum class TextureFormat {
        Unknown,
        NV12,
        YUV420P,
        RGBA,
        BGRA
    };

    // String conversion functions
    inline std::string WStringToString(const std::wstring& wstr) {
        if (wstr.empty()) return std::string();
        int size_needed = WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), (int)wstr.size(), nullptr, 0, nullptr, nullptr);
        std::string str(size_needed, 0);
        WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), (int)wstr.size(), &str[0], size_needed, nullptr, nullptr);
        return str;
    }

    inline std::wstring StringToWString(const std::string& str) {
        if (str.empty()) return std::wstring();
        int size_needed = MultiByteToWideChar(CP_UTF8, 0, str.c_str(), (int)str.size(), nullptr, 0);
        std::wstring wstr(size_needed, 0);
        MultiByteToWideChar(CP_UTF8, 0, str.c_str(), (int)str.size(), &wstr[0], size_needed);
        return wstr;
    }

    // AVRational helper functions
    inline AVRational* CreateAVRational(int num, int den) {
        AVRational* r = (AVRational*)av_malloc(sizeof(AVRational));
        if (r) {
            r->num = num;
            r->den = den;
        }
        return r;
    }

    inline void DeleteAVRational(AVRational* r) {
        if (r) {
            av_free(r);
        }
    }

    // CUDA Driver API error checking
    inline void CheckCudaError(CUresult err) {
        if (err != CUDA_SUCCESS) {
            const char* errorStr;
            cuGetErrorString(err, &errorStr);
            throw std::runtime_error(std::string("CUDA Driver API error: ") + errorStr);
        }
    }

    // FFmpeg error checking
    inline void CheckFFmpegError(int err) {
        if (err < 0) {
            char errbuf[AV_ERROR_MAX_STRING_SIZE] = { 0 };
            av_strerror(err, errbuf, AV_ERROR_MAX_STRING_SIZE);
            throw std::runtime_error(std::string("FFmpeg error: ") + errbuf);
        }
    }
}

bool SaveAlphaMatteToPNG(const char* filename, CUdeviceptr alphaMatteOnGpu, int width, int height);
bool SaveAlphaMatteToPNG(const char* filename, const unsigned char* alphaMatteOnCpu, int width, int height);
bool SaveInterleavedFloatImageToPNG(const char* filename, const float* rgbaImage, int width, int height, bool hasAlpha = false);
CUresult SavePlanarFloatImageToPNG(const std::string& filename,
    const CUdeviceptr deviceImageData,
    int width, int height,
    bool hasAlpha,
    CUstream stream);

std::string ConvertWCharToChar(const wchar_t* wcharStr);
std::string ConvertWCharToChar(const std::wstring& wstr);
std::wstring ConvertCharToWChar(const std::string& str);
Ort::Value CreateOrtValueFromDeviceMemory(void* deviceBuffer, const std::vector<int64_t>& shape,
    ONNXTensorElementDataType dataType, const Ort::MemoryInfo& memoryInfo);
bool loadRGBAImageToCudaBufferFloatPlanar(const char* rgbImagePath, const char* alphaMaskPath,
    CUdeviceptr cudaBuffer, int expectedWidth, int expectedHeight,
    CUstream stream);


