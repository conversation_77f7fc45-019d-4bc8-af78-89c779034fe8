@echo off
echo ===============================================
echo Testing FFV1 Encoding Fix
echo ===============================================

set INPUT_FILE=initial_alpha.ffv1.mkv

echo.
echo Step 1: Check if video file exists...
if not exist "%INPUT_FILE%" (
    echo ERROR: Video file not found. Run your alpha generation first.
    echo Expected file: %INPUT_FILE%
    pause
    exit /b 1
)

echo Found: %INPUT_FILE%

echo.
echo Step 2: Quick integrity check...
ffprobe -v quiet -print_format json -show_streams "%INPUT_FILE%" > quick_check.json 2>quick_errors.txt

if exist quick_errors.txt (
    echo Checking for errors...
    for %%A in (quick_errors.txt) do if %%~zA gtr 0 (
        echo WARNING: Still some issues detected:
        type quick_errors.txt
        echo.
    ) else (
        echo No format errors detected!
    )
) else (
    echo No format errors detected!
)

echo.
echo Step 3: Attempting frame extraction (improved method)...
ffmpeg -v warning -i "%INPUT_FILE%" -vframes 1 -f image2 "test_frame.png" -y 2>extraction_errors.txt

if %ERRORLEVEL% equ 0 (
    echo SUCCESS: Frame extracted to test_frame.png
    echo Opening extracted frame...
    start "" "test_frame.png"
    echo.
    echo Step 4: Video information:
    ffprobe -v quiet -show_format -show_streams "%INPUT_FILE%" 
) else (
    echo FAILED: Frame extraction failed
    echo Errors:
    type extraction_errors.txt
)

echo.
echo ===============================================
echo Test Complete
echo ===============================================
pause
