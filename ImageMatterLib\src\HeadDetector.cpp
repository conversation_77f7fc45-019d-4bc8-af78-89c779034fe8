#include "HeadDetector.h"
#include "Helpers.h"
#include "HeadDetectorKernels.cuh"
#include <onnxruntime_cxx_api.h>
#include <vector>
#include <memory>
#include <array>
#include <iostream>
#include <chrono>
#include <cuda.h>  // CUDA Driver API
#include <cuda_runtime.h>  // CUDA Runtime API for memory functions

HeadDetector::HeadDetector()
: m_env(nullptr)
, m_session(nullptr)
, m_memory_info_cuda()
, m_modelInputName("")
, m_modelInputWidth(0)
, m_modelInputHeight(0)
, m_imageWidth(0)
, m_imageHeight(0)
, m_inputBufferSize(0)
, m_preprocessedBuffer(nullptr)
, m_deviceResizedInputBuffer(nullptr)
// , m_inputExternalMemory{} // Removed for Driver API compatibility
, m_inputTensor(nullptr)
, m_initialized(false)
, m_cudaStream(nullptr)
{
}

HeadDetector::~HeadDetector() {
    Release();
}

// Modified HeadDetector.cpp - Key changes to let ONNX handle output allocation

bool HeadDetector::Initialize(
    int imageWidth,
    int imageHeight,
    CUstream externalStream) {
    // Save current CUDA context (from main.cpp) to restore it after ONNX Runtime potentially changes it
    CUcontext savedContext = nullptr;
    CUresult cuResult = cuCtxGetCurrent(&savedContext);
    if (cuResult != CUDA_SUCCESS) {
        std::cerr << "Failed to get current CUDA context in Initialize(): " << cuResult << std::endl;
    }

    try {
        if (m_initialized) {
            std::cout << "Already initialized. Shutting down first." << std::endl;
            Release();
        }

        // Initialize ImageMatting
        // Use models directory relative to executable
        wchar_t exePath[MAX_PATH];
        GetModuleFileNameW(NULL, exePath, MAX_PATH);
        std::wstring exeDir = std::wstring(exePath);
        exeDir = exeDir.substr(0, exeDir.find_last_of(L"\\/"));
        std::wstring modelPath = exeDir + L"\\ModelsVggHeadDetector\\vgg_heads_l.onnx";

        // Store model input dimensions
        m_modelInputWidth = 640;
        m_modelInputHeight = 640;
        m_imageWidth = imageWidth;
        m_imageHeight = imageHeight;

        // Use external stream if provided, otherwise use default stream
        m_cudaStream = externalStream;

        // Allocate CUDA buffers for resized input (ONLY INPUT - outputs will be managed by ONNX)
        CUdeviceptr devicePtr;
        CUresult cudaStatus = cuMemAlloc(&devicePtr, m_modelInputWidth * m_modelInputHeight * 3 * sizeof(float));
        m_deviceResizedInputBuffer = (float*)devicePtr;
        if (cudaStatus != CUDA_SUCCESS) {
            const char* errorStr;
            cuGetErrorString(cudaStatus, &errorStr);
            std::cerr << "Failed to allocate resized input buffer: " << errorStr << std::endl;
            return false;
        }

        // REMOVED: Output buffer allocations - ONNX will handle these

        // ONNX Runtime setup
        m_env = std::make_unique<Ort::Env>(ORT_LOGGING_LEVEL_WARNING, "HeadDetectorOnnx");

        std::cout << "Setting up ONNX session options..." << std::endl;
        Ort::SessionOptions sessionOptions;
        sessionOptions.SetGraphOptimizationLevel(GraphOptimizationLevel::ORT_ENABLE_ALL);
        sessionOptions.SetExecutionMode(ExecutionMode::ORT_PARALLEL);
        sessionOptions.SetIntraOpNumThreads(4);
        sessionOptions.SetInterOpNumThreads(4);
        sessionOptions.EnableMemPattern();
        sessionOptions.DisableCpuMemArena();
        
        // Set up CUDA provider options with arena shrinkage enabled
        OrtCUDAProviderOptions cudaOptions;
        cudaOptions.device_id = 0;
        cudaOptions.arena_extend_strategy = 1; // Enable arena shrinkage
        cudaOptions.gpu_mem_limit = 512LL * 1024 * 1024; // 512MB limit
        cudaOptions.cudnn_conv_algo_search = static_cast<OrtCudnnConvAlgoSearch>(1);
        cudaOptions.do_copy_in_default_stream = 1;

        sessionOptions.AppendExecutionProvider_CUDA(cudaOptions);

        // Create session with CUDA provider
        std::cout << "Creating ONNX session..." << std::endl;
        m_session = std::make_unique<Ort::Session>(*m_env, modelPath.c_str(), sessionOptions);
        std::cout << "ONNX session created successfully." << std::endl;

        // Get model input/output names
        Ort::AllocatorWithDefaultOptions allocator;
        m_modelInputName = m_session->GetInputNameAllocated(0, allocator).get();

        // Get all output names
        size_t numOutputs = m_session->GetOutputCount();
        m_modelOutputNames.resize(numOutputs);
        for (size_t i = 0; i < numOutputs; i++) {
            m_modelOutputNames[i] = m_session->GetOutputNameAllocated(i, allocator).get();
        }

        // Create CUDA memory info for input only
        m_memory_info_cuda = std::make_unique<Ort::MemoryInfo>("Cuda", OrtDeviceAllocator, 0, OrtMemTypeDefault);

        // Create input tensor (still pre-allocated on GPU to avoid CPU copy)
        std::vector<int64_t> inputDims = { 1, 3, m_modelInputHeight, m_modelInputWidth };
        m_inputTensor = std::make_unique<Ort::Value>(CreateOrtValueFromDeviceMemory(
            m_deviceResizedInputBuffer, inputDims, ONNX_TENSOR_ELEMENT_DATA_TYPE_FLOAT, *m_memory_info_cuda));

        // REMOVED: Output tensor creation - ONNX will handle these
        // REMOVED: IoBinding - we'll use regular Run() method

        m_initialized = true;
        std::cout << "Head detection initialization successful." << std::endl;

        // Restore main.cpp CUDA context (ONNX Runtime may have changed it)
        if (savedContext != nullptr) {
            CUresult restoreResult = cuCtxSetCurrent(savedContext);
            if (restoreResult != CUDA_SUCCESS) {
                std::cerr << "Failed to restore main.cpp CUDA context in Initialize(): " << restoreResult << std::endl;
            }
        }

        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Exception during initialization: " << e.what() << std::endl;

        // Restore main.cpp CUDA context on exception
        if (savedContext != nullptr) {
            CUresult restoreResult = cuCtxSetCurrent(savedContext);
            if (restoreResult != CUDA_SUCCESS) {
                std::cerr << "Failed to restore main.cpp CUDA context in Initialize() exception handler: " << restoreResult << std::endl;
            }
        }

        Release();
        return false;
    }
}

bool HeadDetector::DetectHeads(float* inputBuffer, int width, int height, std::vector<Box>& results, float confidenceThreshold) {
    if (!m_initialized) {
        std::cerr << "Head detector not initialized" << std::endl;
        return false;
    }

    // Save current CUDA context (from main.cpp) to restore it after ONNX Runtime potentially changes it
    CUcontext savedContext = nullptr;
    CUresult cuResult = cuCtxGetCurrent(&savedContext);
    if (cuResult != CUDA_SUCCESS) {
        std::cerr << "Failed to get current CUDA context in DetectHeads(): " << cuResult << std::endl;
    }

    try {
        // Verify input dimensions match expected dimensions
        if (width != m_imageWidth || height != m_imageHeight) {
            std::cerr << "Input dimensions (" << width << "x" << height
                << ") do not match expected dimensions (" << m_imageWidth << "x" << m_imageHeight << ")" << std::endl;
            return false;
        }

        // Calculate padding and scaling
        float scale = std::min(static_cast<float>(m_modelInputWidth) / width, static_cast<float>(m_modelInputHeight) / height);
        int newWidth = static_cast<int>(width * scale);
        int newHeight = static_cast<int>(height * scale);
        int padWidth = m_modelInputWidth - newWidth;
        int padHeight = m_modelInputHeight - newHeight;
        int padLeft = padWidth / 2;
        int padTop = padHeight / 2;

        // Resize and pad input RGB to model resolution
        CUresult cudaStatus = LaunchLanczosResizeAndPadKernel(
            m_deviceResizedInputBuffer, m_modelInputWidth, m_modelInputHeight,
            inputBuffer, width, height);

        cudaStatus = cuCtxSynchronize();
        if (cudaStatus != CUDA_SUCCESS) {
            const char* errorStr;
            cuGetErrorString(cudaStatus, &errorStr);
            std::cerr << "Failed to synchronize after preprocessing: " << errorStr << std::endl;
            return false;
        }

        // Prepare inputs - only the input tensor (pre-allocated on GPU)
        std::vector<Ort::Value> inputTensors;
        inputTensors.push_back(std::move(*m_inputTensor));

        std::vector<const char*> inputNames = { m_modelInputName.c_str() };

        // Prepare output names
        std::vector<const char*> outputNames;
        for (const auto& name : m_modelOutputNames) {
            outputNames.push_back(name.c_str());
        }

        //SavePlanarFloatImageToPNG("Videos\\input.png", m_deviceResizedInputBuffer, m_modelInputWidth, m_modelInputHeight, false);
        //SavePlanarFloatRGBImageToPNG("Videos\\inputResized.png", m_deviceResizedInputBuffer, m_modelInputWidth, m_modelInputHeight);

        // Run inference - ONNX Runtime will allocate output tensors
        std::vector<Ort::Value> outputTensors = m_session->Run(
            Ort::RunOptions{ nullptr },
            inputNames.data(),
            inputTensors.data(),
            inputNames.size(),
            outputNames.data(),
            outputNames.size()
        );

        // Recreate input tensor for next inference (since we moved it)
        std::vector<int64_t> inputDims = { 1, 3, m_modelInputHeight, m_modelInputWidth };
        m_inputTensor = std::make_unique<Ort::Value>(CreateOrtValueFromDeviceMemory(
            m_deviceResizedInputBuffer, inputDims, ONNX_TENSOR_ELEMENT_DATA_TYPE_FLOAT, *m_memory_info_cuda));

        cudaStatus = cuCtxSynchronize();
        if (cudaStatus != CUDA_SUCCESS) {
            const char* errorStr;
            cuGetErrorString(cudaStatus, &errorStr);
            std::cerr << "Failed to synchronize after inference: " << errorStr << std::endl;
            return false;
        }

        // Access output tensors (ONNX-managed)
        if (outputTensors.size() < 2) {
            std::cerr << "Expected at least 2 outputs, got " << outputTensors.size() << std::endl;
            return false;
        }

        // Get tensor info and data pointers
        auto& boxesTensor = outputTensors[0];  // pre_nms_pred
        auto& scoresTensor = outputTensors[1]; // pre_nms_raw_pred

        auto boxesShape = boxesTensor.GetTensorTypeAndShapeInfo().GetShape();
        auto scoresShape = scoresTensor.GetTensorTypeAndShapeInfo().GetShape();

        // Get data pointers - these point to ONNX-managed memory
        const float* boxesData = boxesTensor.GetTensorData<float>();
        const float* scoresData = scoresTensor.GetTensorData<float>();

        size_t numDetections = scoresShape[1]; // Should be 1000

        // Process detections
        results.clear();
        for (size_t i = 0; i < numDetections; i++) {
            // Handle different score tensor shapes
            float score;
            if (scoresShape.size() == 3 && scoresShape[2] == 1) {
                score = scoresData[i]; // For [1,1000,1] shape
            }
            else {
                score = scoresData[i]; // For other shapes
            }

            if (score < confidenceThreshold) continue;

            // Get box coordinates (assuming [1,1000,4] shape)
            float x1 = boxesData[i * 4];
            float y1 = boxesData[i * 4 + 1];
            float x2 = boxesData[i * 4 + 2];
            float y2 = boxesData[i * 4 + 3];

            // Clamp to model input size
            x1 = std::max(0.0f, std::min(static_cast<float>(m_modelInputWidth), x1));
            y1 = std::max(0.0f, std::min(static_cast<float>(m_modelInputHeight), y1));
            x2 = std::max(0.0f, std::min(static_cast<float>(m_modelInputWidth), x2));
            y2 = std::max(0.0f, std::min(static_cast<float>(m_modelInputHeight), y2));

            // Remove padding and scale back to original image
            Box result;
			int extra = 25; // Head detection is not precise, so add some extra padding to assure we have the complete heads.
            result.x = (x1 - padLeft) / scale - extra;
            result.y = (y1 - padTop) / scale - extra;
            result.width = (x2 - padLeft) / scale - result.x + 2 * extra;
            result.height = (y2 - padTop) / scale - result.y + 2 * extra;
            result.confidence = score;

            if (results.empty() || std::abs(result.x - results.back().x) > 20 || std::abs(result.y - results.back().y) > 20) {
                results.push_back(result);
            }
        }

        // Restore main.cpp CUDA context (ONNX Runtime may have changed it)
        if (savedContext != nullptr) {
            CUresult restoreResult = cuCtxSetCurrent(savedContext);
            if (restoreResult != CUDA_SUCCESS) {
                std::cerr << "Failed to restore main.cpp CUDA context in DetectHeads(): " << restoreResult << std::endl;
            }
        }

        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Exception during head detection: " << e.what() << std::endl;

        // Restore main.cpp CUDA context on exception
        if (savedContext != nullptr) {
            CUresult restoreResult = cuCtxSetCurrent(savedContext);
            if (restoreResult != CUDA_SUCCESS) {
                std::cerr << "Failed to restore main.cpp CUDA context in DetectHeads() exception handler: " << restoreResult << std::endl;
            }
        }

        return false;
    }
}

void HeadDetector::Release() {
    std::cout << "Starting HeadDetector cleanup..." << std::endl;
    
    // Release tensors first
    m_inputTensor.reset();
    std::cout << "Input tensor released." << std::endl;
    
    // Force CUDA synchronization before session destruction
    if (m_session) {
        cudaDeviceSynchronize();
    }
    
    // Destroy session (arena shrinkage should release memory)
    m_session.reset();
    std::cout << "ONNX session released." << std::endl;
    
    // Force immediate CUDA memory release after session destruction
    cudaDeviceSynchronize();
    
    // Release other ONNX components
    m_memory_info_cuda.reset();
    m_env.reset();
    std::cout << "ONNX environment released." << std::endl;

    if (m_deviceResizedInputBuffer) {
        cuMemFree((CUdeviceptr)m_deviceResizedInputBuffer);
        m_deviceResizedInputBuffer = nullptr;
        std::cout << "CUDA input buffer released." << std::endl;
    }

    m_initialized = false;
    std::cout << "Head detection shutdown complete." << std::endl;
}

Ort::Value HeadDetector::CreateOrtValueFromDeviceMemory(void* deviceBuffer, const std::vector<int64_t>& shape,
    ONNXTensorElementDataType dataType, const Ort::MemoryInfo& memoryInfo) {
    size_t elementSize;
    switch (dataType) {
    case ONNX_TENSOR_ELEMENT_DATA_TYPE_FLOAT:
        elementSize = sizeof(float);
        break;
    default:
        throw std::runtime_error("Unsupported tensor data type");
    }

    size_t totalSize = elementSize;
    for (const auto& dim : shape) {
        totalSize *= dim;
    }

    return Ort::Value::CreateTensor(memoryInfo, deviceBuffer, totalSize,
        shape.data(), shape.size(), dataType);
}
