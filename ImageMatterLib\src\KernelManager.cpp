// KernelManager.cpp - CUDA Driver API Kernel Module Loading System Implementation
#include "KernelManager.h"
#include <iostream>
#include <fstream>
#include <vector>
#include <filesystem>
#ifdef _WIN32
#include <windows.h>
#else
#include <unistd.h>
#endif

KernelManager& KernelManager::GetInstance() {
    static KernelManager instance;
    return instance;
}

KernelManager::~KernelManager() {
    Cleanup();
}

void KernelManager::CheckCudaError(CUresult error, const std::string& operation) {
    if (error != CUDA_SUCCESS) {
        const char* errorStr;
        cuGetErrorString(error, &errorStr);
        std::string errorMsg = "CUDA Driver API error in " + operation + ": " + std::string(errorStr);
        std::cerr << errorMsg << std::endl;
        throw std::runtime_error(errorMsg);
    }
}

bool KernelManager::Initialize(const std::string& modulePath) {
    if (m_initialized) {
        return true; // Already initialized
    }
    
    try {
        // Load the module from file (PTX or cubin)
        CheckCudaError(cuModuleLoad(&m_module, modulePath.c_str()), "cuModuleLoad");
        
        m_initialized = true;
        std::cout << "KernelManager: Successfully loaded module from " << modulePath << std::endl;
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "KernelManager: Failed to initialize: " << e.what() << std::endl;
        return false;
    }
}

bool KernelManager::InitializeFromPTX(const char* ptxData) {
    if (m_initialized) {
        return true; // Already initialized
    }
    
    try {
        // Load the module from embedded PTX data
        CheckCudaError(cuModuleLoadData(&m_module, ptxData), "cuModuleLoadData");
        
        m_initialized = true;
        std::cout << "KernelManager: Successfully loaded module from embedded PTX data" << std::endl;
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "KernelManager: Failed to initialize from PTX: " << e.what() << std::endl;
        return false;
    }
}

CUfunction KernelManager::GetKernel(const std::string& kernelName) {
    if (!m_initialized) {
        throw std::runtime_error("KernelManager not initialized");
    }
    
    // Check cache first
    auto it = m_kernelCache.find(kernelName);
    if (it != m_kernelCache.end()) {
        return it->second;
    }
    
    // Load kernel from module
    CUfunction kernel;
    try {
        CheckCudaError(cuModuleGetFunction(&kernel, m_module, kernelName.c_str()), 
                      "cuModuleGetFunction for " + kernelName);
        
        // Cache the kernel
        m_kernelCache[kernelName] = kernel;
        std::cout << "KernelManager: Loaded kernel '" << kernelName << "'" << std::endl;
        return kernel;
    }
    catch (const std::exception& e) {
        std::cerr << "KernelManager: Failed to load kernel '" << kernelName << "': " << e.what() << std::endl;
        throw;
    }
}

bool KernelManager::IsKernelLoaded(const std::string& kernelName) const {
    return m_kernelCache.find(kernelName) != m_kernelCache.end();
}

void KernelManager::Cleanup() {
    if (m_module) {
        cuModuleUnload(m_module);
        m_module = nullptr;
    }
    m_kernelCache.clear();
    m_initialized = false;
}

namespace KernelHelpers {
    bool InitializeKernels() {
        // Get the executable directory to locate PTX file relative to the executable
        std::string exePath;
        char buffer[MAX_PATH];
        
        #ifdef _WIN32
        DWORD pathLength = GetModuleFileNameA(NULL, buffer, MAX_PATH);
        if (pathLength > 0 && pathLength < MAX_PATH) {
            exePath = std::string(buffer);
            // Remove the executable filename to get directory
            size_t lastSlash = exePath.find_last_of("\\/");
            if (lastSlash != std::string::npos) {
                exePath = exePath.substr(0, lastSlash + 1);
            }
        }
        #else
        // For Linux/Unix systems
        ssize_t len = readlink("/proc/self/exe", buffer, sizeof(buffer) - 1);
        if (len != -1) {
            buffer[len] = '\0';
            exePath = std::string(buffer);
            size_t lastSlash = exePath.find_last_of("/");
            if (lastSlash != std::string::npos) {
                exePath = exePath.substr(0, lastSlash + 1);
            }
        }
        #endif
        
        // Try to load from PTX file in various locations
        std::vector<std::string> possiblePaths = {
            "ImageMatterKernels.ptx",                           // Current directory
            exePath + "ImageMatterKernels.ptx",                 // Executable directory
            exePath + "..\\ImageMatterKernels.ptx",             // Parent of executable directory
            exePath + "kernels\\ImageMatterKernels.ptx",        // Kernels subdirectory
            "kernels/ImageMatterKernels.ptx",
            "../kernels/ImageMatterKernels.ptx",
            "ImageMatterKernels.cubin"
        };
        
        std::cout << "KernelHelpers: Searching for PTX file..." << std::endl;
        if (!exePath.empty()) {
            std::cout << "KernelHelpers: Executable directory: " << exePath << std::endl;
        }
        
        for (const auto& path : possiblePaths) {
            std::cout << "KernelHelpers: Trying: " << path << std::endl;
            std::ifstream file(path);
            if (file.good()) {
                std::cout << "KernelHelpers: Found PTX file at: " << path << std::endl;
                return KernelManager::GetInstance().Initialize(path);
            }
        }
        
        std::cerr << "KernelHelpers: Could not find ImageMatterKernels.ptx in any expected location" << std::endl;
        std::cerr << "KernelHelpers: Make sure the PTX file is generated and placed correctly" << std::endl;
        return false;
    }
    
    CUfunction GetKernelFunction(const std::string& kernelName) {
        // Check if KernelManager is initialized
        KernelManager& manager = KernelManager::GetInstance();
        if (manager.GetModule() == nullptr) {
            if (!InitializeKernels()) {
                throw std::runtime_error("Failed to initialize kernels - PTX file not found or invalid");
            }
        }
        
        return manager.GetKernel(kernelName);
    }
    
    CUresult LaunchKernel(CUfunction kernel, 
                         unsigned int gridX, unsigned int gridY, unsigned int gridZ,
                         unsigned int blockX, unsigned int blockY, unsigned int blockZ,
                         unsigned int sharedMemBytes, CUstream stream,
                         void** kernelParams) {
        if (!kernel) {
            std::cerr << "KernelHelpers::LaunchKernel: Invalid kernel function (nullptr)" << std::endl;
            return CUDA_ERROR_INVALID_VALUE;
        }
        
        // Validate grid and block dimensions
        if (gridX == 0 || gridY == 0 || gridZ == 0) {
            std::cerr << "KernelHelpers::LaunchKernel: Invalid grid dimensions (" 
                     << gridX << ", " << gridY << ", " << gridZ << ")" << std::endl;
            return CUDA_ERROR_INVALID_VALUE;
        }
        
        if (blockX == 0 || blockY == 0 || blockZ == 0) {
            std::cerr << "KernelHelpers::LaunchKernel: Invalid block dimensions (" 
                     << blockX << ", " << blockY << ", " << blockZ << ")" << std::endl;
            return CUDA_ERROR_INVALID_VALUE;
        }
        
        // Launch the kernel
        CUresult result = cuLaunchKernel(kernel, 
                                        gridX, gridY, gridZ,
                                        blockX, blockY, blockZ,
                                        sharedMemBytes, stream,
                                        kernelParams, nullptr);
        
        // Check for launch errors
        if (result != CUDA_SUCCESS) {
            const char* errorStr;
            cuGetErrorString(result, &errorStr);
            std::cerr << "KernelHelpers::LaunchKernel: Kernel launch failed: " 
                     << (errorStr ? errorStr : "unknown error") << std::endl;
            std::cerr << "  Grid: (" << gridX << ", " << gridY << ", " << gridZ << ")" << std::endl;
            std::cerr << "  Block: (" << blockX << ", " << blockY << ", " << blockZ << ")" << std::endl;
            std::cerr << "  Shared memory: " << sharedMemBytes << " bytes" << std::endl;
        }
        
        return result;
    }
    
    void LaunchKernelChecked(const std::string& kernelName,
                           unsigned int gridX, unsigned int gridY, unsigned int gridZ,
                           unsigned int blockX, unsigned int blockY, unsigned int blockZ,
                           unsigned int sharedMemBytes, CUstream stream,
                           void** kernelParams) {
        // Get the kernel function
        CUfunction kernel = GetKernelFunction(kernelName);
        if (!kernel) {
            throw std::runtime_error("Failed to get kernel function: " + kernelName);
        }
        
        // Launch the kernel with error checking
        CUresult result = LaunchKernel(kernel, gridX, gridY, gridZ, blockX, blockY, blockZ, 
                                      sharedMemBytes, stream, kernelParams);
        
        // Throw exception if launch failed
        if (result != CUDA_SUCCESS) {
            const char* errorStr;
            cuGetErrorString(result, &errorStr);
            throw std::runtime_error("Kernel launch failed for '" + kernelName + "': " + 
                                   (errorStr ? errorStr : "unknown error"));
        }
    }
}
