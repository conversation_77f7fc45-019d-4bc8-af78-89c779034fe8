#include "FFmpegIOKernels.cuh"
#include "KernelManager.h"
#include <iostream>
#include <exception>

#ifdef __CUDACC__

// CUDA kernel to convert NV12 to planar float RGB
extern "C" __global__ void ByteNv12ToPlanarFloatRgbKernel(unsigned char* nv12Data,
	float* rgbData,
	int width,
	int height,
	size_t nv12Pitch) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;
	if (x >= width || y >= height) return;
	int yIndex = y * nv12Pitch + x;
	int uvIndex = (y / 2) * nv12Pitch + (x & ~1);
	size_t uvOffset = (size_t)height * nv12Pitch;
	float Y = (float)nv12Data[yIndex];
	float U = (float)nv12Data[uvOffset + uvIndex];
	float V = (float)nv12Data[uvOffset + uvIndex + 1];
	Y = (Y - 16.0f) / 219.0f;
	U = (U - 128.0f) / 224.0f;
	V = (V - 128.0f) / 224.0f;
	float R = Y + 1.402f * V;
	float G = Y - 0.344f * U - 0.714f * V;
	float B = Y + 1.772f * U;
	R = fmaxf(0.0f, fminf(1.0f, R));
	G = fmaxf(0.0f, fminf(1.0f, G));
	B = fmaxf(0.0f, fminf(1.0f, B));
	int pixelIndex = y * width + x;
	int planeSize = width * height;
	rgbData[pixelIndex] = R;
	rgbData[pixelIndex + planeSize] = G;
	rgbData[pixelIndex + 2 * planeSize] = B;
}

// CUDA kernel for converting YUV420P (planar) to planar float RGB
extern "C" __global__ void Yuv420pToPlanarFloatRgbKernel(
	const unsigned char* y_plane_in,
	const unsigned char* u_plane_in,
	const unsigned char* v_plane_in,
	float* r_plane_out,
	float* g_plane_out,
	float* b_plane_out,
	int width,
	int height,
	int y_stride,
	int u_stride,
	int v_stride) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;
	if (x >= width || y >= height) return;
	unsigned char Y = y_plane_in[y * y_stride + x];
	int uv_x = x / 2;
	int uv_y = y / 2;
	unsigned char U = u_plane_in[uv_y * u_stride + uv_x];
	unsigned char V = v_plane_in[uv_y * v_stride + uv_x];
	float fY = (float)Y;
	float fU = (float)U - 128.0f;
	float fV = (float)V - 128.0f;
	float R = fY + 1.402f * fV;
	float G = fY - 0.344136f * fU - 0.714136f * fV;
	float B = fY + 1.772f * fU;
	r_plane_out[y * width + x] = fminf(fmaxf(R, 0.0f), 255.0f) / 255.0f;
	g_plane_out[y * width + x] = fminf(fmaxf(G, 0.0f), 255.0f) / 255.0f;
	b_plane_out[y * width + x] = fminf(fmaxf(B, 0.0f), 255.0f) / 255.0f;
}

// CUDA kernel to convert float alpha [0.0-1.0] to YUV420 format
// Alpha goes in Y channel, U/V channels set to 128 (neutral chroma)
extern "C" __global__ void convertFloatAlphaToYuv420Kernel(const float* d_alphaData, uint8_t* d_yuvData,
	int width, int height, int yPitch, int uvPitch) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;

	if (x >= width || y >= height)
		return;

	int alphaIdx = y * width + x;
	float alpha = d_alphaData[alphaIdx];

	// Clamp alpha to [0.0, 1.0]
	alpha = fmaxf(0.0f, fminf(1.0f, alpha));

	// Convert to 8-bit grayscale
	uint8_t gray = static_cast<uint8_t>(alpha * 255.0f);

	// Y channel (luma) = grayscale alpha value
	int yIdx = y * yPitch + x;
	d_yuvData[yIdx] = gray;

	// U and V channels (chroma) = 128 (neutral gray) - only for even coordinates (YUV420 subsampling)
	if ((x % 2 == 0) && (y % 2 == 0)) {
		int uvHeight = height / 2;
		int uvX = x / 2;
		int uvY = y / 2;

		int uPlaneOffset = yPitch * height;
		int vPlaneOffset = uPlaneOffset + uvPitch * uvHeight;

		int uvIdx = uvY * uvPitch + uvX;
		d_yuvData[uPlaneOffset + uvIdx] = 128; // U channel
		d_yuvData[vPlaneOffset + uvIdx] = 128; // V channel
	}
}

// CUDA kernel to convert YUV420 format back to float alpha [0.0-1.0]
// Extracts only the Y (luma) channel which contains the alpha data
extern "C" __global__ void convertYuv420ToFloatAlphaKernel(const uint8_t* d_yuvData, float* d_alphaData,
	int width, int height, int yPitch) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;

	if (x >= width || y >= height)
		return;

	int yIdx = y * yPitch + x;
	int alphaIdx = y * width + x;

	// Extract Y channel (luma) from YUV420 data
	uint8_t gray = d_yuvData[yIdx];

	// Convert back to float [0.0-1.0]
	d_alphaData[alphaIdx] = static_cast<float>(gray) / 255.0f;
}

// CUDA kernel to convert planar float RGB [0..1] to YUV420 8-bit
extern "C" __global__ void planarFloatRgbToYuv420Kernel(
	const float* d_rgb,
	uint8_t* d_yuv,
	int width,
	int height,
	int yPitch,
	int uvPitch) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;
	if (x >= width || y >= height) return;

	int planeSize = width * height;
	float R = d_rgb[y * width + x];
	float G = d_rgb[planeSize + y * width + x];
	float B = d_rgb[2 * planeSize + y * width + x];

	R = fminf(fmaxf(R, 0.0f), 1.0f);
	G = fminf(fmaxf(G, 0.0f), 1.0f);
	B = fminf(fmaxf(B, 0.0f), 1.0f);

	// BT.601 limited range conversion from RGB [0..1] to YUV
	float r255 = R * 255.0f;
	float g255 = G * 255.0f;
	float b255 = B * 255.0f;
	float Yf = 16.0f + 0.257f * r255 + 0.504f * g255 + 0.098f * b255;
	Yf = fminf(fmaxf(Yf, 16.0f), 235.0f);
	int yIdx = y * yPitch + x;
	d_yuv[yIdx] = static_cast<uint8_t>(Yf + 0.5f);

	// Compute U/V for each 2x2 block from (x,y) if even
	if ((x % 2 == 0) && (y % 2 == 0)) {
		float Uacc = 0.0f;
		float Vacc = 0.0f;
		for (int dy = 0; dy < 2; ++dy) {
			for (int dx = 0; dx < 2; ++dx) {
				int sx = min(width - 1, x + dx);
				int sy = min(height - 1, y + dy);
				float r = d_rgb[sy * width + sx] * 255.0f;
				float g = d_rgb[planeSize + sy * width + sx] * 255.0f;
				float b = d_rgb[2 * planeSize + sy * width + sx] * 255.0f;
				float Uf = 128.0f - 0.148f * r - 0.291f * g + 0.439f * b;
				float Vf = 128.0f + 0.439f * r - 0.368f * g - 0.071f * b;
				Uacc += Uf;
				Vacc += Vf;
			}
		}
		float Uavg = fminf(fmaxf(Uacc / 4.0f, 16.0f), 240.0f);
		float Vavg = fminf(fmaxf(Vacc / 4.0f, 16.0f), 240.0f);
		int uvHeight = height / 2;
		int uvX = x / 2;
		int uvY = y / 2;
		int uPlaneOffset = yPitch * height;
		int vPlaneOffset = uPlaneOffset + uvPitch * uvHeight;
		int uvIdx = uvY * uvPitch + uvX;
		d_yuv[uPlaneOffset + uvIdx] = static_cast<uint8_t>(Uavg + 0.5f);
		d_yuv[vPlaneOffset + uvIdx] = static_cast<uint8_t>(Vavg + 0.5f);
	}
}

// CUDA kernel to extract alpha from NV12 Y plane
extern "C" __global__ void extractAlphaFromNv12Kernel(unsigned char* nv12Data, float* alphaData, int width, int height, size_t nv12Pitch) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;

	if (x >= width || y >= height) return;

	// Calculate indices
	int yIndex = y * nv12Pitch + x;
	int alphaIndex = y * width + x;

	// Extract Y value and convert to alpha [0.0, 1.0]
	unsigned char yValue = nv12Data[yIndex];
	float alpha = static_cast<float>(yValue) / 255.0f;

	// Write alpha value
	alphaData[alphaIndex] = alpha;
}

// CUDA kernel for converting YUV420P to NV12
extern "C" __global__ void Yuv420pToNv12Kernel(
	const unsigned char* y_plane_in,
	const unsigned char* u_plane_in,
	const unsigned char* v_plane_in,
	unsigned char* nv12_buffer_out,
	int width,
	int height,
	int y_stride_in,
	int u_stride_in,
	int v_stride_in,
	int nv12_pitch_out) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;

	if (x < width && y < height) {
		// Copy Y plane
		nv12_buffer_out[y * nv12_pitch_out + x] = y_plane_in[y * y_stride_in + x];
	}

	// Handle UV plane (NV12 interleaved format)
	if (x < width / 2 && y < height / 2) {
		int uv_x = x * 2;
		int uv_y = y * 2;

		// Calculate UV plane offset (after Y plane)
		size_t uv_offset = (size_t)height * nv12_pitch_out;

		// Read U and V values from planar format
		unsigned char u_val = u_plane_in[y * u_stride_in + x];
		unsigned char v_val = v_plane_in[y * v_stride_in + x];

		// Write interleaved UV to NV12 format (UVUV...)
		int nv12_uv_index = uv_offset + uv_y * nv12_pitch_out + uv_x;
		nv12_buffer_out[nv12_uv_index] = u_val;
		nv12_buffer_out[nv12_uv_index + 1] = v_val;
	}
}

// CUDA kernel to convert NV12 to byte BGRA
extern "C" __global__ void nv12ToBgraKernel(unsigned char* nv12Data, unsigned char* bgraData, int width, int height, size_t nv12Pitch) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;

	if (x >= width || y >= height) return;

	int yIndex = y * nv12Pitch + x;
	int uvIndex = (y / 2) * nv12Pitch + (x & ~1);
	size_t uvOffset = (size_t)height * nv12Pitch;

	// Get Y, U, V values
	float Y = (float)nv12Data[yIndex];
	float U = (float)nv12Data[uvOffset + uvIndex];
	float V = (float)nv12Data[uvOffset + uvIndex + 1];

	// Convert YUV to RGB
	Y = (Y - 16.0f) / 219.0f;
	U = (U - 128.0f) / 224.0f;
	V = (V - 128.0f) / 224.0f;

	float R = Y + 1.402f * V;
	float G = Y - 0.344f * U - 0.714f * V;
	float B = Y + 1.772f * U;

	// Clamp to [0, 1]
	R = fmaxf(0.0f, fminf(1.0f, R));
	G = fmaxf(0.0f, fminf(1.0f, G));
	B = fmaxf(0.0f, fminf(1.0f, B));

	// Convert to byte and write as BGRA
	int bgraIndex = (y * width + x) * 4;
	bgraData[bgraIndex + 0] = (unsigned char)(B * 255.0f + 0.5f); // B
	bgraData[bgraIndex + 1] = (unsigned char)(G * 255.0f + 0.5f); // G
	bgraData[bgraIndex + 2] = (unsigned char)(R * 255.0f + 0.5f); // R
	bgraData[bgraIndex + 3] = 255; // A (full opacity)
}

// CUDA kernel to combine RGB NV12 + Alpha NV12 into byte BGRA
extern "C" __global__ void combineNv12ToBgraKernel(unsigned char* rgbNv12Data, unsigned char* alphaNv12Data, unsigned char* bgraData, int width, int height, size_t nv12Pitch) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;

	if (x >= width || y >= height) return;

	int yIndex = y * nv12Pitch + x;
	int uvIndex = (y / 2) * nv12Pitch + (x & ~1);
	size_t uvOffset = (size_t)height * nv12Pitch;

	// Get RGB Y, U, V values
	float Y = (float)rgbNv12Data[yIndex];
	float U = (float)rgbNv12Data[uvOffset + uvIndex];
	float V = (float)rgbNv12Data[uvOffset + uvIndex + 1];

	// Convert YUV to RGB
	Y = (Y - 16.0f) / 219.0f;
	U = (U - 128.0f) / 224.0f;
	V = (V - 128.0f) / 224.0f;

	float R = Y + 1.402f * V;
	float G = Y - 0.344f * U - 0.714f * V;
	float B = Y + 1.772f * U;

	// Clamp RGB to [0, 1]
	R = fmaxf(0.0f, fminf(1.0f, R));
	G = fmaxf(0.0f, fminf(1.0f, G));
	B = fmaxf(0.0f, fminf(1.0f, B));

	// Get alpha from alpha NV12 Y plane (alpha is stored in Y channel)
	float alpha = (float)alphaNv12Data[yIndex] / 255.0f;
	alpha = fmaxf(0.0f, fminf(1.0f, alpha));

	// Convert to byte and write as BGRA
	int bgraIndex = (y * width + x) * 4;
	bgraData[bgraIndex + 0] = (unsigned char)(B * 255.0f + 0.5f); // B
	bgraData[bgraIndex + 1] = (unsigned char)(G * 255.0f + 0.5f); // G
	bgraData[bgraIndex + 2] = (unsigned char)(R * 255.0f + 0.5f); // R
	bgraData[bgraIndex + 3] = (unsigned char)(alpha * 255.0f + 0.5f); // A
}

#endif // __CUDACC__

void launchByteNv12ToPlanarFloatRgb(unsigned char* nv12Data, float* rgbData, int width, int height, size_t nv12Pitch, CUstream stream) {
    unsigned int blockSizeX = 16;
    unsigned int blockSizeY = 16;
    unsigned int gridSizeX = (width + blockSizeX - 1) / blockSizeX;
    unsigned int gridSizeY = (height + blockSizeY - 1) / blockSizeY;
    try {
        CUfunction kernel = KernelHelpers::GetKernelFunction("ByteNv12ToPlanarFloatRgbKernel");
        void* kernelArgs[] = { &nv12Data, &rgbData, &width, &height, &nv12Pitch };
        CUresult result = KernelHelpers::LaunchKernel(kernel,
            gridSizeX, gridSizeY, 1,
            blockSizeX, blockSizeY, 1,
            0, stream, kernelArgs);
        if (result != CUDA_SUCCESS) {
            const char* errorStr;
            cuGetErrorString(result, &errorStr);
            std::cerr << "launchByteNv12ToPlanarFloatRgb: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
        }
    } catch (const std::exception& e) {
        std::cerr << "launchByteNv12ToPlanarFloatRgb: Exception: " << e.what() << std::endl;
    }
}

void launchYuv420pToPlanarFloatRgb(
	const unsigned char* y_plane,
	const unsigned char* u_plane,
	const unsigned char* v_plane,
	float* rgb_output,
	int width,
	int height,
	int y_stride,
	int u_stride,
	int v_stride,
	CUstream stream) {
	unsigned int blockSizeX = 16;
	unsigned int blockSizeY = 16;
	unsigned int gridSizeX = (width + blockSizeX - 1) / blockSizeX;
	unsigned int gridSizeY = (height + blockSizeY - 1) / blockSizeY;
	float* r_plane_out = rgb_output;
	float* g_plane_out = rgb_output + width * height;
	float* b_plane_out = rgb_output + 2 * width * height;
	try {
		CUfunction kernel = KernelHelpers::GetKernelFunction("Yuv420pToPlanarFloatRgbKernel");
		void* kernelArgs[] = { &y_plane, &u_plane, &v_plane, &r_plane_out, &g_plane_out, &b_plane_out, &width, &height, &y_stride, &u_stride, &v_stride };
		CUresult result = KernelHelpers::LaunchKernel(kernel,
			gridSizeX, gridSizeY, 1,
			blockSizeX, blockSizeY, 1,
			0, stream, kernelArgs);
		if (result != CUDA_SUCCESS) {
			const char* errorStr;
			cuGetErrorString(result, &errorStr);
			std::cerr << "launchYuv420pToPlanarFloatRgb: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
		}
	} catch (const std::exception& e) {
		std::cerr << "launchYuv420pToPlanarFloatRgb: Exception: " << e.what() << std::endl;
	}
}

void launchYuv420pToNv12(const unsigned char* y_plane, const unsigned char* u_plane, const unsigned char* v_plane, unsigned char* nv12_buffer, int width, int height, int y_stride, int u_stride, int v_stride, int nv12_pitch, CUstream stream) {
	unsigned int blockSizeX = 16;
	unsigned int blockSizeY = 16;
	unsigned int gridSizeX = (width + blockSizeX - 1) / blockSizeX;
	unsigned int gridSizeY = (height + blockSizeY - 1) / blockSizeY;
	try {
		CUfunction kernel = KernelHelpers::GetKernelFunction("Yuv420pToNv12Kernel");
		void* kernelArgs[] = { &y_plane, &u_plane, &v_plane, &nv12_buffer, &width, &height, &y_stride, &u_stride, &v_stride, &nv12_pitch };
		CUresult result = KernelHelpers::LaunchKernel(kernel,
			gridSizeX, gridSizeY, 1,
			blockSizeX, blockSizeY, 1,
			0, stream, kernelArgs);
		if (result != CUDA_SUCCESS) {
			const char* errorStr;
			cuGetErrorString(result, &errorStr);
			std::cerr << "launchYuv420pToNv12: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
		}
	} catch (const std::exception& e) {
		std::cerr << "launchYuv420pToNv12: Exception: " << e.what() << std::endl;
	}
}

void launchExtractAlphaFromNv12(unsigned char* nv12Data, float* alphaData, int width, int height, size_t nv12Pitch, CUstream stream) {
	unsigned int blockSizeX = 16;
	unsigned int blockSizeY = 16;
	unsigned int gridSizeX = (width + blockSizeX - 1) / blockSizeX;
	unsigned int gridSizeY = (height + blockSizeY - 1) / blockSizeY;
	try {
		CUfunction kernel = KernelHelpers::GetKernelFunction("extractAlphaFromNv12Kernel");
		void* kernelArgs[] = { &nv12Data, &alphaData, &width, &height, &nv12Pitch };
		CUresult result = KernelHelpers::LaunchKernel(kernel,
			gridSizeX, gridSizeY, 1,
			blockSizeX, blockSizeY, 1,
			0, stream, kernelArgs);
		if (result != CUDA_SUCCESS) {
			const char* errorStr;
			cuGetErrorString(result, &errorStr);
			std::cerr << "launchExtractAlphaFromNv12: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
		}
	} catch (const std::exception& e) {
		std::cerr << "launchExtractAlphaFromNv12: Exception: " << e.what() << std::endl;
	}
}

void launchNv12ToBgra(unsigned char* nv12Data, unsigned char* bgraData, int width, int height, size_t nv12Pitch, CUstream stream) {
	unsigned int blockSizeX = 16;
	unsigned int blockSizeY = 16;
	unsigned int gridSizeX = (width + blockSizeX - 1) / blockSizeX;
	unsigned int gridSizeY = (height + blockSizeY - 1) / blockSizeY;
	try {
		CUfunction kernel = KernelHelpers::GetKernelFunction("nv12ToBgraKernel");
		void* kernelArgs[] = { &nv12Data, &bgraData, &width, &height, &nv12Pitch };
		CUresult result = KernelHelpers::LaunchKernel(kernel,
			gridSizeX, gridSizeY, 1,
			blockSizeX, blockSizeY, 1,
			0, stream, kernelArgs);
		if (result != CUDA_SUCCESS) {
			const char* errorStr;
			cuGetErrorString(result, &errorStr);
			std::cerr << "launchNv12ToBgra: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
		}
	} catch (const std::exception& e) {
		std::cerr << "launchNv12ToBgra: Exception: " << e.what() << std::endl;
	}
}

void launchCombineNv12ToBgra(unsigned char* rgbNv12Data, unsigned char* alphaNv12Data, unsigned char* bgraData, int width, int height, size_t nv12Pitch, CUstream stream) {
	unsigned int blockSizeX = 16;
	unsigned int blockSizeY = 16;
	unsigned int gridSizeX = (width + blockSizeX - 1) / blockSizeX;
	unsigned int gridSizeY = (height + blockSizeY - 1) / blockSizeY;
	try {
		CUfunction kernel = KernelHelpers::GetKernelFunction("combineNv12ToBgraKernel");
		void* kernelArgs[] = { &rgbNv12Data, &alphaNv12Data, &bgraData, &width, &height, &nv12Pitch };
		CUresult result = KernelHelpers::LaunchKernel(kernel,
			gridSizeX, gridSizeY, 1,
			blockSizeX, blockSizeY, 1,
			0, stream, kernelArgs);
		if (result != CUDA_SUCCESS) {
			const char* errorStr;
			cuGetErrorString(result, &errorStr);
			std::cerr << "launchCombineNv12ToBgra: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
		}
	} catch (const std::exception& e) {
		std::cerr << "launchCombineNv12ToBgra: Exception: " << e.what() << std::endl;
	}
}

bool LaunchPlanarFloatRgbToYuv420(const float* d_rgbData, uint8_t* d_yuvData,
	int width, int height, int yPitch, int uvPitch, CUstream stream) {
#ifdef __CUDACC__
	unsigned int blockSizeX = 16;
	unsigned int blockSizeY = 16;
	unsigned int gridSizeX = (width + blockSizeX - 1) / blockSizeX;
	unsigned int gridSizeY = (height + blockSizeY - 1) / blockSizeY;
	try {
		CUfunction kernel = KernelHelpers::GetKernelFunction("planarFloatRgbToYuv420Kernel");
		void* kernelArgs[] = { &d_rgbData, &d_yuvData, &width, &height, &yPitch, &uvPitch };
		CUresult result = KernelHelpers::LaunchKernel(kernel,
			gridSizeX, gridSizeY, 1,
			blockSizeX, blockSizeY, 1,
			0, stream, kernelArgs);
		if (result != CUDA_SUCCESS) {
			const char* errorStr; cuGetErrorString(result, &errorStr);
			std::cerr << "LaunchPlanarFloatRgbToYuv420: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
		}
		return result == CUDA_SUCCESS;
	} catch (const std::exception& e) {
		std::cerr << "LaunchPlanarFloatRgbToYuv420: Exception: " << e.what() << std::endl;
		return false;
	}
#else
	return false;
#endif
}

// Wrappers for YUV420 <-> float alpha conversions
bool LaunchFloatAlphaToYuv420(const float* d_alphaData, uint8_t* d_yuvData,
	int width, int height, int yPitch, int uvPitch, CUstream stream) {
#ifdef __CUDACC__
	unsigned int blockSizeX = 16;
	unsigned int blockSizeY = 16;
	unsigned int gridSizeX = (width + blockSizeX - 1) / blockSizeX;
	unsigned int gridSizeY = (height + blockSizeY - 1) / blockSizeY;
	try {
		CUfunction kernel = KernelHelpers::GetKernelFunction("convertFloatAlphaToYuv420Kernel");
		void* kernelArgs[] = { &d_alphaData, &d_yuvData, &width, &height, &yPitch, &uvPitch };
		CUresult result = KernelHelpers::LaunchKernel(kernel,
			gridSizeX, gridSizeY, 1,
			blockSizeX, blockSizeY, 1,
			0, stream, kernelArgs);
		if (result != CUDA_SUCCESS) {
			const char* errorStr; cuGetErrorString(result, &errorStr);
			std::cerr << "LaunchFloatAlphaToYuv420: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
		}
		return result == CUDA_SUCCESS;
	} catch (const std::exception& e) {
		std::cerr << "LaunchFloatAlphaToYuv420: Exception: " << e.what() << std::endl;
		return false;
	}
#else
	return false;
#endif
}

bool LaunchYuv420ToFloatAlpha(const uint8_t* d_yuvData, float* d_alphaData,
	int width, int height, int yPitch, CUstream stream) {
#ifdef __CUDACC__
	unsigned int blockSizeX = 16;
	unsigned int blockSizeY = 16;
	unsigned int gridSizeX = (width + blockSizeX - 1) / blockSizeX;
	unsigned int gridSizeY = (height + blockSizeY - 1) / blockSizeY;
	try {
		CUfunction kernel = KernelHelpers::GetKernelFunction("convertYuv420ToFloatAlphaKernel");
		void* kernelArgs[] = { &d_yuvData, &d_alphaData, &width, &height, &yPitch };
		CUresult result = KernelHelpers::LaunchKernel(kernel,
			gridSizeX, gridSizeY, 1,
			blockSizeX, blockSizeY, 1,
			0, stream, kernelArgs);
		if (result != CUDA_SUCCESS) {
			const char* errorStr; cuGetErrorString(result, &errorStr);
			std::cerr << "LaunchYuv420ToFloatAlpha: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
		}
		return result == CUDA_SUCCESS;
	} catch (const std::exception& e) {
		std::cerr << "LaunchYuv420ToFloatAlpha: Exception: " << e.what() << std::endl;
		return false;
	}
#else
	return false;
#endif
}

// CUDA kernel to combine planar float RGB and float alpha to interleaved byte BGRA
extern "C" __global__ void combinePlanarFloatRgbAlphaToInterleavedByteBgraKernel(
	const float* d_rgbPlanar,
	const float* d_alpha,
	uint8_t* d_bgraInterleaved,
	int width,
	int height) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;
	if (x >= width || y >= height) return;

	int pixelIdx = y * width + x;
	int planeSize = width * height;

	// Read planar float RGB values [0..1]
	float r = d_rgbPlanar[pixelIdx];
	float g = d_rgbPlanar[pixelIdx + planeSize];
	float b = d_rgbPlanar[pixelIdx + 2 * planeSize];

	// Read alpha value [0..1]
	float a = d_alpha[pixelIdx];

	// Clamp to [0..1]
	r = fminf(fmaxf(r, 0.0f), 1.0f);
	g = fminf(fmaxf(g, 0.0f), 1.0f);
	b = fminf(fmaxf(b, 0.0f), 1.0f);
	a = fminf(fmaxf(a, 0.0f), 1.0f);

	// Convert to byte [0..255] and write as interleaved BGRA
	int outIdx = pixelIdx * 4;
	d_bgraInterleaved[outIdx + 0] = static_cast<uint8_t>(b * 255.0f + 0.5f); // B
	d_bgraInterleaved[outIdx + 1] = static_cast<uint8_t>(g * 255.0f + 0.5f); // G
	d_bgraInterleaved[outIdx + 2] = static_cast<uint8_t>(r * 255.0f + 0.5f); // R
	d_bgraInterleaved[outIdx + 3] = static_cast<uint8_t>(a * 255.0f + 0.5f); // A
}

void launchCombinePlanarFloatRgbAlphaToInterleavedByteBgra(
	const float* d_rgbPlanar, const float* d_alpha,
	uint8_t* d_bgraInterleaved,
	int width, int height, CUstream stream) {
	unsigned int blockSizeX = 16;
	unsigned int blockSizeY = 16;
	unsigned int gridSizeX = (width + blockSizeX - 1) / blockSizeX;
	unsigned int gridSizeY = (height + blockSizeY - 1) / blockSizeY;
	try {
		CUfunction kernel = KernelHelpers::GetKernelFunction("combinePlanarFloatRgbAlphaToInterleavedByteBgraKernel");
		void* kernelArgs[] = { &d_rgbPlanar, &d_alpha, &d_bgraInterleaved, &width, &height };
		CUresult result = KernelHelpers::LaunchKernel(kernel,
			gridSizeX, gridSizeY, 1,
			blockSizeX, blockSizeY, 1,
			0, stream, kernelArgs);
		if (result != CUDA_SUCCESS) {
			const char* errorStr;
			cuGetErrorString(result, &errorStr);
			std::cerr << "launchCombinePlanarFloatRgbAlphaToInterleavedByteBgra: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
		}
	} catch (const std::exception& e) {
		std::cerr << "launchCombinePlanarFloatRgbAlphaToInterleavedByteBgra: Exception: " << e.what() << std::endl;
	}
}

// CUDA kernel to convert planar float RGB to interleaved float RGBA
extern "C" __global__ void planarFloatRgbToInterleavedFloatRgbaKernel(
	const float* d_rgbPlanar,
	float* d_rgbaInterleaved,
	int width,
	int height) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;
	if (x >= width || y >= height) return;

	int pixelIdx = y * width + x;
	int planeSize = width * height;

	// Read planar float RGB values
	float r = d_rgbPlanar[pixelIdx];
	float g = d_rgbPlanar[pixelIdx + planeSize];
	float b = d_rgbPlanar[pixelIdx + 2 * planeSize];

	// Write as interleaved RGBA with alpha = 1.0
	int outIdx = pixelIdx * 4;
	d_rgbaInterleaved[outIdx + 0] = r;
	d_rgbaInterleaved[outIdx + 1] = g;
	d_rgbaInterleaved[outIdx + 2] = b;
	d_rgbaInterleaved[outIdx + 3] = 1.0f;
}

void launchPlanarFloatRgbToInterleavedFloatRgba(
	const float* d_rgbPlanar,
	float* d_rgbaInterleaved,
	int width, int height, CUstream stream) {
	unsigned int blockSizeX = 16;
	unsigned int blockSizeY = 16;
	unsigned int gridSizeX = (width + blockSizeX - 1) / blockSizeX;
	unsigned int gridSizeY = (height + blockSizeY - 1) / blockSizeY;
	try {
		CUfunction kernel = KernelHelpers::GetKernelFunction("planarFloatRgbToInterleavedFloatRgbaKernel");
		void* kernelArgs[] = { &d_rgbPlanar, &d_rgbaInterleaved, &width, &height };
		CUresult result = KernelHelpers::LaunchKernel(kernel,
			gridSizeX, gridSizeY, 1,
			blockSizeX, blockSizeY, 1,
			0, stream, kernelArgs);
		if (result != CUDA_SUCCESS) {
			const char* errorStr;
			cuGetErrorString(result, &errorStr);
			std::cerr << "launchPlanarFloatRgbToInterleavedFloatRgba: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
		}
	} catch (const std::exception& e) {
		std::cerr << "launchPlanarFloatRgbToInterleavedFloatRgba: Exception: " << e.what() << std::endl;
	}
}

// CUDA kernel to convert planar float RGB to interleaved byte BGRA (alpha = 255)
extern "C" __global__ void planarFloatRgbToInterleavedByteBgraKernel(
	const float* d_rgbPlanar,
	uint8_t* d_bgraInterleaved,
	int width,
	int height) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;
	if (x >= width || y >= height) return;

	int pixelIdx = y * width + x;
	int planeSize = width * height;

	// Read planar float RGB values [0..1]
	float r = d_rgbPlanar[pixelIdx];
	float g = d_rgbPlanar[pixelIdx + planeSize];
	float b = d_rgbPlanar[pixelIdx + 2 * planeSize];

	// Clamp to [0..1]
	r = fminf(fmaxf(r, 0.0f), 1.0f);
	g = fminf(fmaxf(g, 0.0f), 1.0f);
	b = fminf(fmaxf(b, 0.0f), 1.0f);

	// Convert to byte [0..255] and write as interleaved BGRA
	int outIdx = pixelIdx * 4;
	d_bgraInterleaved[outIdx + 0] = static_cast<uint8_t>(b * 255.0f + 0.5f); // B
	d_bgraInterleaved[outIdx + 1] = static_cast<uint8_t>(g * 255.0f + 0.5f); // G
	d_bgraInterleaved[outIdx + 2] = static_cast<uint8_t>(r * 255.0f + 0.5f); // R
	d_bgraInterleaved[outIdx + 3] = 255; // A = fully opaque
}

void launchPlanarFloatRgbToInterleavedByteBgra(
	const float* d_rgbPlanar,
	uint8_t* d_bgraInterleaved,
	int width, int height, CUstream stream) {
	unsigned int blockSizeX = 16;
	unsigned int blockSizeY = 16;
	unsigned int gridSizeX = (width + blockSizeX - 1) / blockSizeX;
	unsigned int gridSizeY = (height + blockSizeY - 1) / blockSizeY;
	try {
		CUfunction kernel = KernelHelpers::GetKernelFunction("planarFloatRgbToInterleavedByteBgraKernel");
		void* kernelArgs[] = { &d_rgbPlanar, &d_bgraInterleaved, &width, &height };
		CUresult result = KernelHelpers::LaunchKernel(kernel,
			gridSizeX, gridSizeY, 1,
			blockSizeX, blockSizeY, 1,
			0, stream, kernelArgs);
		if (result != CUDA_SUCCESS) {
			const char* errorStr;
			cuGetErrorString(result, &errorStr);
			std::cerr << "launchPlanarFloatRgbToInterleavedByteBgra: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
		}
	} catch (const std::exception& e) {
		std::cerr << "launchPlanarFloatRgbToInterleavedByteBgra: Exception: " << e.what() << std::endl;
	}
}

// CUDA kernel to convert planar float RGB to interleaved byte RGBA (alpha = 255)
extern "C" __global__ void planarFloatRgbToInterleavedByteRgbaKernel(
	const float* d_rgbPlanar,
	uint8_t* d_rgbaInterleaved,
	int width,
	int height) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;
	if (x >= width || y >= height) return;

	int pixelIdx = y * width + x;
	int planeSize = width * height;

	// Read planar float RGB values [0..1]
	float r = d_rgbPlanar[pixelIdx];
	float g = d_rgbPlanar[pixelIdx + planeSize];
	float b = d_rgbPlanar[pixelIdx + 2 * planeSize];

	// Clamp to [0..1]
	r = fminf(fmaxf(r, 0.0f), 1.0f);
	g = fminf(fmaxf(g, 0.0f), 1.0f);
	b = fminf(fmaxf(b, 0.0f), 1.0f);

	// Convert to byte [0..255] and write as interleaved RGBA
	int outIdx = pixelIdx * 4;
	d_rgbaInterleaved[outIdx + 0] = static_cast<uint8_t>(r * 255.0f + 0.5f); // R
	d_rgbaInterleaved[outIdx + 1] = static_cast<uint8_t>(g * 255.0f + 0.5f); // G
	d_rgbaInterleaved[outIdx + 2] = static_cast<uint8_t>(b * 255.0f + 0.5f); // B
	d_rgbaInterleaved[outIdx + 3] = 255; // A = fully opaque
}

void launchPlanarFloatRgbToInterleavedByteRgba(
	const float* d_rgbPlanar,
	uint8_t* d_rgbaInterleaved,
	int width, int height, CUstream stream) {
	unsigned int blockSizeX = 16;
	unsigned int blockSizeY = 16;
	unsigned int gridSizeX = (width + blockSizeX - 1) / blockSizeX;
	unsigned int gridSizeY = (height + blockSizeY - 1) / blockSizeY;
	try {
		CUfunction kernel = KernelHelpers::GetKernelFunction("planarFloatRgbToInterleavedByteRgbaKernel");
		void* kernelArgs[] = { &d_rgbPlanar, &d_rgbaInterleaved, &width, &height };
		CUresult result = KernelHelpers::LaunchKernel(kernel,
			gridSizeX, gridSizeY, 1,
			blockSizeX, blockSizeY, 1,
			0, stream, kernelArgs);
		if (result != CUDA_SUCCESS) {
			const char* errorStr;
			cuGetErrorString(result, &errorStr);
			std::cerr << "launchPlanarFloatRgbToInterleavedByteRgba: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
		}
	} catch (const std::exception& e) {
		std::cerr << "launchPlanarFloatRgbToInterleavedByteRgba: Exception: " << e.what() << std::endl;
	}
}

// CUDA kernel to convert float alpha [0-1] to byte alpha [0-255]
extern "C" __global__ void floatAlphaToByteAlphaKernel(
	const float* d_floatAlpha,
	uint8_t* d_byteAlpha,
	int width,
	int height) {
	int x = blockIdx.x * blockDim.x + threadIdx.x;
	int y = blockIdx.y * blockDim.y + threadIdx.y;
	if (x >= width || y >= height) return;

	int idx = y * width + x;
	float alpha = d_floatAlpha[idx];

	// Clamp to [0..1]
	alpha = fminf(fmaxf(alpha, 0.0f), 1.0f);

	// Convert to byte [0..255]
	d_byteAlpha[idx] = static_cast<uint8_t>(alpha * 255.0f + 0.5f);
}

void launchFloatAlphaToByteAlpha(
	const float* d_floatAlpha,
	uint8_t* d_byteAlpha,
	int width, int height, CUstream stream) {
	unsigned int blockSizeX = 16;
	unsigned int blockSizeY = 16;
	unsigned int gridSizeX = (width + blockSizeX - 1) / blockSizeX;
	unsigned int gridSizeY = (height + blockSizeY - 1) / blockSizeY;
	try {
		CUfunction kernel = KernelHelpers::GetKernelFunction("floatAlphaToByteAlphaKernel");
		void* kernelArgs[] = { &d_floatAlpha, &d_byteAlpha, &width, &height };
		CUresult result = KernelHelpers::LaunchKernel(kernel,
			gridSizeX, gridSizeY, 1,
			blockSizeX, blockSizeY, 1,
			0, stream, kernelArgs);
		if (result != CUDA_SUCCESS) {
			const char* errorStr;
			cuGetErrorString(result, &errorStr);
			std::cerr << "launchFloatAlphaToByteAlpha: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
		}
	} catch (const std::exception& e) {
		std::cerr << "launchFloatAlphaToByteAlpha: Exception: " << e.what() << std::endl;
	}
}
