﻿  Compiling CUDA source file src\FFmpegIO\FFmpegIOKernels.cu...
  
  F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterLib>"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\bin\nvcc.exe" -gencode=arch=compute_75,code=\"sm_75,compute_75\" --use-local-env -ccbin "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64" -x cu   -Iinclude_internal\FFmpegIO -I"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include" -IF:\VccLibs\ffmpeg\include -IF:\VccLibs\onnxruntime\include -IF:\VccLibs\TensorRT\include -I"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include" -I"C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\include"     --keep-dir x64\Debug  -maxrregcount=0    --machine 64 --compile -cudart static --expt-relaxed-constexpr -g  -DIMAGEMATTERLIB_EXPORTS -D_CRT_SECURE_NO_WARNINGS -D_WINDLL -D_UNICODE -DUNICODE -Xcompiler "/EHsc /W3 /nologo /Od /FS /Zi /RTC1 /MDd " -Xcompiler "/Fdx64\Debug\vc143.pdb" -o F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterLib\x64\Debug\FFmpegIOKernels.cu.obj "F:\Catechese\EditeurAudioVideo\ImageMatter\ImageMatterLib\src\FFmpegIO\FFmpegIOKernels.cu" 
  FFmpegIOKernels.cu
  tmpxft_0000a164_00000000-7_FFmpegIOKernels.cudafe1.cpp
  D3D11VideoReader.cpp
  D3D11VideoReaderAPI.cpp
  DirectVideoReader.cpp
  DirectVideoWriter.cpp
  MultiVideoReader.cpp
  VideoBackgroundRemover.cpp
  Génération de code en cours...
  /NODEFAULTLIB:LIBCMT traité
  LINK : 0 nouveaux modules et 7 (sur 92) modules ont changé depuis la liaison précédente
  LINK : espace de remplissage épuisé (3) : liaison complète en cours
  Appel de LINK.EXE :
  /NODEFAULTLIB:LIBCMT traité
   /ERRORREPORT:PROMPT @C:\Users\<USER>\AppData\Local\Temp\MSBuildTemp\tmp7e3615fa9a5d4f128487feddf5ee6e82.rsp
   /nologo
   /incremental
   /fullbuild
  
  Début de la passe 1
  /DEFAULTLIB:msvcprtd traité
  /DEFAULTLIB:MSVCRTD traité
  /DEFAULTLIB:OLDNAMES traité
  /DEFAULTLIB:uuid.lib traité
  /DEFAULTLIB:avcodec.lib traité
  /DEFAULTLIB:avformat.lib traité
  /DEFAULTLIB:avutil.lib traité
  /DEFAULTLIB:cuda.lib traité
  
  Recherche en cours des bibliothèques
      Recherche en cours F:\VccLibs\Tensorrt\lib\nvinfer_10.lib :
        createInferRuntime_INTERNAL trouvé
          Référencé dans ImageMattingTensorRt.obj
          Chargé nvinfer_10.lib(nvinfer_10.dll)
        __IMPORT_DESCRIPTOR_nvinfer_10 trouvé
          Référencé dans nvinfer_10.lib(nvinfer_10.dll)
          Chargé nvinfer_10.lib(nvinfer_10.dll)
        __NULL_IMPORT_DESCRIPTOR trouvé
          Référencé dans nvinfer_10.lib(nvinfer_10.dll)
          Chargé nvinfer_10.lib(nvinfer_10.dll)
        nvinfer_10_NULL_THUNK_DATA trouvé
          Référencé dans nvinfer_10.lib(nvinfer_10.dll)
          Chargé nvinfer_10.lib(nvinfer_10.dll)
      Recherche en cours F:\VccLibs\Tensorrt\lib\nvonnxparser_10.lib :
      Recherche en cours F:\VccLibs\Tensorrt\lib\nvinfer_plugin_10.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cudart_static.lib :
        cudaLaunchKernel trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé cudart_static.lib(cudart_generated_cuda_runtime_api.obj)
        __cudaPopCallConfiguration trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé cudart_static.lib(cudart_cuda_runtime_api.obj)
        cudaGraphicsD3D11RegisterResource trouvé
          Référencé dans D3D11VideoReader.obj
          Chargé cudart_static.lib(cudart_generated_cuda_d3d11_interop.obj)
        "public: enum cudaError __cdecl cudart::globalState::initializeDriver(void)" (?initializeDriver@globalState@cudart@@QEAA?AW4cudaError@@XZ) trouvé
          Référencé dans cudart_static.lib(cudart_generated_cuda_runtime_api.obj)
          Référencé dans cudart_static.lib(cudart_cuda_runtime_api.obj)
          Référencé dans cudart_static.lib(cudart_generated_cuda_d3d11_interop.obj)
          Chargé cudart_static.lib(cudart_cudart_global.obj)
        "public: enum cudaError __cdecl cudart::contextState::getDriverEntryOrName(void const *,struct cudart::cudaEntryFunction * *,char const * *)" (?getDriverEntryOrName@contextState@cudart@@QEAA?AW4cudaError@@PEBXPEAPEAUcudaEntryFunction@2@PEAPEBD@Z) trouvé
          Référencé dans cudart_static.lib(cudart_generated_cuda_runtime_api.obj)
          Chargé cudart_static.lib(cudart_cudart_context.obj)
        __cudaGetExportTableInternal trouvé
          Référencé dans cudart_static.lib(cudart_generated_cuda_runtime_api.obj)
          Référencé dans cudart_static.lib(cudart_cuda_runtime_api.obj)
          Référencé dans cudart_static.lib(cudart_generated_cuda_d3d11_interop.obj)
          Chargé cudart_static.lib(cudart_cudart_etbl.obj)
        "void __cdecl cudart::cuosEnterCriticalSection(struct _RTL_CRITICAL_SECTION *)" (?cuosEnterCriticalSection@cudart@@YAXPEAU_RTL_CRITICAL_SECTION@@@Z) trouvé
          Référencé dans cudart_static.lib(cudart_cuda_runtime_api.obj)
          Référencé dans cudart_static.lib(cudart_cudart_global.obj)
          Référencé dans cudart_static.lib(cudart_cudart_context.obj)
          Chargé cudart_static.lib(cudart_cuoswin32.obj)
  /DEFAULTLIB:Shell32.lib traité
  /DEFAULTLIB:Advapi32.lib traité
        "public: enum cudaError __cdecl cudart::contextStateManager::getRuntimeContextState(class cudart::contextState * *,bool)" (?getRuntimeContextState@contextStateManager@cudart@@QEAA?AW4cudaError@@PEAPEAVcontextState@2@_N@Z) trouvé
          Référencé dans cudart_static.lib(cudart_cuda_runtime_api.obj)
          Référencé dans cudart_static.lib(cudart_cudart_global.obj)
          Chargé cudart_static.lib(cudart_cudart_context_mgr.obj)
        "public: enum cudaError __cdecl cudart::threadLaunchState::pushConfig(struct dim3,struct dim3,unsigned __int64,struct CUstream_st *)" (?pushConfig@threadLaunchState@cudart@@QEAA?AW4cudaError@@Udim3@@0_KPEAUCUstream_st@@@Z) trouvé
          Référencé dans cudart_static.lib(cudart_cuda_runtime_api.obj)
          Chargé cudart_static.lib(cudart_cudart_thread.obj)
        "enum cudaError __cdecl cudart::getThreadState(struct cudart::threadState * *)" (?getThreadState@cudart@@YA?AW4cudaError@@PEAPEAUthreadState@1@@Z) trouvé
          Référencé dans cudart_static.lib(cudart_cuda_runtime_api.obj)
          Référencé dans cudart_static.lib(cudart_cudart_context_mgr.obj)
          Chargé cudart_static.lib(cudart_cudart_tls.obj)
        "enum cudaError __cdecl cudart::arrayHelper::getChannelDesc(struct cudaArray const *,struct cudaChannelFormatDesc *)" (?getChannelDesc@arrayHelper@cudart@@YA?AW4cudaError@@PEBUcudaArray@@PEAUcudaChannelFormatDesc@@@Z) trouvé
          Référencé dans cudart_static.lib(cudart_cuda_runtime_api.obj)
          Chargé cudart_static.lib(cudart_cudart_array.obj)
        "enum cudaError __cdecl cudart::driverHelper::getCurrentContext(struct CUctx_st * *)" (?getCurrentContext@driverHelper@cudart@@YA?AW4cudaError@@PEAPEAUCUctx_st@@@Z) trouvé
          Référencé dans cudart_static.lib(cudart_cuda_runtime_api.obj)
          Chargé cudart_static.lib(cudart_cudart_helper.obj)
        "unsigned int const cudart::cudartErrorTableEntryCount" (?cudartErrorTableEntryCount@cudart@@3IB) trouvé
          Référencé dans cudart_static.lib(cudart_cuda_runtime_api.obj)
          Chargé cudart_static.lib(cudart_generated_error_tables.obj)
        "int __cdecl cudart::cuosOnceWithArgs(unsigned int volatile *,int (__cdecl*)(void *),void *)" (?cuosOnceWithArgs@cudart@@YAHPECIP6AHPEAX@Z1@Z) trouvé
          Référencé dans cudart_static.lib(cudart_cudart_global.obj)
          Chargé cudart_static.lib(cudart_cuos_once.obj)
        "struct CUetblToolsRuntimeInstance_st const cudart::g_etblToolsRuntimeInstance" (?g_etblToolsRuntimeInstance@cudart@@3UCUetblToolsRuntimeInstance_st@@B) trouvé
          Référencé dans cudart_static.lib(cudart_cudart_etbl.obj)
          Chargé cudart_static.lib(cudart_tools_runtime_instance.obj)
        "struct CUetblToolsRuntimeCallbacks_st const cudart::g_etblToolsRuntimeCallbacks" (?g_etblToolsRuntimeCallbacks@cudart@@3UCUetblToolsRuntimeCallbacks_st@@B) trouvé
          Référencé dans cudart_static.lib(cudart_cudart_etbl.obj)
          Chargé cudart_static.lib(cudart_tools_runtime_callbacks.obj)
        "struct CUetblRuntimeErrorTable_st const cudart::g_etblRuntimeErrorTable" (?g_etblRuntimeErrorTable@cudart@@3UCUetblRuntimeErrorTable_st@@B) trouvé
          Référencé dans cudart_static.lib(cudart_cudart_etbl.obj)
          Chargé cudart_static.lib(cudart_runtime_error_table.obj)
        nvLoadSystemLibraryExA trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
        "struct CUtoolsRuntimeFunctionDescriptor_st const * const cudart::g_descriptorTable" (?g_descriptorTable@cudart@@3QEBUCUtoolsRuntimeFunctionDescriptor_st@@EB) trouvé
          Référencé dans cudart_static.lib(cudart_tools_runtime_callbacks.obj)
          Chargé cudart_static.lib(cudart_generated_cuda_runtime_api_names.obj)
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cuda.lib :
        cuGetErrorString trouvé
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans ProcessBodyRegions.obj
          Référencé dans RAII.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans ImageMatting.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé cuda.lib(nvcuda.dll)
        cuCtxSynchronize trouvé
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans Helpers_Kernels.cu.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans RAII.obj
          Chargé cuda.lib(nvcuda.dll)
        cuLaunchKernel trouvé
          Référencé dans Helpers_Kernels.cu.obj
          Référencé dans KernelManager.obj
          Chargé cuda.lib(nvcuda.dll)
        cuMemAlloc_v2 trouvé
          Référencé dans ProcessBodyRegions.obj
          Référencé dans RAII.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Chargé cuda.lib(nvcuda.dll)
        cuMemFree_v2 trouvé
          Référencé dans ProcessBodyRegions.obj
          Référencé dans RAII.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Chargé cuda.lib(nvcuda.dll)
        cuMemcpy2DAsync_v2 trouvé
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans ImageMatting.obj
          Chargé cuda.lib(nvcuda.dll)
        cuStreamCreate trouvé
          Référencé dans RAII.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Chargé cuda.lib(nvcuda.dll)
        cuStreamSynchronize trouvé
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans Helpers.obj
          Référencé dans ProcessBodyRegions.obj
          Référencé dans RAII.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans ImageMattingOnnx.obj
          Chargé cuda.lib(nvcuda.dll)
        cuStreamDestroy_v2 trouvé
          Référencé dans RAII.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Chargé cuda.lib(nvcuda.dll)
        cuInit trouvé
          Référencé dans D3D11VideoReader.obj
          Référencé dans RAII.obj
          Référencé dans VideoBackgroundRemover.obj
          Chargé cuda.lib(nvcuda.dll)
        cuDeviceGet trouvé
          Référencé dans D3D11VideoReader.obj
          Chargé cuda.lib(nvcuda.dll)
        cuDeviceGetCount trouvé
          Référencé dans D3D11VideoReader.obj
          Chargé cuda.lib(nvcuda.dll)
        cuCtxCreate_v2 trouvé
          Référencé dans D3D11VideoReader.obj
          Référencé dans RAII.obj
          Référencé dans VideoBackgroundRemover.obj
          Chargé cuda.lib(nvcuda.dll)
        cuCtxDestroy_v2 trouvé
          Référencé dans D3D11VideoReader.obj
          Référencé dans RAII.obj
          Référencé dans VideoBackgroundRemover.obj
          Chargé cuda.lib(nvcuda.dll)
        cuMemcpyDtoDAsync_v2 trouvé
          Référencé dans ImageMatting.obj
          Chargé cuda.lib(nvcuda.dll)
        cuCtxSetCurrent trouvé
          Référencé dans ImageMattingOnnx.obj
          Référencé dans HeadDetector.obj
          Référencé dans RAII.obj
          Référencé dans VideoBackgroundRemover.obj
          Chargé cuda.lib(nvcuda.dll)
        cuCtxGetCurrent trouvé
          Référencé dans ImageMattingOnnx.obj
          Référencé dans HeadDetector.obj
          Référencé dans RAII.obj
          Chargé cuda.lib(nvcuda.dll)
        cuMemcpyHtoD_v2 trouvé
          Référencé dans Helpers.obj
          Référencé dans ProcessBodyRegions.obj
          Chargé cuda.lib(nvcuda.dll)
        cuMemcpyDtoH_v2 trouvé
          Référencé dans Helpers.obj
          Référencé dans ProcessBodyRegions.obj
          Chargé cuda.lib(nvcuda.dll)
        cuMemcpyHtoDAsync_v2 trouvé
          Référencé dans Helpers.obj
          Chargé cuda.lib(nvcuda.dll)
        cuMemcpyDtoHAsync_v2 trouvé
          Référencé dans Helpers.obj
          Chargé cuda.lib(nvcuda.dll)
        cuModuleLoad trouvé
          Référencé dans KernelManager.obj
          Chargé cuda.lib(nvcuda.dll)
        cuModuleLoadData trouvé
          Référencé dans KernelManager.obj
          Chargé cuda.lib(nvcuda.dll)
        cuModuleUnload trouvé
          Référencé dans KernelManager.obj
          Chargé cuda.lib(nvcuda.dll)
        cuModuleGetFunction trouvé
          Référencé dans KernelManager.obj
          Chargé cuda.lib(nvcuda.dll)
        cuMemcpy2D_v2 trouvé
          Référencé dans ProcessBodyRegions.obj
          Chargé cuda.lib(nvcuda.dll)
        cuMemsetD8Async trouvé
          Référencé dans ProcessBodyRegions.obj
          Chargé cuda.lib(nvcuda.dll)
        cuArrayCreate_v2 trouvé
          Référencé dans ProcessBodyRegions.obj
          Chargé cuda.lib(nvcuda.dll)
        cuArrayDestroy trouvé
          Référencé dans ProcessBodyRegions.obj
          Chargé cuda.lib(nvcuda.dll)
        cuTexObjectCreate trouvé
          Référencé dans ProcessBodyRegions.obj
          Chargé cuda.lib(nvcuda.dll)
        cuTexObjectDestroy trouvé
          Référencé dans ProcessBodyRegions.obj
          Chargé cuda.lib(nvcuda.dll)
        __IMPORT_DESCRIPTOR_nvcuda trouvé
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Référencé dans cuda.lib(nvcuda.dll)
          Chargé cuda.lib(nvcuda.dll)
        nvcuda_NULL_THUNK_DATA trouvé
          Référencé dans cuda.lib(nvcuda.dll)
          Chargé cuda.lib(nvcuda.dll)
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cublas.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\curand.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cusparse.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cusolver.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cufft.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avcodec.lib :
        av_packet_alloc trouvé
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Chargé avcodec.lib(avcodec-61.dll)
        av_packet_free trouvé
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Chargé avcodec.lib(avcodec-61.dll)
        av_packet_unref trouvé
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Chargé avcodec.lib(avcodec-61.dll)
        avcodec_find_decoder trouvé
          Référencé dans DirectVideoReader.obj
          Chargé avcodec.lib(avcodec-61.dll)
        avcodec_alloc_context3 trouvé
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Chargé avcodec.lib(avcodec-61.dll)
        avcodec_free_context trouvé
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Chargé avcodec.lib(avcodec-61.dll)
        avcodec_parameters_to_context trouvé
          Référencé dans DirectVideoReader.obj
          Chargé avcodec.lib(avcodec-61.dll)
        avcodec_open2 trouvé
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Chargé avcodec.lib(avcodec-61.dll)
        avcodec_send_packet trouvé
          Référencé dans DirectVideoReader.obj
          Chargé avcodec.lib(avcodec-61.dll)
        avcodec_receive_frame trouvé
          Référencé dans DirectVideoReader.obj
          Chargé avcodec.lib(avcodec-61.dll)
        avcodec_flush_buffers trouvé
          Référencé dans DirectVideoReader.obj
          Chargé avcodec.lib(avcodec-61.dll)
        avcodec_find_encoder_by_name trouvé
          Référencé dans DirectVideoWriter.obj
          Chargé avcodec.lib(avcodec-61.dll)
        avcodec_parameters_from_context trouvé
          Référencé dans DirectVideoWriter.obj
          Chargé avcodec.lib(avcodec-61.dll)
        avcodec_send_frame trouvé
          Référencé dans DirectVideoWriter.obj
          Chargé avcodec.lib(avcodec-61.dll)
        avcodec_receive_packet trouvé
          Référencé dans DirectVideoWriter.obj
          Chargé avcodec.lib(avcodec-61.dll)
        __IMPORT_DESCRIPTOR_avcodec-61 trouvé
          Référencé dans avcodec.lib(avcodec-61.dll)
          Référencé dans avcodec.lib(avcodec-61.dll)
          Référencé dans avcodec.lib(avcodec-61.dll)
          Référencé dans avcodec.lib(avcodec-61.dll)
          Référencé dans avcodec.lib(avcodec-61.dll)
          Référencé dans avcodec.lib(avcodec-61.dll)
          Référencé dans avcodec.lib(avcodec-61.dll)
          Référencé dans avcodec.lib(avcodec-61.dll)
          Référencé dans avcodec.lib(avcodec-61.dll)
          Référencé dans avcodec.lib(avcodec-61.dll)
          Référencé dans avcodec.lib(avcodec-61.dll)
          Référencé dans avcodec.lib(avcodec-61.dll)
          Référencé dans avcodec.lib(avcodec-61.dll)
          Référencé dans avcodec.lib(avcodec-61.dll)
          Référencé dans avcodec.lib(avcodec-61.dll)
          Chargé avcodec.lib(avcodec-61.dll)
        avcodec-61_NULL_THUNK_DATA trouvé
          Référencé dans avcodec.lib(avcodec-61.dll)
          Chargé avcodec.lib(avcodec-61.dll)
      Recherche en cours F:\VccLibs\ffmpeg\bin\avformat.lib :
        avformat_network_init trouvé
          Référencé dans DirectVideoReader.obj
          Chargé avformat.lib(avformat-61.dll)
        avformat_open_input trouvé
          Référencé dans DirectVideoReader.obj
          Chargé avformat.lib(avformat-61.dll)
        avformat_find_stream_info trouvé
          Référencé dans DirectVideoReader.obj
          Chargé avformat.lib(avformat-61.dll)
        av_read_frame trouvé
          Référencé dans DirectVideoReader.obj
          Chargé avformat.lib(avformat-61.dll)
        av_seek_frame trouvé
          Référencé dans DirectVideoReader.obj
          Chargé avformat.lib(avformat-61.dll)
        avformat_close_input trouvé
          Référencé dans DirectVideoReader.obj
          Chargé avformat.lib(avformat-61.dll)
        avio_open trouvé
          Référencé dans DirectVideoWriter.obj
          Chargé avformat.lib(avformat-61.dll)
        avio_closep trouvé
          Référencé dans DirectVideoWriter.obj
          Chargé avformat.lib(avformat-61.dll)
        avformat_free_context trouvé
          Référencé dans DirectVideoWriter.obj
          Chargé avformat.lib(avformat-61.dll)
        avformat_new_stream trouvé
          Référencé dans DirectVideoWriter.obj
          Chargé avformat.lib(avformat-61.dll)
        avformat_alloc_output_context2 trouvé
          Référencé dans DirectVideoWriter.obj
          Chargé avformat.lib(avformat-61.dll)
        avformat_write_header trouvé
          Référencé dans DirectVideoWriter.obj
          Chargé avformat.lib(avformat-61.dll)
        av_interleaved_write_frame trouvé
          Référencé dans DirectVideoWriter.obj
          Chargé avformat.lib(avformat-61.dll)
        av_write_trailer trouvé
          Référencé dans DirectVideoWriter.obj
          Chargé avformat.lib(avformat-61.dll)
        __IMPORT_DESCRIPTOR_avformat-61 trouvé
          Référencé dans avformat.lib(avformat-61.dll)
          Référencé dans avformat.lib(avformat-61.dll)
          Référencé dans avformat.lib(avformat-61.dll)
          Référencé dans avformat.lib(avformat-61.dll)
          Référencé dans avformat.lib(avformat-61.dll)
          Référencé dans avformat.lib(avformat-61.dll)
          Référencé dans avformat.lib(avformat-61.dll)
          Référencé dans avformat.lib(avformat-61.dll)
          Référencé dans avformat.lib(avformat-61.dll)
          Référencé dans avformat.lib(avformat-61.dll)
          Référencé dans avformat.lib(avformat-61.dll)
          Référencé dans avformat.lib(avformat-61.dll)
          Référencé dans avformat.lib(avformat-61.dll)
          Référencé dans avformat.lib(avformat-61.dll)
          Chargé avformat.lib(avformat-61.dll)
        avformat-61_NULL_THUNK_DATA trouvé
          Référencé dans avformat.lib(avformat-61.dll)
          Chargé avformat.lib(avformat-61.dll)
      Recherche en cours F:\VccLibs\ffmpeg\bin\avutil.lib :
        av_strerror trouvé
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Chargé avutil.lib(avutil-59.dll)
        av_rescale_q trouvé
          Référencé dans DirectVideoReader.obj
          Chargé avutil.lib(avutil-59.dll)
        av_buffer_ref trouvé
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Chargé avutil.lib(avutil-59.dll)
        av_buffer_unref trouvé
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Chargé avutil.lib(avutil-59.dll)
        av_frame_alloc trouvé
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Chargé avutil.lib(avutil-59.dll)
        av_frame_free trouvé
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Chargé avutil.lib(avutil-59.dll)
        av_hwdevice_ctx_alloc trouvé
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Chargé avutil.lib(avutil-59.dll)
        av_hwdevice_ctx_init trouvé
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Chargé avutil.lib(avutil-59.dll)
        av_get_pix_fmt_name trouvé
          Référencé dans DirectVideoReader.obj
          Chargé avutil.lib(avutil-59.dll)
        av_dict_set trouvé
          Référencé dans DirectVideoWriter.obj
          Chargé avutil.lib(avutil-59.dll)
        av_dict_set_int trouvé
          Référencé dans DirectVideoWriter.obj
          Chargé avutil.lib(avutil-59.dll)
        av_dict_free trouvé
          Référencé dans DirectVideoWriter.obj
          Chargé avutil.lib(avutil-59.dll)
        av_hwframe_ctx_alloc trouvé
          Référencé dans DirectVideoWriter.obj
          Chargé avutil.lib(avutil-59.dll)
        av_hwframe_ctx_init trouvé
          Référencé dans DirectVideoWriter.obj
          Chargé avutil.lib(avutil-59.dll)
        av_hwframe_get_buffer trouvé
          Référencé dans DirectVideoWriter.obj
          Chargé avutil.lib(avutil-59.dll)
        __IMPORT_DESCRIPTOR_avutil-59 trouvé
          Référencé dans avutil.lib(avutil-59.dll)
          Référencé dans avutil.lib(avutil-59.dll)
          Référencé dans avutil.lib(avutil-59.dll)
          Référencé dans avutil.lib(avutil-59.dll)
          Référencé dans avutil.lib(avutil-59.dll)
          Référencé dans avutil.lib(avutil-59.dll)
          Référencé dans avutil.lib(avutil-59.dll)
          Référencé dans avutil.lib(avutil-59.dll)
          Référencé dans avutil.lib(avutil-59.dll)
          Référencé dans avutil.lib(avutil-59.dll)
          Référencé dans avutil.lib(avutil-59.dll)
          Référencé dans avutil.lib(avutil-59.dll)
          Référencé dans avutil.lib(avutil-59.dll)
          Référencé dans avutil.lib(avutil-59.dll)
          Référencé dans avutil.lib(avutil-59.dll)
          Chargé avutil.lib(avutil-59.dll)
        avutil-59_NULL_THUNK_DATA trouvé
          Référencé dans avutil.lib(avutil-59.dll)
          Chargé avutil.lib(avutil-59.dll)
      Recherche en cours F:\VccLibs\ffmpeg\bin\swresample.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avfilter.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\swscale.lib :
      Recherche en cours F:\VccLibs\onnxruntime\lib\onnxruntime.lib :
        OrtGetApiBase trouvé
          Référencé dans ProcessBodyRegions.obj
          Référencé dans RAII.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans BackgroundRemover.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Chargé onnxruntime.lib(onnxruntime.dll)
        __IMPORT_DESCRIPTOR_onnxruntime trouvé
          Référencé dans onnxruntime.lib(onnxruntime.dll)
          Chargé onnxruntime.lib(onnxruntime.dll)
        onnxruntime_NULL_THUNK_DATA trouvé
          Référencé dans onnxruntime.lib(onnxruntime.dll)
          Chargé onnxruntime.lib(onnxruntime.dll)
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\d3d11.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib :
        __imp_GetStdHandle trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_CloseHandle trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetLastError trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_WaitForSingleObject trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_TerminateProcess trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetExitCodeProcess trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_CreateProcessA trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetModuleFileNameW trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans HeadDetector.obj
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_MultiByteToWideChar trouvé
          Référencé dans Helpers.obj
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_WideCharToMultiByte trouvé
          Référencé dans Helpers.obj
          Référencé dans VideoBackgroundRemover.obj
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetModuleFileNameA trouvé
          Référencé dans KernelManager.obj
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_FreeLibrary trouvé
          Référencé dans cudart_static.lib(cudart_cudart_global.obj)
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetProcAddress trouvé
          Référencé dans cudart_static.lib(cudart_cudart_global.obj)
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_LoadLibraryExA trouvé
          Référencé dans cudart_static.lib(cudart_cudart_global.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetEnvironmentVariableA trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_SetEnvironmentVariableA trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetCurrentDirectoryA trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_CreateDirectoryA trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_DeleteFileA trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_FindClose trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_FindFirstFileA trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_FindNextFileA trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetFileAttributesExA trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_LockFileEx trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_RemoveDirectoryA trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_UnlockFileEx trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_QueryPerformanceCounter trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_QueryPerformanceFrequency trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_HeapCreate trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_HeapDestroy trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_HeapAlloc trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_HeapReAlloc trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_HeapFree trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_InitializeSRWLock trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_ReleaseSRWLockExclusive trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_ReleaseSRWLockShared trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_AcquireSRWLockExclusive trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_AcquireSRWLockShared trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_TryAcquireSRWLockExclusive trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_TryAcquireSRWLockShared trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_InitializeCriticalSection trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_EnterCriticalSection trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_LeaveCriticalSection trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_TryEnterCriticalSection trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_DeleteCriticalSection trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_InitializeConditionVariable trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_WakeConditionVariable trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_WakeAllConditionVariable trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_SleepConditionVariableCS trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_SetEvent trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_ResetEvent trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_ReleaseSemaphore trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_CreateEventA trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_Sleep trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_WaitForMultipleObjects trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetCurrentProcess trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetCurrentProcessId trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_SwitchToThread trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetCurrentThread trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetCurrentThreadId trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_TlsAlloc trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_TlsGetValue trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_TlsSetValue trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_TlsFree trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GlobalMemoryStatusEx trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetSystemInfo trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetLocalTime trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetNativeSystemInfo trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_VirtualAlloc trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_VirtualProtect trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_VirtualFree trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_VirtualQuery trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetLargePageMinimum trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_FreeLibraryAndExitThread trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetModuleHandleA trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetModuleHandleExA trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_LoadLibraryA trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetProcessAffinityMask trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_SetThreadAffinityMask trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_CreateSemaphoreA trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_CreateFileMappingA trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_CreateFileMappingNumaA trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetComputerNameA trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetNumaNodeProcessorMask trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_VerSetConditionMask trouvé
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_CreateFileW trouvé
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetFileAttributesW trouvé
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetFullPathNameW trouvé
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_SetLastError trouvé
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_CreateProcessW trouvé
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetSystemDirectoryW trouvé
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetModuleHandleW trouvé
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_LoadLibraryExW trouvé
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_LocalAlloc trouvé
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_LocalFree trouvé
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_VerifyVersionInfoW trouvé
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __IMPORT_DESCRIPTOR_KERNEL32 trouvé
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Référencé dans kernel32.lib(KERNEL32.dll)
          Chargé kernel32.lib(KERNEL32.dll)
        KERNEL32_NULL_THUNK_DATA trouvé
          Référencé dans kernel32.lib(KERNEL32.dll)
          Chargé kernel32.lib(KERNEL32.dll)
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cudart.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cudadevrt.lib :
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\msvcprtd.lib :
        "__declspec(dllimport) public: __cdecl std::_Lockit::_Lockit(int)" (__imp_??0_Lockit@std@@QEAA@H@Z) trouvé
          Référencé dans RAII.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans Helpers.obj
          Référencé dans KernelManager.obj
          Référencé dans lodepng.obj
          Référencé dans ProcessBodyRegions.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans BackgroundRemover.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans main_Kernels.cu.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: __cdecl std::_Lockit::~_Lockit(void)" (__imp_??1_Lockit@std@@QEAA@XZ) trouvé
          Référencé dans RAII.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans Helpers.obj
          Référencé dans KernelManager.obj
          Référencé dans lodepng.obj
          Référencé dans ProcessBodyRegions.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans BackgroundRemover.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans main_Kernels.cu.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "void __cdecl std::_Xlength_error(char const *)" (?_Xlength_error@std@@YAXPEBD@Z) trouvé
          Référencé dans RAII.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans Helpers.obj
          Référencé dans KernelManager.obj
          Référencé dans lodepng.obj
          Référencé dans ProcessBodyRegions.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans BackgroundRemover.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans main_Kernels.cu.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "bool __cdecl std::uncaught_exception(void)" (?uncaught_exception@std@@YA_NXZ) trouvé
          Référencé dans ProcessBodyRegions.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: bool __cdecl std::ios_base::good(void)const " (__imp_?good@ios_base@std@@QEBA_NXZ) trouvé
          Référencé dans ProcessBodyRegions.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: int __cdecl std::ios_base::flags(void)const " (__imp_?flags@ios_base@std@@QEBAHXZ) trouvé
          Référencé dans ProcessBodyRegions.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: __int64 __cdecl std::ios_base::width(void)const " (__imp_?width@ios_base@std@@QEBA_JXZ) trouvé
          Référencé dans ProcessBodyRegions.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: __int64 __cdecl std::ios_base::width(__int64)" (__imp_?width@ios_base@std@@QEAA_J_J@Z) trouvé
          Référencé dans ProcessBodyRegions.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: int __cdecl std::basic_streambuf<char,struct std::char_traits<char> >::sputc(char)" (__imp_?sputc@?$basic_streambuf@DU?$char_traits@D@std@@@std@@QEAAHD@Z) trouvé
          Référencé dans ProcessBodyRegions.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: __int64 __cdecl std::basic_streambuf<char,struct std::char_traits<char> >::sputn(char const *,__int64)" (__imp_?sputn@?$basic_streambuf@DU?$char_traits@D@std@@@std@@QEAA_JPEBD_J@Z) trouvé
          Référencé dans ProcessBodyRegions.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: void __cdecl std::basic_ios<char,struct std::char_traits<char> >::setstate(int,bool)" (__imp_?setstate@?$basic_ios@DU?$char_traits@D@std@@@std@@QEAAXH_N@Z) trouvé
          Référencé dans ProcessBodyRegions.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: class std::basic_ostream<char,struct std::char_traits<char> > * __cdecl std::basic_ios<char,struct std::char_traits<char> >::tie(void)const " (__imp_?tie@?$basic_ios@DU?$char_traits@D@std@@@std@@QEBAPEAV?$basic_ostream@DU?$char_traits@D@std@@@2@XZ) trouvé
          Référencé dans ProcessBodyRegions.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: class std::basic_streambuf<char,struct std::char_traits<char> > * __cdecl std::basic_ios<char,struct std::char_traits<char> >::rdbuf(void)const " (__imp_?rdbuf@?$basic_ios@DU?$char_traits@D@std@@@std@@QEBAPEAV?$basic_streambuf@DU?$char_traits@D@std@@@2@XZ) trouvé
          Référencé dans ProcessBodyRegions.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: char __cdecl std::basic_ios<char,struct std::char_traits<char> >::fill(void)const " (__imp_?fill@?$basic_ios@DU?$char_traits@D@std@@@std@@QEBADXZ) trouvé
          Référencé dans ProcessBodyRegions.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: char __cdecl std::basic_ios<char,struct std::char_traits<char> >::widen(char)const " (__imp_?widen@?$basic_ios@DU?$char_traits@D@std@@@std@@QEBADD@Z) trouvé
          Référencé dans ProcessBodyRegions.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: void __cdecl std::basic_ostream<char,struct std::char_traits<char> >::_Osfx(void)" (__imp_?_Osfx@?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAAXXZ) trouvé
          Référencé dans ProcessBodyRegions.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: class std::basic_ostream<char,struct std::char_traits<char> > & __cdecl std::basic_ostream<char,struct std::char_traits<char> >::operator<<(class std::basic_ostream<char,struct std::char_traits<char> > & (__cdecl*)(class std::basic_ostream<char,struct std::char_traits<char> > &))" (__imp_??6?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAAAEAV01@P6AAEAV01@AEAV01@@Z@Z) trouvé
          Référencé dans ProcessBodyRegions.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: class std::basic_ostream<char,struct std::char_traits<char> > & __cdecl std::basic_ostream<char,struct std::char_traits<char> >::put(char)" (__imp_?put@?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAAAEAV12@D@Z) trouvé
          Référencé dans ProcessBodyRegions.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: class std::basic_ostream<char,struct std::char_traits<char> > & __cdecl std::basic_ostream<char,struct std::char_traits<char> >::flush(void)" (__imp_?flush@?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAAAEAV12@XZ) trouvé
          Référencé dans ProcessBodyRegions.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) class std::basic_ostream<char,struct std::char_traits<char> > std::cerr" (__imp_?cerr@std@@3V?$basic_ostream@DU?$char_traits@D@std@@@1@A) trouvé
          Référencé dans ProcessBodyRegions.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "void __cdecl std::_Xbad_alloc(void)" (?_Xbad_alloc@std@@YAXXZ) trouvé
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans Helpers.obj
          Référencé dans KernelManager.obj
          Référencé dans ProcessBodyRegions.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: struct _Cvtvec __cdecl std::_Locinfo::_Getcvt(void)const " (__imp_?_Getcvt@_Locinfo@std@@QEBA?AU_Cvtvec@@XZ) trouvé
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans Helpers.obj
          Référencé dans KernelManager.obj
          Référencé dans ProcessBodyRegions.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: unsigned short const * __cdecl std::_Locinfo::_W_Getdays(void)const " (__imp_?_W_Getdays@_Locinfo@std@@QEBAPEBGXZ) trouvé
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans Helpers.obj
          Référencé dans KernelManager.obj
          Référencé dans ProcessBodyRegions.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: unsigned short const * __cdecl std::_Locinfo::_W_Getmonths(void)const " (__imp_?_W_Getmonths@_Locinfo@std@@QEBAPEBGXZ) trouvé
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans Helpers.obj
          Référencé dans KernelManager.obj
          Référencé dans ProcessBodyRegions.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: class std::basic_ostream<char,struct std::char_traits<char> > & __cdecl std::basic_ostream<char,struct std::char_traits<char> >::operator<<(unsigned __int64)" (__imp_??6?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAAAEAV01@_K@Z) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMattingOnnx.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: class std::basic_ostream<char,struct std::char_traits<char> > & __cdecl std::basic_ostream<char,struct std::char_traits<char> >::operator<<(int)" (__imp_??6?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAAAEAV01@H@Z) trouvé
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans ProcessBodyRegions.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: class std::basic_ostream<char,struct std::char_traits<char> > & __cdecl std::basic_ostream<char,struct std::char_traits<char> >::operator<<(__int64)" (__imp_??6?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAAAEAV01@_J@Z) trouvé
          Référencé dans DirectVideoWriter.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMattingTensorRt.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: class std::basic_ostream<char,struct std::char_traits<char> > & __cdecl std::basic_ostream<char,struct std::char_traits<char> >::operator<<(double)" (__imp_??6?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAAAEAV01@N@Z) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMattingOnnx.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) class std::basic_ostream<char,struct std::char_traits<char> > std::cout" (__imp_?cout@std@@3V?$basic_ostream@DU?$char_traits@D@std@@@1@A) trouvé
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans KernelManager.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMattingOnnx.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        __std_find_trivial_2 trouvé
          Référencé dans MultiVideoReader.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans VideoBackgroundRemover.obj
          Chargé msvcprtd.lib(vector_algorithms.obj)
        "void __cdecl std::_Xout_of_range(char const *)" (?_Xout_of_range@std@@YAXPEBD@Z) trouvé
          Référencé dans KernelManager.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        __std_system_error_allocate_message trouvé
          Référencé dans MultiVideoReader.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans Helpers.obj
          Référencé dans VideoBackgroundRemover.obj
          Chargé msvcprtd.lib(syserror_import_lib.obj)
        "char const * __cdecl std::_Syserror_map(int)" (?_Syserror_map@std@@YAPEBDH@Z) trouvé
          Référencé dans MultiVideoReader.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans Helpers.obj
          Référencé dans VideoBackgroundRemover.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "int __cdecl std::_Winerror_map(int)" (?_Winerror_map@std@@YAHH@Z) trouvé
          Référencé dans MultiVideoReader.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans Helpers.obj
          Référencé dans VideoBackgroundRemover.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        __std_fs_code_page trouvé
          Référencé dans MultiVideoReader.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans Helpers.obj
          Référencé dans VideoBackgroundRemover.obj
          Chargé msvcprtd.lib(filesystem.obj)
        "void __cdecl std::_Xinvalid_argument(char const *)" (?_Xinvalid_argument@std@@YAXPEBD@Z) trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        _Query_perf_counter trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        _Query_perf_frequency trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        _Mtx_lock trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        _Mtx_unlock trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "void __cdecl std::_Throw_Cpp_error(int)" (?_Throw_Cpp_error@std@@YAXH@Z) trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "void __cdecl std::_Facet_Register(class std::_Facet_base *)" (?_Facet_Register@std@@YAXPEAV_Facet_base@1@@Z) trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(locale0_implib.obj)
        _Strcoll trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        _Strxfrm trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: __cdecl std::_Locinfo::_Locinfo(char const *)" (__imp_??0_Locinfo@std@@QEAA@PEBD@Z) trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: __cdecl std::_Locinfo::~_Locinfo(void)" (__imp_??1_Locinfo@std@@QEAA@XZ) trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: struct _Collvec __cdecl std::_Locinfo::_Getcoll(void)const " (__imp_?_Getcoll@_Locinfo@std@@QEBA?AU_Collvec@@XZ) trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: char const * __cdecl std::_Yarn<char>::c_str(void)const " (__imp_?c_str@?$_Yarn@D@std@@QEBAPEBDXZ) trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: static void * __cdecl std::_Crt_new_delete::operator new(unsigned __int64)" (__imp_??2_Crt_new_delete@std@@SAPEAX_K@Z) trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: static void __cdecl std::_Crt_new_delete::operator delete(void *)" (__imp_??3_Crt_new_delete@std@@SAXPEAX@Z) trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) protected: __cdecl std::locale::facet::facet(unsigned __int64)" (__imp_??0facet@locale@std@@IEAA@_K@Z) trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) protected: virtual __cdecl std::locale::facet::~facet(void)" (__imp_??1facet@locale@std@@MEAA@XZ) trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "private: static class std::locale::_Locimp * __cdecl std::locale::_Init(bool)" (?_Init@locale@std@@CAPEAV_Locimp@12@_N@Z) trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "private: static class std::locale::_Locimp * __cdecl std::locale::_Getgloballocale(void)" (?_Getgloballocale@locale@std@@CAPEAV_Locimp@12@XZ) trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: bool __cdecl std::ctype<char>::is(short,char)const " (__imp_?is@?$ctype@D@std@@QEBA_NFD@Z) trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: char __cdecl std::ctype<char>::tolower(char)const " (__imp_?tolower@?$ctype@D@std@@QEBADD@Z) trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: char const * __cdecl std::ctype<char>::tolower(char *,char const *)const " (__imp_?tolower@?$ctype@D@std@@QEBAPEBDPEADPEBD@Z) trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: static unsigned __int64 __cdecl std::ctype<char>::_Getcat(class std::locale::facet const * *,class std::locale const *)" (__imp_?_Getcat@?$ctype@D@std@@SA_KPEAPEBVfacet@locale@2@PEBV42@@Z) trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: int __cdecl std::ios_base::setf(int,int)" (__imp_?setf@ios_base@std@@QEAAHHH@Z) trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: unsigned short __cdecl std::basic_streambuf<wchar_t,struct std::char_traits<wchar_t> >::sputc(wchar_t)" (__imp_?sputc@?$basic_streambuf@_WU?$char_traits@_W@std@@@std@@QEAAG_W@Z) trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans VideoBackgroundRemover.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: __int64 __cdecl std::basic_streambuf<wchar_t,struct std::char_traits<wchar_t> >::sputn(wchar_t const *,__int64)" (__imp_?sputn@?$basic_streambuf@_WU?$char_traits@_W@std@@@std@@QEAA_JPEB_W_J@Z) trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans VideoBackgroundRemover.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "void __cdecl std::_Xregex_error(enum std::regex_constants::error_type)" (?_Xregex_error@std@@YAXW4error_type@regex_constants@1@@Z) trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: void __cdecl std::basic_ios<wchar_t,struct std::char_traits<wchar_t> >::setstate(int,bool)" (__imp_?setstate@?$basic_ios@_WU?$char_traits@_W@std@@@std@@QEAAXH_N@Z) trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans VideoBackgroundRemover.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: class std::basic_ostream<wchar_t,struct std::char_traits<wchar_t> > * __cdecl std::basic_ios<wchar_t,struct std::char_traits<wchar_t> >::tie(void)const " (__imp_?tie@?$basic_ios@_WU?$char_traits@_W@std@@@std@@QEBAPEAV?$basic_ostream@_WU?$char_traits@_W@std@@@2@XZ) trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans VideoBackgroundRemover.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: class std::basic_streambuf<wchar_t,struct std::char_traits<wchar_t> > * __cdecl std::basic_ios<wchar_t,struct std::char_traits<wchar_t> >::rdbuf(void)const " (__imp_?rdbuf@?$basic_ios@_WU?$char_traits@_W@std@@@std@@QEBAPEAV?$basic_streambuf@_WU?$char_traits@_W@std@@@2@XZ) trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans VideoBackgroundRemover.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: wchar_t __cdecl std::basic_ios<wchar_t,struct std::char_traits<wchar_t> >::fill(void)const " (__imp_?fill@?$basic_ios@_WU?$char_traits@_W@std@@@std@@QEBA_WXZ) trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans VideoBackgroundRemover.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: wchar_t __cdecl std::basic_ios<wchar_t,struct std::char_traits<wchar_t> >::widen(char)const " (__imp_?widen@?$basic_ios@_WU?$char_traits@_W@std@@@std@@QEBA_WD@Z) trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans VideoBackgroundRemover.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: class std::basic_ostream<char,struct std::char_traits<char> > & __cdecl std::basic_ostream<char,struct std::char_traits<char> >::operator<<(class std::ios_base & (__cdecl*)(class std::ios_base &))" (__imp_??6?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAAAEAV01@P6AAEAVios_base@1@AEAV21@@Z@Z) trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: class std::basic_ostream<char,struct std::char_traits<char> > & __cdecl std::basic_ostream<char,struct std::char_traits<char> >::operator<<(unsigned long)" (__imp_??6?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAAAEAV01@K@Z) trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: void __cdecl std::basic_ostream<wchar_t,struct std::char_traits<wchar_t> >::_Osfx(void)" (__imp_?_Osfx@?$basic_ostream@_WU?$char_traits@_W@std@@@std@@QEAAXXZ) trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans VideoBackgroundRemover.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: class std::basic_ostream<wchar_t,struct std::char_traits<wchar_t> > & __cdecl std::basic_ostream<wchar_t,struct std::char_traits<wchar_t> >::operator<<(class std::basic_ostream<wchar_t,struct std::char_traits<wchar_t> > & (__cdecl*)(class std::basic_ostream<wchar_t,struct std::char_traits<wchar_t> > &))" (__imp_??6?$basic_ostream@_WU?$char_traits@_W@std@@@std@@QEAAAEAV01@P6AAEAV01@AEAV01@@Z@Z) trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans VideoBackgroundRemover.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: class std::basic_ostream<wchar_t,struct std::char_traits<wchar_t> > & __cdecl std::basic_ostream<wchar_t,struct std::char_traits<wchar_t> >::put(wchar_t)" (__imp_?put@?$basic_ostream@_WU?$char_traits@_W@std@@@std@@QEAAAEAV12@_W@Z) trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans VideoBackgroundRemover.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: class std::basic_ostream<wchar_t,struct std::char_traits<wchar_t> > & __cdecl std::basic_ostream<wchar_t,struct std::char_traits<wchar_t> >::flush(void)" (__imp_?flush@?$basic_ostream@_WU?$char_traits@_W@std@@@std@@QEAAAEAV12@XZ) trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans VideoBackgroundRemover.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "struct std::_Smanip<__int64> __cdecl std::setprecision(__int64)" (?setprecision@std@@YA?AU?$_Smanip@_J@1@_J@Z) trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "public: virtual void __cdecl std::locale::facet::_Incref(void)" (?_Incref@facet@locale@std@@UEAAXXZ) trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "public: virtual class std::_Facet_base * __cdecl std::locale::facet::_Decref(void)" (?_Decref@facet@locale@std@@UEAAPEAV_Facet_base@3@XZ) trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) private: static int std::locale::id::_Id_cnt" (__imp_?_Id_cnt@id@locale@std@@0HA) trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: static class std::locale::id std::ctype<char>::id" (__imp_?id@?$ctype@D@std@@2V0locale@2@A) trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: static class std::locale::id std::collate<char>::id" (__imp_?id@?$collate@D@std@@2V0locale@2@A) trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) class std::basic_ostream<wchar_t,struct std::char_traits<wchar_t> > std::wcout" (__imp_?wcout@std@@3V?$basic_ostream@_WU?$char_traits@_W@std@@@1@A) trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) class std::basic_ostream<wchar_t,struct std::char_traits<wchar_t> > std::wcerr" (__imp_?wcerr@std@@3V?$basic_ostream@_WU?$char_traits@_W@std@@@1@A) trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans VideoBackgroundRemover.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: class std::basic_ostream<char,struct std::char_traits<char> > & __cdecl std::basic_ostream<char,struct std::char_traits<char> >::operator<<(void const *)" (__imp_??6?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAAAEAV01@PEBX@Z) trouvé
          Référencé dans ImageMattingOnnx.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: bool __cdecl std::codecvt_base::always_noconv(void)const " (__imp_?always_noconv@codecvt_base@std@@QEBA_NXZ) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: int __cdecl std::codecvt<char,char,struct _Mbstatet>::in(struct _Mbstatet &,char const *,char const *,char const * &,char *,char *,char * &)const " (__imp_?in@?$codecvt@DDU_Mbstatet@@@std@@QEBAHAEAU_Mbstatet@@PEBD1AEAPEBDPEAD3AEAPEAD@Z) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: int __cdecl std::codecvt<char,char,struct _Mbstatet>::out(struct _Mbstatet &,char const *,char const *,char const * &,char *,char *,char * &)const " (__imp_?out@?$codecvt@DDU_Mbstatet@@@std@@QEBAHAEAU_Mbstatet@@PEBD1AEAPEBDPEAD3AEAPEAD@Z) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: int __cdecl std::codecvt<char,char,struct _Mbstatet>::unshift(struct _Mbstatet &,char *,char *,char * &)const " (__imp_?unshift@?$codecvt@DDU_Mbstatet@@@std@@QEBAHAEAU_Mbstatet@@PEAD1AEAPEAD@Z) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: static unsigned __int64 __cdecl std::codecvt<char,char,struct _Mbstatet>::_Getcat(class std::locale::facet const * *,class std::locale const *)" (__imp_?_Getcat@?$codecvt@DDU_Mbstatet@@@std@@SA_KPEAPEBVfacet@locale@2@PEBV42@@Z) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: bool __cdecl std::ios_base::operator!(void)const " (__imp_??7ios_base@std@@QEBA_NXZ) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) protected: __cdecl std::basic_streambuf<char,struct std::char_traits<char> >::basic_streambuf<char,struct std::char_traits<char> >(void)" (__imp_??0?$basic_streambuf@DU?$char_traits@D@std@@@std@@IEAA@XZ) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: virtual __cdecl std::basic_streambuf<char,struct std::char_traits<char> >::~basic_streambuf<char,struct std::char_traits<char> >(void)" (__imp_??1?$basic_streambuf@DU?$char_traits@D@std@@@std@@UEAA@XZ) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: class std::locale __cdecl std::basic_streambuf<char,struct std::char_traits<char> >::getloc(void)const " (__imp_?getloc@?$basic_streambuf@DU?$char_traits@D@std@@@std@@QEBA?AVlocale@2@XZ) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) protected: char * __cdecl std::basic_streambuf<char,struct std::char_traits<char> >::eback(void)const " (__imp_?eback@?$basic_streambuf@DU?$char_traits@D@std@@@std@@IEBAPEADXZ) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) protected: char * __cdecl std::basic_streambuf<char,struct std::char_traits<char> >::gptr(void)const " (__imp_?gptr@?$basic_streambuf@DU?$char_traits@D@std@@@std@@IEBAPEADXZ) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) protected: char * __cdecl std::basic_streambuf<char,struct std::char_traits<char> >::pptr(void)const " (__imp_?pptr@?$basic_streambuf@DU?$char_traits@D@std@@@std@@IEBAPEADXZ) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) protected: char * __cdecl std::basic_streambuf<char,struct std::char_traits<char> >::egptr(void)const " (__imp_?egptr@?$basic_streambuf@DU?$char_traits@D@std@@@std@@IEBAPEADXZ) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) protected: void __cdecl std::basic_streambuf<char,struct std::char_traits<char> >::gbump(int)" (__imp_?gbump@?$basic_streambuf@DU?$char_traits@D@std@@@std@@IEAAXH@Z) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) protected: void __cdecl std::basic_streambuf<char,struct std::char_traits<char> >::setg(char *,char *,char *)" (__imp_?setg@?$basic_streambuf@DU?$char_traits@D@std@@@std@@IEAAXPEAD00@Z) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) protected: char * __cdecl std::basic_streambuf<char,struct std::char_traits<char> >::epptr(void)const " (__imp_?epptr@?$basic_streambuf@DU?$char_traits@D@std@@@std@@IEBAPEADXZ) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) protected: char * __cdecl std::basic_streambuf<char,struct std::char_traits<char> >::_Gndec(void)" (__imp_?_Gndec@?$basic_streambuf@DU?$char_traits@D@std@@@std@@IEAAPEADXZ) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) protected: char * __cdecl std::basic_streambuf<char,struct std::char_traits<char> >::_Gninc(void)" (__imp_?_Gninc@?$basic_streambuf@DU?$char_traits@D@std@@@std@@IEAAPEADXZ) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) protected: __int64 __cdecl std::basic_streambuf<char,struct std::char_traits<char> >::_Gnavail(void)const " (__imp_?_Gnavail@?$basic_streambuf@DU?$char_traits@D@std@@@std@@IEBA_JXZ) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) protected: void __cdecl std::basic_streambuf<char,struct std::char_traits<char> >::pbump(int)" (__imp_?pbump@?$basic_streambuf@DU?$char_traits@D@std@@@std@@IEAAXH@Z) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) protected: char * __cdecl std::basic_streambuf<char,struct std::char_traits<char> >::_Pninc(void)" (__imp_?_Pninc@?$basic_streambuf@DU?$char_traits@D@std@@@std@@IEAAPEADXZ) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) protected: __int64 __cdecl std::basic_streambuf<char,struct std::char_traits<char> >::_Pnavail(void)const " (__imp_?_Pnavail@?$basic_streambuf@DU?$char_traits@D@std@@@std@@IEBA_JXZ) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) protected: void __cdecl std::basic_streambuf<char,struct std::char_traits<char> >::_Init(void)" (__imp_?_Init@?$basic_streambuf@DU?$char_traits@D@std@@@std@@IEAAXXZ) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) protected: void __cdecl std::basic_streambuf<char,struct std::char_traits<char> >::_Init(char * *,char * *,int *,char * *,char * *,int *)" (__imp_?_Init@?$basic_streambuf@DU?$char_traits@D@std@@@std@@IEAAXPEAPEAD0PEAH001@Z) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) protected: virtual __int64 __cdecl std::basic_streambuf<char,struct std::char_traits<char> >::xsgetn(char *,__int64)" (__imp_?xsgetn@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MEAA_JPEAD_J@Z) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) protected: virtual __int64 __cdecl std::basic_streambuf<char,struct std::char_traits<char> >::xsputn(char const *,__int64)" (__imp_?xsputn@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MEAA_JPEBD_J@Z) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: virtual __cdecl std::basic_ios<char,struct std::char_traits<char> >::~basic_ios<char,struct std::char_traits<char> >(void)" (__imp_??1?$basic_ios@DU?$char_traits@D@std@@@std@@UEAA@XZ) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) protected: __cdecl std::basic_ios<char,struct std::char_traits<char> >::basic_ios<char,struct std::char_traits<char> >(void)" (__imp_??0?$basic_ios@DU?$char_traits@D@std@@@std@@IEAA@XZ) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: class std::basic_ostream<char,struct std::char_traits<char> > & __cdecl std::basic_ostream<char,struct std::char_traits<char> >::operator<<(long)" (__imp_??6?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAAAEAV01@J@Z) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans ProcessBodyRegions.obj
          Référencé dans VideoBackgroundRemover.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: __cdecl std::basic_istream<char,struct std::char_traits<char> >::basic_istream<char,struct std::char_traits<char> >(class std::basic_streambuf<char,struct std::char_traits<char> > *,bool)" (__imp_??0?$basic_istream@DU?$char_traits@D@std@@@std@@QEAA@PEAV?$basic_streambuf@DU?$char_traits@D@std@@@1@_N@Z) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: virtual __cdecl std::basic_istream<char,struct std::char_traits<char> >::~basic_istream<char,struct std::char_traits<char> >(void)" (__imp_??1?$basic_istream@DU?$char_traits@D@std@@@std@@UEAA@XZ) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: class std::basic_istream<char,struct std::char_traits<char> > & __cdecl std::basic_istream<char,struct std::char_traits<char> >::read(char *,__int64)" (__imp_?read@?$basic_istream@DU?$char_traits@D@std@@@std@@QEAAAEAV12@PEAD_J@Z) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: class std::basic_istream<char,struct std::char_traits<char> > & __cdecl std::basic_istream<char,struct std::char_traits<char> >::seekg(__int64,int)" (__imp_?seekg@?$basic_istream@DU?$char_traits@D@std@@@std@@QEAAAEAV12@_JH@Z) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: class std::fpos<struct _Mbstatet> __cdecl std::basic_istream<char,struct std::char_traits<char> >::tellg(void)" (__imp_?tellg@?$basic_istream@DU?$char_traits@D@std@@@std@@QEAA?AV?$fpos@U_Mbstatet@@@2@XZ) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "struct _iobuf * __cdecl std::_Fiopen(char const *,int,int)" (?_Fiopen@std@@YAPEAU_iobuf@@PEBDHH@Z) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "protected: virtual __int64 __cdecl std::basic_streambuf<char,struct std::char_traits<char> >::showmanyc(void)" (?showmanyc@?$basic_streambuf@DU?$char_traits@D@std@@@std@@MEAA_JXZ) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: static class std::locale::id std::codecvt<char,char,struct _Mbstatet>::id" (__imp_?id@?$codecvt@DDU_Mbstatet@@@std@@2V0locale@2@A) trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        "__declspec(dllimport) public: class std::basic_ostream<char,struct std::char_traits<char> > & __cdecl std::basic_ostream<char,struct std::char_traits<char> >::operator<<(unsigned int)" (__imp_??6?$basic_ostream@DU?$char_traits@D@std@@@std@@QEAAAEAV01@I@Z) trouvé
          Référencé dans KernelManager.obj
          Chargé msvcprtd.lib(MSVCP140D.dll)
        __IMPORT_DESCRIPTOR_MSVCP140D trouvé
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Chargé msvcprtd.lib(MSVCP140D.dll)
        MSVCP140D_NULL_THUNK_DATA trouvé
          Référencé dans msvcprtd.lib(MSVCP140D.dll)
          Chargé msvcprtd.lib(MSVCP140D.dll)
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\MSVCRTD.lib :
        "void * __cdecl operator new(unsigned __int64)" (??2@YAPEAX_K@Z) trouvé
          Référencé dans RAII.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans Helpers.obj
          Référencé dans KernelManager.obj
          Référencé dans lodepng.obj
          Référencé dans ProcessBodyRegions.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans BackgroundRemover.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans main_Kernels.cu.obj
          Chargé MSVCRTD.lib(new_scalar.obj)
        "void __cdecl operator delete(void *,unsigned __int64)" (??3@YAXPEAX_K@Z) trouvé
          Référencé dans RAII.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans Helpers.obj
          Référencé dans KernelManager.obj
          Référencé dans lodepng.obj
          Référencé dans ProcessBodyRegions.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans BackgroundRemover.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans main_Kernels.cu.obj
          Chargé MSVCRTD.lib(delete_scalar_size.obj)
        atexit trouvé
          Référencé dans msvcprtd.lib(locale0_implib.obj)
          Référencé dans ImageMattingFactory.obj
          Référencé dans KernelManager.obj
          Référencé dans cudart_static.lib(cudart_cudart_global.obj)
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé MSVCRTD.lib(utility.obj)
        _RTC_CheckStackVars trouvé
          Référencé dans RAII.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans lodepng.obj
          Référencé dans ProcessBodyRegions.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans BackgroundRemover.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé MSVCRTD.lib(stack.obj)
        _RTC_InitBase trouvé
          Référencé dans ProcessBodyRegions.obj
          Référencé dans RAII.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans Helpers.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans lodepng.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans BackgroundRemover.obj
          Référencé dans dllmain.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé MSVCRTD.lib(init.obj)
        __GSHandlerCheck trouvé
          Référencé dans msvcprtd.lib(vector_algorithms.obj)
          Référencé dans msvcprtd.lib(filesystem.obj)
          Référencé dans cudart_static.lib(cudart_cudart_context_mgr.obj)
          Référencé dans cudart_static.lib(cudart_cudart_array.obj)
          Référencé dans cudart_static.lib(cudart_cudart_helper.obj)
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Référencé dans cudart_static.lib(cudart_cuda_runtime_api.obj)
          Référencé dans cudart_static.lib(cudart_cudart_global.obj)
          Référencé dans cudart_static.lib(cudart_cudart_context.obj)
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Référencé dans lodepng.obj
          Référencé dans ProcessBodyRegions.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans cudart_static.lib(cudart_generated_cuda_runtime_api.obj)
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé MSVCRTD.lib(gshandler.obj)
        __GSHandlerCheck_EH4 trouvé
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans msvcprtd.lib(vector_algorithms.obj)
          Référencé dans msvcprtd.lib(filesystem.obj)
          Référencé dans Helpers.obj
          Référencé dans KernelManager.obj
          Référencé dans lodepng.obj
          Référencé dans RAII.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans BackgroundRemover.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans main_Kernels.cu.obj
          Chargé MSVCRTD.lib(gshandlereh4.obj)
        __security_check_cookie trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Référencé dans MSVCRTD.lib(gshandler.obj)
          Référencé dans cudart_static.lib(cudart_cudart_array.obj)
          Référencé dans cudart_static.lib(cudart_cudart_helper.obj)
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Référencé dans msvcprtd.lib(vector_algorithms.obj)
          Référencé dans cudart_static.lib(cudart_cudart_global.obj)
          Référencé dans cudart_static.lib(cudart_cudart_context.obj)
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Référencé dans cudart_static.lib(cudart_cudart_context_mgr.obj)
          Référencé dans RAII.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans cudart_static.lib(cudart_generated_cuda_runtime_api.obj)
          Référencé dans cudart_static.lib(cudart_cuda_runtime_api.obj)
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans lodepng.obj
          Référencé dans ProcessBodyRegions.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans BackgroundRemover.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé MSVCRTD.lib(amdsecgs.obj)
        "const type_info::`vftable'" (??_7type_info@@6B@) trouvé
          Référencé dans RAII.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans Helpers.obj
          Référencé dans KernelManager.obj
          Référencé dans lodepng.obj
          Référencé dans ProcessBodyRegions.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans BackgroundRemover.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans main_Kernels.cu.obj
          Chargé MSVCRTD.lib(std_type_info_static.obj)
        __security_cookie trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Référencé dans MSVCRTD.lib(amdsecgs.obj)
          Référencé dans cudart_static.lib(cudart_cudart_array.obj)
          Référencé dans cudart_static.lib(cudart_cudart_helper.obj)
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Référencé dans msvcprtd.lib(vector_algorithms.obj)
          Référencé dans cudart_static.lib(cudart_cudart_global.obj)
          Référencé dans cudart_static.lib(cudart_cudart_context.obj)
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Référencé dans cudart_static.lib(cudart_cudart_context_mgr.obj)
          Référencé dans RAII.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans cudart_static.lib(cudart_generated_cuda_runtime_api.obj)
          Référencé dans cudart_static.lib(cudart_cuda_runtime_api.obj)
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans lodepng.obj
          Référencé dans ProcessBodyRegions.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans BackgroundRemover.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé MSVCRTD.lib(gs_cookie.obj)
        _fltused trouvé
          Référencé dans cudart_static.lib(cudart_cudart_helper.obj)
          Référencé dans msvcprtd.lib(vector_algorithms.obj)
          Référencé dans KernelManager.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans cudart_static.lib(cudart_cuda_runtime_api.obj)
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé MSVCRTD.lib(fltused.obj)
        __CheckForDebuggerJustMyCode trouvé
          Référencé dans ProcessBodyRegions.obj
          Référencé dans RAII.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans Helpers.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans lodepng.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans BackgroundRemover.obj
          Référencé dans dllmain.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Chargé MSVCRTD.lib(debugger_jmc.obj)
        "void __cdecl `eh vector destructor iterator'(void *,unsigned __int64,unsigned __int64,void (__cdecl*)(void *))" (??_M@YAXPEAX_K1P6AX0@Z@Z) trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans KernelManager.obj
          Référencé dans VideoBackgroundRemover.obj
          Chargé MSVCRTD.lib(ehvecdtr.obj)
        __chkstk trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans lodepng.obj
          Référencé dans VideoBackgroundRemover.obj
          Chargé MSVCRTD.lib(chkstk.obj)
        "void * __cdecl operator new[](unsigned __int64)" (??_U@YAPEAX_K@Z) trouvé
          Référencé dans Helpers.obj
          Référencé dans ProcessBodyRegions.obj
          Chargé MSVCRTD.lib(new_array.obj)
        "void __cdecl operator delete[](void *)" (??_V@YAXPEAX@Z) trouvé
          Référencé dans Helpers.obj
          Référencé dans ProcessBodyRegions.obj
          Chargé MSVCRTD.lib(delete_array.obj)
        _Init_thread_header trouvé
          Référencé dans KernelManager.obj
          Chargé MSVCRTD.lib(thread_safe_statics.obj)
        _tls_index trouvé
          Référencé dans KernelManager.obj
          Référencé dans cudart_static.lib(cudart_cudart_tls.obj)
          Référencé dans MSVCRTD.lib(thread_safe_statics.obj)
          Chargé MSVCRTD.lib(tlssup.obj)
        _DllMainCRTStartup trouvé
          Chargé MSVCRTD.lib(dll_dllmain.obj)
        __isa_enabled trouvé
          Référencé dans msvcprtd.lib(vector_algorithms.obj)
          Chargé MSVCRTD.lib(cpu_disp.obj)
        __guard_dispatch_icall_fptr trouvé
          Référencé dans MSVCRTD.lib(dll_dllmain.obj)
          Référencé dans msvcprtd.lib(filesystem.obj)
          Référencé dans msvcprtd.lib(locale0_implib.obj)
          Référencé dans MSVCRTD.lib(utility.obj)
          Référencé dans MSVCRTD.lib(ehvecdtr.obj)
          Chargé MSVCRTD.lib(guard_support.obj)
        "void __cdecl __scrt_throw_std_bad_alloc(void)" (?__scrt_throw_std_bad_alloc@@YAXXZ) trouvé
          Référencé dans MSVCRTD.lib(new_scalar.obj)
          Chargé MSVCRTD.lib(throw_bad_alloc.obj)
        "void __cdecl operator delete(void *)" (??3@YAXPEAX@Z) trouvé
          Référencé dans MSVCRTD.lib(delete_scalar_size.obj)
          Référencé dans MSVCRTD.lib(delete_array.obj)
          Chargé MSVCRTD.lib(delete_scalar.obj)
        _get_startup_argv_mode trouvé
          Référencé dans MSVCRTD.lib(utility.obj)
          Chargé MSVCRTD.lib(argv_mode.obj)
        __scrt_is_ucrt_dll_in_use trouvé
          Référencé dans MSVCRTD.lib(utility.obj)
          Chargé MSVCRTD.lib(ucrt_detection.obj)
        __scrt_fastfail trouvé
          Référencé dans MSVCRTD.lib(utility.obj)
          Référencé dans MSVCRTD.lib(dll_dllmain.obj)
          Chargé MSVCRTD.lib(utility_desktop.obj)
        "void __cdecl _RTC_StackFailure(void *,char const *)" (?_RTC_StackFailure@@YAXPEAXPEBD@Z) trouvé
          Référencé dans MSVCRTD.lib(stack.obj)
          Chargé MSVCRTD.lib(error.obj)
        _RTC_SetErrorFuncW trouvé
          Référencé dans MSVCRTD.lib(init.obj)
          Chargé MSVCRTD.lib(userapi.obj)
        __report_gsfailure trouvé
          Référencé dans MSVCRTD.lib(amdsecgs.obj)
          Chargé MSVCRTD.lib(gs_report.obj)
        __security_init_cookie trouvé
          Référencé dans MSVCRTD.lib(dll_dllmain.obj)
          Chargé MSVCRTD.lib(gs_support.obj)
        "void __cdecl __scrt_initialize_type_info(void)" (?__scrt_initialize_type_info@@YAXXZ) trouvé
          Référencé dans MSVCRTD.lib(dll_dllmain.obj)
          Chargé MSVCRTD.lib(tncleanup.obj)
        __scrt_initialize_default_local_stdio_options trouvé
          Référencé dans MSVCRTD.lib(dll_dllmain.obj)
          Chargé MSVCRTD.lib(default_local_stdio_options.obj)
        __scrt_get_dyn_tls_init_callback trouvé
          Référencé dans MSVCRTD.lib(dll_dllmain.obj)
          Chargé MSVCRTD.lib(dyn_tls_init.obj)
        _RTC_Initialize trouvé
          Référencé dans MSVCRTD.lib(dll_dllmain.obj)
          Chargé MSVCRTD.lib(initsect.obj)
        __xi_a trouvé
          Référencé dans MSVCRTD.lib(dll_dllmain.obj)
          Chargé MSVCRTD.lib(initializers.obj)
  /DEFAULTLIB:kernel32.lib traité
   /DISALLOWLIB:msvcrt.lib traité
   /DISALLOWLIB:libcmtd.lib traité
   /DISALLOWLIB:vcruntime.lib traité
  /DEFAULTLIB:vcruntimed.lib traité
   /DISALLOWLIB:libvcruntime.lib traité
   /DISALLOWLIB:libvcruntimed.lib traité
   /DISALLOWLIB:ucrt.lib traité
  /DEFAULTLIB:ucrtd.lib traité
   /DISALLOWLIB:libucrt.lib traité
   /DISALLOWLIB:libucrtd.lib traité
        _guard_dispatch_icall_nop trouvé
          Référencé dans MSVCRTD.lib(guard_support.obj)
          Chargé MSVCRTD.lib(guard_dispatch.obj)
        _guard_xfg_dispatch_icall_nop trouvé
          Référencé dans MSVCRTD.lib(guard_support.obj)
          Chargé MSVCRTD.lib(guard_xfg_dispatch.obj)
        "int __cdecl _RTC_GetSrcLine(unsigned char *,wchar_t *,unsigned long,int *,wchar_t *,unsigned long)" (?_RTC_GetSrcLine@@YAHPEAEPEA_WKPEAH1K@Z) trouvé
          Référencé dans MSVCRTD.lib(error.obj)
          Chargé MSVCRTD.lib(pdblkup.obj)
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib :
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\vcruntimed.lib :
        memcpy trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Référencé dans RAII.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Référencé dans msvcprtd.lib(vector_algorithms.obj)
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans KernelManager.obj
          Référencé dans ProcessBodyRegions.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans BackgroundRemover.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans main_Kernels.cu.obj
          Chargé vcruntimed.lib(VCRUNTIME140D.dll)
        __std_exception_copy trouvé
          Référencé dans RAII.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans MSVCRTD.lib(throw_bad_alloc.obj)
          Référencé dans Helpers.obj
          Référencé dans KernelManager.obj
          Référencé dans lodepng.obj
          Référencé dans ProcessBodyRegions.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans BackgroundRemover.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans main_Kernels.cu.obj
          Chargé vcruntimed.lib(VCRUNTIME140D.dll)
        __std_exception_destroy trouvé
          Référencé dans RAII.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans MSVCRTD.lib(throw_bad_alloc.obj)
          Référencé dans Helpers.obj
          Référencé dans KernelManager.obj
          Référencé dans lodepng.obj
          Référencé dans ProcessBodyRegions.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans BackgroundRemover.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans main_Kernels.cu.obj
          Chargé vcruntimed.lib(VCRUNTIME140D.dll)
        _CxxThrowException trouvé
          Référencé dans RAII.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans MSVCRTD.lib(throw_bad_alloc.obj)
          Référencé dans Helpers.obj
          Référencé dans KernelManager.obj
          Référencé dans lodepng.obj
          Référencé dans ProcessBodyRegions.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans BackgroundRemover.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans main_Kernels.cu.obj
          Chargé vcruntimed.lib(VCRUNTIME140D.dll)
        __CxxFrameHandler4 trouvé
          Référencé dans MSVCRTD.lib(gshandlereh4.obj)
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans msvcprtd.lib(vector_algorithms.obj)
          Référencé dans msvcprtd.lib(filesystem.obj)
          Référencé dans msvcprtd.lib(locale0_implib.obj)
          Référencé dans KernelManager.obj
          Référencé dans lodepng.obj
          Référencé dans ProcessBodyRegions.obj
          Référencé dans RAII.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans BackgroundRemover.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé vcruntimed.lib(VCRUNTIME140_1D.dll)
        memmove trouvé
          Référencé dans RAII.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans Helpers.obj
          Référencé dans KernelManager.obj
          Référencé dans lodepng.obj
          Référencé dans ProcessBodyRegions.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Chargé vcruntimed.lib(VCRUNTIME140D.dll)
        memset trouvé
          Référencé dans cudart_static.lib(cudart_cudart_helper.obj)
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Référencé dans msvcprtd.lib(vector_algorithms.obj)
          Référencé dans msvcprtd.lib(filesystem.obj)
          Référencé dans cudart_static.lib(cudart_cuda_runtime_api.obj)
          Référencé dans cudart_static.lib(cudart_cudart_global.obj)
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Référencé dans cudart_static.lib(cudart_cudart_array.obj)
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans lodepng.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingTensorRt.obj
          Chargé vcruntimed.lib(VCRUNTIME140D.dll)
        memcmp trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Référencé dans cudart_static.lib(cudart_cuda_runtime_api.obj)
          Référencé dans cudart_static.lib(cudart_cudart_etbl.obj)
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Référencé dans msvcprtd.lib(vector_algorithms.obj)
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans HeadDetector.obj
          Référencé dans KernelManager.obj
          Chargé vcruntimed.lib(VCRUNTIME140D.dll)
        strchr trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé vcruntimed.lib(VCRUNTIME140D.dll)
        memchr trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans KernelManager.obj
          Référencé dans msvcprtd.lib(vector_algorithms.obj)
          Chargé vcruntimed.lib(VCRUNTIME140D.dll)
        _purecall trouvé
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Chargé vcruntimed.lib(VCRUNTIME140D.dll)
        wcsrchr trouvé
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé vcruntimed.lib(VCRUNTIME140D.dll)
        wcsstr trouvé
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé vcruntimed.lib(VCRUNTIME140D.dll)
        __C_specific_handler trouvé
          Référencé dans MSVCRTD.lib(utility.obj)
          Référencé dans MSVCRTD.lib(ehvecdtr.obj)
          Référencé dans MSVCRTD.lib(dll_dllmain.obj)
          Chargé vcruntimed.lib(VCRUNTIME140D.dll)
        __current_exception trouvé
          Référencé dans MSVCRTD.lib(ehvecdtr.obj)
          Référencé dans MSVCRTD.lib(utility_desktop.obj)
          Chargé vcruntimed.lib(VCRUNTIME140D.dll)
        __current_exception_context trouvé
          Référencé dans MSVCRTD.lib(ehvecdtr.obj)
          Référencé dans MSVCRTD.lib(utility_desktop.obj)
          Chargé vcruntimed.lib(VCRUNTIME140D.dll)
        __C_specific_handler_noexcept trouvé
          Référencé dans MSVCRTD.lib(error.obj)
          Chargé vcruntimed.lib(VCRUNTIME140D.dll)
        __std_type_info_destroy_list trouvé
          Référencé dans MSVCRTD.lib(tncleanup.obj)
          Chargé vcruntimed.lib(VCRUNTIME140D.dll)
        __vcrt_GetModuleFileNameW trouvé
          Référencé dans MSVCRTD.lib(pdblkup.obj)
          Chargé vcruntimed.lib(VCRUNTIME140D.dll)
        __vcrt_GetModuleHandleW trouvé
          Référencé dans MSVCRTD.lib(pdblkup.obj)
          Chargé vcruntimed.lib(VCRUNTIME140D.dll)
        __vcrt_LoadLibraryExW trouvé
          Référencé dans MSVCRTD.lib(pdblkup.obj)
          Chargé vcruntimed.lib(VCRUNTIME140D.dll)
        __IMPORT_DESCRIPTOR_VCRUNTIME140D trouvé
          Référencé dans vcruntimed.lib(VCRUNTIME140D.dll)
          Référencé dans vcruntimed.lib(VCRUNTIME140D.dll)
          Référencé dans vcruntimed.lib(VCRUNTIME140D.dll)
          Référencé dans vcruntimed.lib(VCRUNTIME140D.dll)
          Référencé dans vcruntimed.lib(VCRUNTIME140D.dll)
          Référencé dans vcruntimed.lib(VCRUNTIME140D.dll)
          Référencé dans vcruntimed.lib(VCRUNTIME140D.dll)
          Référencé dans vcruntimed.lib(VCRUNTIME140D.dll)
          Référencé dans vcruntimed.lib(VCRUNTIME140D.dll)
          Référencé dans vcruntimed.lib(VCRUNTIME140D.dll)
          Référencé dans vcruntimed.lib(VCRUNTIME140D.dll)
          Référencé dans vcruntimed.lib(VCRUNTIME140D.dll)
          Référencé dans vcruntimed.lib(VCRUNTIME140D.dll)
          Référencé dans vcruntimed.lib(VCRUNTIME140D.dll)
          Référencé dans vcruntimed.lib(VCRUNTIME140D.dll)
          Référencé dans vcruntimed.lib(VCRUNTIME140D.dll)
          Référencé dans vcruntimed.lib(VCRUNTIME140D.dll)
          Référencé dans vcruntimed.lib(VCRUNTIME140D.dll)
          Référencé dans vcruntimed.lib(VCRUNTIME140D.dll)
          Référencé dans vcruntimed.lib(VCRUNTIME140D.dll)
          Chargé vcruntimed.lib(VCRUNTIME140D.dll)
        __IMPORT_DESCRIPTOR_VCRUNTIME140_1D trouvé
          Référencé dans vcruntimed.lib(VCRUNTIME140_1D.dll)
          Chargé vcruntimed.lib(VCRUNTIME140_1D.dll)
        VCRUNTIME140D_NULL_THUNK_DATA trouvé
          Référencé dans vcruntimed.lib(VCRUNTIME140D.dll)
          Chargé vcruntimed.lib(VCRUNTIME140D.dll)
        VCRUNTIME140_1D_NULL_THUNK_DATA trouvé
          Référencé dans vcruntimed.lib(VCRUNTIME140_1D.dll)
          Chargé vcruntimed.lib(VCRUNTIME140_1D.dll)
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\ucrtd.lib :
        __imp__invoke_watson trouvé
          Référencé dans ProcessBodyRegions.obj
          Référencé dans RAII.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans Helpers.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans lodepng.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans BackgroundRemover.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans main_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_exit trouvé
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        labs trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        llabs trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        strlen trouvé
          Référencé dans ProcessBodyRegions.obj
          Référencé dans RAII.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        fabs trouvé
          Référencé dans DirectVideoWriter.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans HeadDetector.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_fminf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_fmaxf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_exp2f trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_expm1f trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_log2f trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_log1pf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_acoshf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_asinhf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_atanhf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_ldexp trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_logbf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_ilogbf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_scalblnf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_frexp trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_roundf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_lroundf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_llroundf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_rintf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_lrintf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_llrintf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_nearbyintf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_truncf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_fdimf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_cbrtf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_remainderf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_remquof trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_erff trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_erfcf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_lgammaf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_tgammaf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_copysignf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_nextafterf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_fmaf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_acosf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_asinf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_atanf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_atan2f trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_cosf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_sinf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_tanf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_coshf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_sinhf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_tanhf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_expf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_logf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_log10f trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_modff trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_powf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_sqrtf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_ceilf trouvé
          Référencé dans KernelManager.obj
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_floorf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_fmodf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp__dsign trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp__ldsign trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp__fdsign trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp__hypotf trouvé
          Référencé dans main_Kernels.cu.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans UpscaleResizingKernels.cu.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans Helpers_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp__CrtDbgReport trouvé
          Référencé dans ProcessBodyRegions.obj
          Référencé dans RAII.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans Helpers.obj
          Référencé dans Helpers_Heads.obj
          Référencé dans KernelManager.obj
          Référencé dans lodepng.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans HeadDetector.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans D3D11VideoReader.obj
          Référencé dans D3D11VideoReaderAPI.obj
          Référencé dans BackgroundRemover.obj
          Référencé dans Matting_Kernels.cu.obj
          Référencé dans DownscaleResizingKernels.cu.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans DirectVideoWriter.obj
          Référencé dans BackgroundEstimationKernels.cu.obj
          Référencé dans FFmpegIOKernels.cu.obj
          Référencé dans HeadDetectorKernels.cu.obj
          Référencé dans main_Kernels.cu.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp__calloc_dbg trouvé
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans Helpers.obj
          Référencé dans KernelManager.obj
          Référencé dans ProcessBodyRegions.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans ImageMattingFactory.obj
          Référencé dans ImageMatting.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_wcslen trouvé
          Référencé dans KernelManager.obj
          Référencé dans ProcessBodyRegions.obj
          Référencé dans VideoBackgroundRemover.obj
          Référencé dans ImageMatting.obj
          Référencé dans ImageMattingOnnx.obj
          Référencé dans HeadDetector.obj
          Référencé dans Helpers.obj
          Référencé dans DirectVideoReader.obj
          Référencé dans MultiVideoReader.obj
          Référencé dans BackgroundRemover.obj
          Référencé dans ImageMattingFactory.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_towlower trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_tolower trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp__errno trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_free trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans lodepng.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_realloc trouvé
          Référencé dans ImageMattingFactory.obj
          Référencé dans lodepng.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_strtol trouvé
          Référencé dans ImageMattingFactory.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        strcmp trouvé
          Référencé dans ImageMattingOnnx.obj
          Référencé dans HeadDetector.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp__get_stream_buffer_pointers trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_fclose trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Référencé dans lodepng.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_fflush trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_fgetc trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_fgetpos trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_fputc trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_fread trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Référencé dans lodepng.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_fsetpos trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp__fseeki64 trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_fwrite trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Référencé dans lodepng.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_setvbuf trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_ungetc trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp__lock_file trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp__unlock_file trouvé
          Référencé dans ImageMattingTensorRt.obj
          Référencé dans KernelManager.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_malloc trouvé
          Référencé dans lodepng.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_fopen trouvé
          Référencé dans lodepng.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_fseek trouvé
          Référencé dans lodepng.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_ftell trouvé
          Référencé dans lodepng.obj
          Chargé ucrtd.lib(ucrtbased.dll)
        calloc trouvé
          Référencé dans cudart_static.lib(cudart_cuda_runtime_api.obj)
          Référencé dans cudart_static.lib(cudart_cudart_global.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        atoi trouvé
          Référencé dans cudart_static.lib(cudart_cudart_global.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        strncmp trouvé
          Référencé dans cudart_static.lib(cudart_cudart_global.obj)
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        _time64 trouvé
          Référencé dans cudart_static.lib(cudart_cudart_global.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        strncpy trouvé
          Référencé dans cudart_static.lib(cudart_cudart_context.obj)
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        _get_osfhandle trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        feof trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        ferror trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        _fileno trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        strncat trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        _beginthreadex trouvé
          Référencé dans cudart_static.lib(cudart_cuoswin32.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        isalpha trouvé
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        isdigit trouvé
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        wcstok trouvé
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        _wcsicmp trouvé
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        _wcsnicmp trouvé
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        _strnicmp trouvé
          Référencé dans cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        _rotl trouvé
          Référencé dans msvcprtd.lib(vector_algorithms.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp____lc_codepage_func trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp_abort trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp__free_dbg trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Référencé dans msvcprtd.lib(locale0_implib.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        __imp__malloc_dbg trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Référencé dans msvcprtd.lib(locale0_implib.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        _callnewh trouvé
          Référencé dans MSVCRTD.lib(new_scalar.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        _seh_filter_dll trouvé
          Référencé dans MSVCRTD.lib(utility.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        _configure_narrow_argv trouvé
          Référencé dans MSVCRTD.lib(utility.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        _initialize_narrow_environment trouvé
          Référencé dans MSVCRTD.lib(utility.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        _initialize_onexit_table trouvé
          Référencé dans MSVCRTD.lib(utility.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        _register_onexit_function trouvé
          Référencé dans MSVCRTD.lib(utility.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        _execute_onexit_table trouvé
          Référencé dans MSVCRTD.lib(utility.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        _crt_atexit trouvé
          Référencé dans MSVCRTD.lib(utility.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        _crt_at_quick_exit trouvé
          Référencé dans MSVCRTD.lib(utility.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        _cexit trouvé
          Référencé dans MSVCRTD.lib(utility.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        _CrtDbgReportW trouvé
          Référencé dans MSVCRTD.lib(init.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        terminate trouvé
          Référencé dans MSVCRTD.lib(ehvecdtr.obj)
          Référencé dans MSVCRTD.lib(utility_desktop.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        _initterm trouvé
          Référencé dans MSVCRTD.lib(dll_dllmain.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        _initterm_e trouvé
          Référencé dans MSVCRTD.lib(dll_dllmain.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        strcpy_s trouvé
          Référencé dans MSVCRTD.lib(error.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        strcat_s trouvé
          Référencé dans MSVCRTD.lib(error.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        __stdio_common_vsprintf_s trouvé
          Référencé dans MSVCRTD.lib(error.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        _wmakepath_s trouvé
          Référencé dans MSVCRTD.lib(pdblkup.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        _wsplitpath_s trouvé
          Référencé dans MSVCRTD.lib(pdblkup.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        wcscpy_s trouvé
          Référencé dans MSVCRTD.lib(pdblkup.obj)
          Chargé ucrtd.lib(ucrtbased.dll)
        __IMPORT_DESCRIPTOR_ucrtbased trouvé
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Chargé ucrtd.lib(ucrtbased.dll)
        ucrtbased_NULL_THUNK_DATA trouvé
          Référencé dans ucrtd.lib(ucrtbased.dll)
          Chargé ucrtd.lib(ucrtbased.dll)
      Recherche en cours F:\VccLibs\Tensorrt\lib\nvinfer_10.lib :
      Recherche en cours F:\VccLibs\Tensorrt\lib\nvonnxparser_10.lib :
      Recherche en cours F:\VccLibs\Tensorrt\lib\nvinfer_plugin_10.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cudart_static.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cuda.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cublas.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\curand.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cusparse.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cusolver.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cufft.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avcodec.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avformat.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avutil.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\swresample.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avfilter.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\swscale.lib :
      Recherche en cours F:\VccLibs\onnxruntime\lib\onnxruntime.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\d3d11.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib :
        __imp_FormatMessageA trouvé
          Référencé dans msvcprtd.lib(syserror_import_lib.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetLocaleInfoEx trouvé
          Référencé dans msvcprtd.lib(syserror_import_lib.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_SetCurrentDirectoryW trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetCurrentDirectoryW trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_CreateDirectoryW trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_FindFirstFileW trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_FindFirstFileExW trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_FindNextFileW trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetDiskFreeSpaceExW trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetFileAttributesExW trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetFileInformationByHandle trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetFinalPathNameByHandleW trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_SetFileAttributesW trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_SetFileInformationByHandle trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_SetFileTime trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetTempPathW trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_AreFileApisANSI trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_DeviceIoControl trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_CreateDirectoryExW trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_CopyFileW trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_MoveFileExW trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_CreateHardLinkW trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetFileInformationByHandleEx trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_CreateSymbolicLinkW trouvé
          Référencé dans msvcprtd.lib(filesystem.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_SleepConditionVariableSRW trouvé
          Référencé dans MSVCRTD.lib(thread_safe_statics.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_RtlCaptureContext trouvé
          Référencé dans MSVCRTD.lib(utility_desktop.obj)
          Référencé dans MSVCRTD.lib(gs_report.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_RtlLookupFunctionEntry trouvé
          Référencé dans MSVCRTD.lib(utility_desktop.obj)
          Référencé dans MSVCRTD.lib(gs_report.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_RtlVirtualUnwind trouvé
          Référencé dans MSVCRTD.lib(utility_desktop.obj)
          Référencé dans MSVCRTD.lib(gs_report.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_IsDebuggerPresent trouvé
          Référencé dans MSVCRTD.lib(utility_desktop.obj)
          Référencé dans MSVCRTD.lib(error.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_UnhandledExceptionFilter trouvé
          Référencé dans MSVCRTD.lib(utility_desktop.obj)
          Référencé dans MSVCRTD.lib(gs_report.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_SetUnhandledExceptionFilter trouvé
          Référencé dans MSVCRTD.lib(utility_desktop.obj)
          Référencé dans MSVCRTD.lib(gs_report.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetStartupInfoW trouvé
          Référencé dans MSVCRTD.lib(utility_desktop.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_IsProcessorFeaturePresent trouvé
          Référencé dans MSVCRTD.lib(utility_desktop.obj)
          Référencé dans MSVCRTD.lib(gs_report.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_RaiseException trouvé
          Référencé dans MSVCRTD.lib(error.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetSystemTimeAsFileTime trouvé
          Référencé dans MSVCRTD.lib(gs_support.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_InitializeSListHead trouvé
          Référencé dans MSVCRTD.lib(tncleanup.obj)
          Chargé kernel32.lib(KERNEL32.dll)
        __imp_GetProcessHeap trouvé
          Référencé dans MSVCRTD.lib(pdblkup.obj)
          Chargé kernel32.lib(KERNEL32.dll)
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cudart.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cudadevrt.lib :
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\msvcprtd.lib :
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\MSVCRTD.lib :
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib :
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\vcruntimed.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\ucrtd.lib :
      Recherche en cours F:\VccLibs\Tensorrt\lib\nvinfer_10.lib :
      Recherche en cours F:\VccLibs\Tensorrt\lib\nvonnxparser_10.lib :
      Recherche en cours F:\VccLibs\Tensorrt\lib\nvinfer_plugin_10.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cudart_static.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cuda.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cublas.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\curand.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cusparse.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cusolver.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cufft.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avcodec.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avformat.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avutil.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\swresample.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avfilter.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\swscale.lib :
      Recherche en cours F:\VccLibs\onnxruntime\lib\onnxruntime.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\d3d11.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib :
  
  Fin de la recherche des bibliothèques
  
  Génération du fichier .exp en cours
     Création de la bibliothèque F:\Catechese\EditeurAudioVideo\ImageMatter\x64\Debug\ImageMatterLib.lib et de l'objet F:\Catechese\EditeurAudioVideo\ImageMatter\x64\Debug\ImageMatterLib.exp
  
  Génération de fichier .exp terminée
  
  Recherche en cours des bibliothèques
      Recherche en cours F:\VccLibs\Tensorrt\lib\nvinfer_10.lib :
      Recherche en cours F:\VccLibs\Tensorrt\lib\nvonnxparser_10.lib :
      Recherche en cours F:\VccLibs\Tensorrt\lib\nvinfer_plugin_10.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cudart_static.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cuda.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cublas.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\curand.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cusparse.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cusolver.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cufft.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avcodec.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avformat.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avutil.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\swresample.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avfilter.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\swscale.lib :
      Recherche en cours F:\VccLibs\onnxruntime\lib\onnxruntime.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\d3d11.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cudart.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cudadevrt.lib :
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\msvcprtd.lib :
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\MSVCRTD.lib :
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib :
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\vcruntimed.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\ucrtd.lib :
  
  Fin de la recherche des bibliothèques
  
  Recherche en cours des bibliothèques
      Recherche en cours F:\VccLibs\Tensorrt\lib\nvinfer_10.lib :
      Recherche en cours F:\VccLibs\Tensorrt\lib\nvonnxparser_10.lib :
      Recherche en cours F:\VccLibs\Tensorrt\lib\nvinfer_plugin_10.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cudart_static.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cuda.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cublas.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\curand.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cusparse.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cusolver.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cufft.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avcodec.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avformat.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avutil.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\swresample.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avfilter.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\swscale.lib :
      Recherche en cours F:\VccLibs\onnxruntime\lib\onnxruntime.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\d3d11.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cudart.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cudadevrt.lib :
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\msvcprtd.lib :
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\MSVCRTD.lib :
        __scrt_stub_for_acrt_initialize trouvé
          Chargé MSVCRTD.lib(ucrt_stubs.obj)
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib :
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\vcruntimed.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\ucrtd.lib :
  
  Fin de la recherche des bibliothèques
  
  Recherche en cours des bibliothèques
      Recherche en cours F:\VccLibs\Tensorrt\lib\nvinfer_10.lib :
      Recherche en cours F:\VccLibs\Tensorrt\lib\nvonnxparser_10.lib :
      Recherche en cours F:\VccLibs\Tensorrt\lib\nvinfer_plugin_10.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cudart_static.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cuda.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cublas.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\curand.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cusparse.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cusolver.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cufft.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avcodec.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avformat.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avutil.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\swresample.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avfilter.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\swscale.lib :
      Recherche en cours F:\VccLibs\onnxruntime\lib\onnxruntime.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\d3d11.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cudart.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cudadevrt.lib :
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\msvcprtd.lib :
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\MSVCRTD.lib :
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib :
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\vcruntimed.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\ucrtd.lib :
  
  Fin de la recherche des bibliothèques
  
  Recherche en cours des bibliothèques
      Recherche en cours F:\VccLibs\Tensorrt\lib\nvinfer_10.lib :
      Recherche en cours F:\VccLibs\Tensorrt\lib\nvonnxparser_10.lib :
      Recherche en cours F:\VccLibs\Tensorrt\lib\nvinfer_plugin_10.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cudart_static.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cuda.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cublas.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\curand.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cusparse.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cusolver.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cufft.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avcodec.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avformat.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avutil.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\swresample.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avfilter.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\swscale.lib :
      Recherche en cours F:\VccLibs\onnxruntime\lib\onnxruntime.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\d3d11.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cudart.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cudadevrt.lib :
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\msvcprtd.lib :
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\MSVCRTD.lib :
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib :
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\vcruntimed.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64\ucrtd.lib :
  
  Fin de la recherche des bibliothèques
  
  Fin de la passe 1
  
  
  Recherche en cours des bibliothèques
      Recherche en cours F:\VccLibs\Tensorrt\lib\nvinfer_10.lib :
      Recherche en cours F:\VccLibs\Tensorrt\lib\nvonnxparser_10.lib :
      Recherche en cours F:\VccLibs\Tensorrt\lib\nvinfer_plugin_10.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cudart_static.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cuda.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cublas.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\curand.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cusparse.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cusolver.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cufft.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avcodec.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avformat.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avutil.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\swresample.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\avfilter.lib :
      Recherche en cours F:\VccLibs\ffmpeg\bin\swscale.lib :
      Recherche en cours F:\VccLibs\onnxruntime\lib\onnxruntime.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\d3d11.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\kernel32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib :
      Recherche en cours C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cudart.lib :
      Recherche en cours C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cudadevrt.lib :
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\msvcprtd.lib :
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\MSVCRTD.lib :
        _load_config_used trouvé
          Chargé MSVCRTD.lib(loadcfg.obj)
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib :
      Recherche en cours C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\vcruntimed.lib :
        __guard_memcpy_fptr trouvé
          Référencé dans MSVCRTD.lib(loadcfg.obj)
          Chargé vcruntimed.lib(softmemtag.obj)
  
  Fin de la recherche des bibliothèques
  
  Appel de rc.exe :
   /v
   /x
   /fo
   "C:\Users\<USER>\AppData\Local\Temp\lnk{D9D4D63C-3E9E-4ADE-A8A7-948DA87930E8}.tmp"
   "C:\Users\<USER>\AppData\Local\Temp\lnk{6B331890-589C-4393-9AE6-97DA1139295B}.tmp"
  Microsoft (R) Windows (R) Resource Compiler Version 10.0.10011.16384
  
  Copyright (C) Microsoft Corporation.  All rights reserved.
  
  
  Using codepage 1252 as default
  Creating C:\Users\<USER>\AppData\Local\Temp\lnk{D9D4D63C-3E9E-4ADE-A8A7-948DA87930E8}.tmp
  
  
  C:\Users\<USER>\AppData\Local\Temp\lnk{6B331890-589C-4393-9AE6-97DA1139295B}.tmp.
  Writing 24:2,	lang:0x409,	size 381
  
  Appel de cvtres.exe :
   /machine:amd64
   /verbose
   /out:"C:\Users\<USER>\AppData\Local\Temp\lnk{09BF97D1-35F3-4694-AEFB-DF4F67CEDDDB}.tmp"
   /readonly
   "C:\Users\<USER>\AppData\Local\Temp\lnk{D9D4D63C-3E9E-4ADE-A8A7-948DA87930E8}.tmp"
  Microsoft (R) Windows Resource To Object Converter Version 14.44.35215.0
  Copyright (C) Microsoft Corporation. Tous droits rÚservÚs.
  
  ajout d'une ressource. Typeá: MANIFEST, nomá: 2, langageá: 0x0409, indicateursá: 0x30, tailleá: 381
  
  
  Bibliothèques inutilisées :
    F:\VccLibs\Tensorrt\lib\nvonnxparser_10.lib
    F:\VccLibs\Tensorrt\lib\nvinfer_plugin_10.lib
    C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cublas.lib
    C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\curand.lib
    C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cusparse.lib
    C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cusolver.lib
    C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cufft.lib
    F:\VccLibs\ffmpeg\bin\swresample.lib
    F:\VccLibs\ffmpeg\bin\avfilter.lib
    F:\VccLibs\ffmpeg\bin\swscale.lib
    C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\d3d11.lib
    C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\dxgi.lib
    C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\user32.lib
    C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\gdi32.lib
    C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\winspool.lib
    C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\comdlg32.lib
    C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\advapi32.lib
    C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\shell32.lib
    C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\ole32.lib
    C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\oleaut32.lib
    C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\uuid.lib
    C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbc32.lib
    C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\um\x64\odbccp32.lib
    C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cudart.lib
    C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.9\lib\x64\cudadevrt.lib
    C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\lib\x64\OLDNAMES.lib
  
  Début de la passe 2
       * linker generated manifest res *
       ImageMatterLib.exp
       VideoBackgroundRemover.obj
       RAII.obj
       ProcessBodyRegions.obj
       lodepng.obj
       KernelManager.obj
       Helpers_Heads.obj
       Helpers.obj
       HeadDetector.obj
       ImageMattingTensorRt.obj
       ImageMattingOnnx.obj
       ImageMatting.obj
       ImageMattingFactory.obj
       dllmain.obj
       BackgroundRemover.obj
       D3D11VideoReaderAPI.obj
       D3D11VideoReader.obj
       MultiVideoReader.obj
       DirectVideoWriter.obj
       DirectVideoReader.obj
       UpscaleResizingKernels.cu.obj
       DownscaleResizingKernels.cu.obj
       Matting_Kernels.cu.obj
       main_Kernels.cu.obj
       Helpers_Kernels.cu.obj
       HeadDetectorKernels.cu.obj
       FFmpegIOKernels.cu.obj
       BackgroundEstimationKernels.cu.obj
       nvinfer_10.lib(nvinfer_10.dll)
       nvinfer_10.lib(nvinfer_10.dll)
       nvinfer_10.lib(nvinfer_10.dll)
       nvinfer_10.lib(nvinfer_10.dll)
       cudart_static.lib(cudart_cuda_runtime_api.obj)
       cudart_static.lib(cudart_cudart_global.obj)
       cudart_static.lib(cudart_cudart_thread.obj)
       cudart_static.lib(cudart_cudart_context.obj)
       cudart_static.lib(cudart_cudart_context_mgr.obj)
       cudart_static.lib(cudart_cudart_array.obj)
       cudart_static.lib(cudart_cudart_helper.obj)
       cudart_static.lib(cudart_cudart_tls.obj)
       cudart_static.lib(cudart_generated_error_tables.obj)
       cudart_static.lib(cudart_generated_cuda_runtime_api_names.obj)
       cudart_static.lib(cudart_generated_cuda_runtime_api.obj)
       cudart_static.lib(cudart_generated_cuda_d3d11_interop.obj)
       cudart_static.lib(cudart_cudart_etbl.obj)
       cudart_static.lib(cudart_runtime_error_table.obj)
       cudart_static.lib(cudart_tools_runtime_instance.obj)
       cudart_static.lib(cudart_tools_runtime_callbacks.obj)
       cudart_static.lib(cudart_cuos_once.obj)
       cudart_static.lib(cudart_cuoswin32.obj)
       cudart_static.lib(cudart_nvSecureLoadLibrary.obj)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       cuda.lib(nvcuda.dll)
       avcodec.lib(avcodec-61.dll)
       avcodec.lib(avcodec-61.dll)
       avcodec.lib(avcodec-61.dll)
       avcodec.lib(avcodec-61.dll)
       avcodec.lib(avcodec-61.dll)
       avcodec.lib(avcodec-61.dll)
       avcodec.lib(avcodec-61.dll)
       avcodec.lib(avcodec-61.dll)
       avcodec.lib(avcodec-61.dll)
       avcodec.lib(avcodec-61.dll)
       avcodec.lib(avcodec-61.dll)
       avcodec.lib(avcodec-61.dll)
       avcodec.lib(avcodec-61.dll)
       avcodec.lib(avcodec-61.dll)
       avcodec.lib(avcodec-61.dll)
       avcodec.lib(avcodec-61.dll)
       avcodec.lib(avcodec-61.dll)
       avformat.lib(avformat-61.dll)
       avformat.lib(avformat-61.dll)
       avformat.lib(avformat-61.dll)
       avformat.lib(avformat-61.dll)
       avformat.lib(avformat-61.dll)
       avformat.lib(avformat-61.dll)
       avformat.lib(avformat-61.dll)
       avformat.lib(avformat-61.dll)
       avformat.lib(avformat-61.dll)
       avformat.lib(avformat-61.dll)
       avformat.lib(avformat-61.dll)
       avformat.lib(avformat-61.dll)
       avformat.lib(avformat-61.dll)
       avformat.lib(avformat-61.dll)
       avformat.lib(avformat-61.dll)
       avformat.lib(avformat-61.dll)
       avutil.lib(avutil-59.dll)
       avutil.lib(avutil-59.dll)
       avutil.lib(avutil-59.dll)
       avutil.lib(avutil-59.dll)
       avutil.lib(avutil-59.dll)
       avutil.lib(avutil-59.dll)
       avutil.lib(avutil-59.dll)
       avutil.lib(avutil-59.dll)
       avutil.lib(avutil-59.dll)
       avutil.lib(avutil-59.dll)
       avutil.lib(avutil-59.dll)
       avutil.lib(avutil-59.dll)
       avutil.lib(avutil-59.dll)
       avutil.lib(avutil-59.dll)
       avutil.lib(avutil-59.dll)
       avutil.lib(avutil-59.dll)
       avutil.lib(avutil-59.dll)
       onnxruntime.lib(onnxruntime.dll)
       onnxruntime.lib(onnxruntime.dll)
       onnxruntime.lib(onnxruntime.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       kernel32.lib(KERNEL32.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(MSVCP140D.dll)
       msvcprtd.lib(vector_algorithms.obj)
       msvcprtd.lib(syserror_import_lib.obj)
       msvcprtd.lib(locale0_implib.obj)
       msvcprtd.lib(filesystem.obj)
       MSVCRTD.lib(argv_mode.obj)
       MSVCRTD.lib(default_local_stdio_options.obj)
       MSVCRTD.lib(fltused.obj)
       MSVCRTD.lib(ehvecdtr.obj)
       MSVCRTD.lib(std_type_info_static.obj)
       MSVCRTD.lib(tncleanup.obj)
       MSVCRTD.lib(delete_array.obj)
       MSVCRTD.lib(delete_scalar.obj)
       MSVCRTD.lib(delete_scalar_size.obj)
       MSVCRTD.lib(new_array.obj)
       MSVCRTD.lib(new_scalar.obj)
       MSVCRTD.lib(throw_bad_alloc.obj)
       MSVCRTD.lib(thread_safe_statics.obj)
       MSVCRTD.lib(dll_dllmain.obj)
       MSVCRTD.lib(tlssup.obj)
       MSVCRTD.lib(initializers.obj)
       MSVCRTD.lib(utility.obj)
       MSVCRTD.lib(ucrt_stubs.obj)
       MSVCRTD.lib(gs_cookie.obj)
       MSVCRTD.lib(gs_report.obj)
       MSVCRTD.lib(gs_support.obj)
       MSVCRTD.lib(debugger_jmc.obj)
       MSVCRTD.lib(guard_support.obj)
       MSVCRTD.lib(loadcfg.obj)
       MSVCRTD.lib(dyn_tls_init.obj)
       MSVCRTD.lib(ucrt_detection.obj)
       MSVCRTD.lib(cpu_disp.obj)
       MSVCRTD.lib(utility_desktop.obj)
       MSVCRTD.lib(error.obj)
       MSVCRTD.lib(init.obj)
       MSVCRTD.lib(initsect.obj)
       MSVCRTD.lib(pdblkup.obj)
       MSVCRTD.lib(stack.obj)
       MSVCRTD.lib(userapi.obj)
       MSVCRTD.lib(gshandler.obj)
       MSVCRTD.lib(gshandlereh4.obj)
       MSVCRTD.lib(amdsecgs.obj)
       MSVCRTD.lib(chkstk.obj)
       MSVCRTD.lib(guard_dispatch.obj)
       MSVCRTD.lib(guard_xfg_dispatch.obj)
       vcruntimed.lib(VCRUNTIME140_1D.dll)
       vcruntimed.lib(VCRUNTIME140_1D.dll)
       vcruntimed.lib(VCRUNTIME140_1D.dll)
       vcruntimed.lib(VCRUNTIME140D.dll)
       vcruntimed.lib(VCRUNTIME140D.dll)
       vcruntimed.lib(VCRUNTIME140D.dll)
       vcruntimed.lib(VCRUNTIME140D.dll)
       vcruntimed.lib(VCRUNTIME140D.dll)
       vcruntimed.lib(VCRUNTIME140D.dll)
       vcruntimed.lib(VCRUNTIME140D.dll)
       vcruntimed.lib(VCRUNTIME140D.dll)
       vcruntimed.lib(VCRUNTIME140D.dll)
       vcruntimed.lib(VCRUNTIME140D.dll)
       vcruntimed.lib(VCRUNTIME140D.dll)
       vcruntimed.lib(VCRUNTIME140D.dll)
       vcruntimed.lib(VCRUNTIME140D.dll)
       vcruntimed.lib(VCRUNTIME140D.dll)
       vcruntimed.lib(VCRUNTIME140D.dll)
       vcruntimed.lib(VCRUNTIME140D.dll)
       vcruntimed.lib(VCRUNTIME140D.dll)
       vcruntimed.lib(VCRUNTIME140D.dll)
       vcruntimed.lib(VCRUNTIME140D.dll)
       vcruntimed.lib(VCRUNTIME140D.dll)
       vcruntimed.lib(VCRUNTIME140D.dll)
       vcruntimed.lib(VCRUNTIME140D.dll)
       vcruntimed.lib(softmemtag.obj)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
       ucrtd.lib(ucrtbased.dll)
  Fin de la passe 2
  ImageMatterLib.vcxproj -> F:\Catechese\EditeurAudioVideo\ImageMatter\x64\Debug\ImageMatterLib.dll
  Generating ImageMatterKernels.ptx...
  **********************************************************************
  ** Visual Studio 2022 Developer Command Prompt v17.14.13
  ** Copyright (c) 2025 Microsoft Corporation
  **********************************************************************
  [vcvarsall.bat] Environment initialized for: 'x64'
  Creating master file...
  Compiling master file...
  ImageMatterKernels_Master.cu
    - ImageMatterKernels.ptx generated successfully
    - PTX copied to Debug directory
    - PTX copied to Release directory
  Done.
