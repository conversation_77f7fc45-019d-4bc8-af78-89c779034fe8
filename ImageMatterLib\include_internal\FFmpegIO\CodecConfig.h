#pragma once

#include <string>

// General codec configuration (hardware NVENC only), with an encodeAlpha flag
struct CodecConfig {
    // Basic video parameters
    int width = 1920;
    int height = 1080;
    double frameRate = 30.0;

    // Encode alpha: when true, the writer will expect grayscale frames (alpha)
    // and store them in Y (U/V filled neutral). When false, it expects RGB frames
    // and converts them to YUV420.
    bool encodeAlpha = false;

    // Codec selection (hardware only)
    enum class CodecType {
        H264_NVENC,
        HEVC_NVENC
    };
    CodecType codecType = CodecType::HEVC_NVENC;

    // Rate control
    enum class RateControlMode {
        CONSTANT_QP,
        CONSTANT_QUALITY,
        VARIABLE_BITRATE,
        CONSTANT_BITRATE
    };
    RateControlMode rateControl = RateControlMode::CONSTANT_QUALITY;

    // Quality/bitrate settings
    int quality = 18;           // Used for CQ/CRF or QP depending on rate control
    int bitrateMbps = 10;
    int maxBitrateMbps = 15;

    // Profiles and presets
    enum class Profile {
        BASELINE,
        MAIN,
        HIGH,
        MAIN_10
    };
    Profile profile = Profile::MAIN;

    enum class Preset {
        FAST,
        MEDIUM,
        SLOW,
        SLOWEST
    };
    Preset preset = Preset::MEDIUM;

    // Advanced options
    int gopSize = 30;
    int bFrames = 0;
    bool lowLatency = true;
    bool lossless = false;

    // Buffering
    int asyncBufferSize = 5;

    std::string GetCodecName() const {
        switch (codecType) {
            case CodecType::H264_NVENC: return "h264_nvenc";
            case CodecType::HEVC_NVENC: return "hevc_nvenc";
            default: return "hevc_nvenc";
        }
    }

    std::string GetProfileName() const {
        if (codecType == CodecType::HEVC_NVENC) {
            switch (profile) {
                case Profile::BASELINE: return "0"; // HEVC Main profile
                case Profile::MAIN: return "0";
                case Profile::HIGH: return "0";
                case Profile::MAIN_10: return "1"; // Main 10
                default: return "0";
            }
        } else {
            switch (profile) {
                case Profile::BASELINE: return "baseline";
                case Profile::MAIN: return "main";
                case Profile::HIGH: return "high";
                case Profile::MAIN_10: return "high";
                default: return "main";
            }
        }
    }

    std::string GetPresetString() const {
        switch (preset) {
            case Preset::FAST: return "p2";
            case Preset::MEDIUM: return "p4";
            case Preset::SLOW: return "p6";
            case Preset::SLOWEST: return "p7";
            default: return "p4";
        }
    }

    std::string GetRateControlString() const {
        if (codecType == CodecType::HEVC_NVENC) {
            switch (rateControl) {
                case RateControlMode::CONSTANT_QP: return "constqp";
                case RateControlMode::CONSTANT_QUALITY: return "vbr"; // HEVC uses vbr with cq parameter
                case RateControlMode::VARIABLE_BITRATE: return "vbr";
                case RateControlMode::CONSTANT_BITRATE: return "cbr";
                default: return "vbr";
            }
        } else {
            switch (rateControl) {
                case RateControlMode::CONSTANT_QP: return "constqp";
                case RateControlMode::CONSTANT_QUALITY: return "cq";
                case RateControlMode::VARIABLE_BITRATE: return "vbr";
                case RateControlMode::CONSTANT_BITRATE: return "cbr";
                default: return "cq";
            }
        }
    }

    // Hardware acceleration is always on in this codebase
    bool IsHardwareAccelerated() const { return true; }
};
