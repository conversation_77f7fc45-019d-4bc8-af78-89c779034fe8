//
// Generated by NVIDIA NVVM Compiler
//
// Compiler Build ID: CL-35813241
// Cuda compilation tools, release 12.9, V12.9.41
// Based on NVVM 7.0.1
//

.version 8.8
.target sm_52
.address_size 64

	// .globl	_Z34estimateBackgroundHorizontalKernelPKfS0_P14BackgroundDataii
.global .align 4 .b8 __cudart_i2opi_f[24] = {65, 144, 67, 60, 153, 149, 98, 219, 192, 221, 52, 245, 209, 87, 39, 252, 41, 21, 68, 78, 110, 131, 249, 162};

.visible .entry _Z34estimateBackgroundHorizontalKernelPKfS0_P14BackgroundDataii(
	.param .u64 _Z34estimateBackgroundHorizontalKernelPKfS0_P14BackgroundDataii_param_0,
	.param .u64 _Z34estimateBackgroundHorizontalKernelPKfS0_P14BackgroundDataii_param_1,
	.param .u64 _Z34estimateBackgroundHorizontalKernelPKfS0_P14BackgroundDataii_param_2,
	.param .u32 _Z34estimateBackgroundHorizontalKernelPKfS0_P14BackgroundDataii_param_3,
	.param .u32 _Z34estimateBackgroundHorizontalKernelPKfS0_P14BackgroundDataii_param_4
)
{
	.reg .pred 	%p<85>;
	.reg .b16 	%rs<11>;
	.reg .f32 	%f<557>;
	.reg .b32 	%r<221>;
	.reg .b64 	%rd<75>;


	ld.param.u64 	%rd36, [_Z34estimateBackgroundHorizontalKernelPKfS0_P14BackgroundDataii_param_0];
	ld.param.u64 	%rd37, [_Z34estimateBackgroundHorizontalKernelPKfS0_P14BackgroundDataii_param_1];
	ld.param.u64 	%rd38, [_Z34estimateBackgroundHorizontalKernelPKfS0_P14BackgroundDataii_param_2];
	ld.param.u32 	%r94, [_Z34estimateBackgroundHorizontalKernelPKfS0_P14BackgroundDataii_param_3];
	ld.param.u32 	%r95, [_Z34estimateBackgroundHorizontalKernelPKfS0_P14BackgroundDataii_param_4];
	cvta.to.global.u64 	%rd1, %rd37;
	cvta.to.global.u64 	%rd2, %rd36;
	cvta.to.global.u64 	%rd3, %rd38;
	mov.u32 	%r96, %ntid.y;
	mov.u32 	%r97, %ctaid.y;
	mov.u32 	%r98, %tid.y;
	mad.lo.s32 	%r1, %r97, %r96, %r98;
	setp.ge.s32 	%p1, %r1, %r95;
	@%p1 bra 	$L__BB0_103;

	max.s32 	%r2, %r94, %r95;
	setp.lt.s32 	%p2, %r94, 1;
	mov.u32 	%r204, -1;
	mov.u16 	%rs9, 0;
	mov.f32 	%f549, 0f00000000;
	mov.f32 	%f550, %f549;
	mov.f32 	%f551, %f549;
	mov.f32 	%f552, %f549;
	@%p2 bra 	$L__BB0_71;

	mul.lo.s32 	%r3, %r1, %r94;
	mul.lo.s32 	%r4, %r95, %r94;
	cvt.rn.f32.s32 	%f1, %r2;
	add.f32 	%f2, %f1, %f1;
	add.s32 	%r5, %r1, -1;
	mov.u32 	%r204, -1;
	sub.s32 	%r6, %r3, %r94;
	add.s32 	%r7, %r1, 1;
	shl.b32 	%r102, %r94, 1;
	add.s32 	%r8, %r6, %r102;
	mov.f32 	%f552, 0f00000000;
	mov.u32 	%r187, 0;
	mov.u16 	%rs9, 0;
	mov.f32 	%f551, %f552;
	mov.f32 	%f550, %f552;
	mov.f32 	%f549, %f552;

$L__BB0_3:
	mov.f32 	%f6, %f549;
	mov.f32 	%f5, %f550;
	mov.f32 	%f4, %f551;
	mov.f32 	%f3, %f552;
	add.s32 	%r103, %r187, %r3;
	mul.wide.s32 	%rd39, %r103, 4;
	add.s64 	%rd5, %rd2, %rd39;
	ld.global.f32 	%f244, [%rd5];
	setp.gt.f32 	%p3, %f244, 0f3CA0A0A1;
	@%p3 bra 	$L__BB0_70;

	setp.gt.s32 	%p4, %r1, %r95;
	add.s32 	%r11, %r187, -1;
	or.b32  	%r104, %r11, %r5;
	setp.lt.s32 	%p5, %r104, 0;
	setp.gt.s32 	%p6, %r187, %r94;
	or.pred  	%p7, %p6, %p5;
	or.pred  	%p8, %p4, %p7;
	mov.f32 	%f549, %f6;
	mov.f32 	%f550, %f5;
	mov.f32 	%f551, %f4;
	mov.f32 	%f552, %f3;
	@%p8 bra 	$L__BB0_70;

	setp.lt.s32 	%p9, %r1, 1;
	add.s32 	%r105, %r187, %r6;
	mul.wide.s32 	%rd40, %r105, 4;
	add.s64 	%rd6, %rd2, %rd40;
	ld.global.f32 	%f245, [%rd6+-4];
	setp.gt.f32 	%p10, %f245, 0f3CA0A0A1;
	setp.ge.s32 	%p11, %r187, %r94;
	or.pred  	%p12, %p11, %p9;
	or.pred  	%p13, %p10, %p12;
	mov.f32 	%f549, %f6;
	mov.f32 	%f550, %f5;
	mov.f32 	%f551, %f4;
	mov.f32 	%f552, %f3;
	@%p13 bra 	$L__BB0_70;

	ld.global.f32 	%f246, [%rd6];
	setp.gt.f32 	%p14, %f246, 0f3CA0A0A1;
	mov.f32 	%f549, %f6;
	mov.f32 	%f550, %f5;
	mov.f32 	%f551, %f4;
	mov.f32 	%f552, %f3;
	@%p14 bra 	$L__BB0_70;

	add.s32 	%r106, %r187, 1;
	setp.ge.s32 	%p15, %r106, %r94;
	mov.f32 	%f549, %f6;
	mov.f32 	%f550, %f5;
	mov.f32 	%f551, %f4;
	mov.f32 	%f552, %f3;
	@%p15 bra 	$L__BB0_70;

	ld.global.f32 	%f247, [%rd6+4];
	setp.gt.f32 	%p16, %f247, 0f3CA0A0A1;
	or.b32  	%r107, %r11, %r1;
	setp.lt.s32 	%p17, %r107, 0;
	or.pred  	%p18, %p16, %p17;
	mov.f32 	%f549, %f6;
	mov.f32 	%f550, %f5;
	mov.f32 	%f551, %f4;
	mov.f32 	%f552, %f3;
	@%p18 bra 	$L__BB0_70;

	ld.global.f32 	%f248, [%rd5+-4];
	setp.gt.f32 	%p19, %f248, 0f3CA0A0A1;
	mov.f32 	%f549, %f6;
	mov.f32 	%f550, %f5;
	mov.f32 	%f551, %f4;
	mov.f32 	%f552, %f3;
	@%p19 bra 	$L__BB0_70;

	setp.lt.s32 	%p20, %r1, 0;
	mov.f32 	%f549, %f6;
	mov.f32 	%f550, %f5;
	mov.f32 	%f551, %f4;
	mov.f32 	%f552, %f3;
	@%p20 bra 	$L__BB0_70;

	ld.global.f32 	%f249, [%rd5+4];
	setp.gt.f32 	%p21, %f249, 0f3CA0A0A1;
	mov.f32 	%f549, %f6;
	mov.f32 	%f550, %f5;
	mov.f32 	%f551, %f4;
	mov.f32 	%f552, %f3;
	@%p21 bra 	$L__BB0_70;

	setp.ge.s32 	%p22, %r7, %r95;
	or.b32  	%r108, %r11, %r7;
	setp.lt.s32 	%p23, %r108, 0;
	or.pred  	%p24, %p22, %p23;
	mov.f32 	%f549, %f6;
	mov.f32 	%f550, %f5;
	mov.f32 	%f551, %f4;
	mov.f32 	%f552, %f3;
	@%p24 bra 	$L__BB0_70;

	add.s32 	%r109, %r187, %r8;
	mul.wide.s32 	%rd41, %r109, 4;
	add.s64 	%rd7, %rd2, %rd41;
	ld.global.f32 	%f250, [%rd7+-4];
	setp.gt.f32 	%p25, %f250, 0f3CA0A0A1;
	mov.f32 	%f549, %f6;
	mov.f32 	%f550, %f5;
	mov.f32 	%f551, %f4;
	mov.f32 	%f552, %f3;
	@%p25 bra 	$L__BB0_70;

	setp.lt.s32 	%p26, %r1, -1;
	mov.f32 	%f549, %f6;
	mov.f32 	%f550, %f5;
	mov.f32 	%f551, %f4;
	mov.f32 	%f552, %f3;
	@%p26 bra 	$L__BB0_70;

	ld.global.f32 	%f251, [%rd7];
	setp.gt.f32 	%p27, %f251, 0f3CA0A0A1;
	mov.f32 	%f549, %f6;
	mov.f32 	%f550, %f5;
	mov.f32 	%f551, %f4;
	mov.f32 	%f552, %f3;
	@%p27 bra 	$L__BB0_70;

	ld.global.f32 	%f252, [%rd7+4];
	setp.gt.f32 	%p28, %f252, 0f3CA0A0A1;
	mov.f32 	%f549, %f6;
	mov.f32 	%f550, %f5;
	mov.f32 	%f551, %f4;
	mov.f32 	%f552, %f3;
	@%p28 bra 	$L__BB0_70;

	add.s32 	%r111, %r11, %r6;
	mul.wide.s32 	%rd42, %r111, 4;
	add.s64 	%rd8, %rd1, %rd42;
	ld.global.f32 	%f7, [%rd8];
	add.f32 	%f466, %f7, 0f00000000;
	add.s32 	%r112, %r111, %r4;
	cvt.s64.s32 	%rd9, %r4;
	mul.wide.s32 	%rd43, %r4, 4;
	add.s64 	%rd10, %rd8, %rd43;
	ld.global.f32 	%f9, [%rd10];
	add.f32 	%f467, %f9, 0f00000000;
	add.s32 	%r113, %r112, %r4;
	mul.wide.s32 	%rd44, %r113, 4;
	add.s64 	%rd11, %rd1, %rd44;
	ld.global.f32 	%f11, [%rd11];
	add.f32 	%f468, %f11, 0f00000000;
	mov.u32 	%r189, 1;
	@%p9 bra 	$L__BB0_19;

	add.f32 	%f455, %f11, 0f00000000;
	add.f32 	%f454, %f9, 0f00000000;
	add.f32 	%f453, %f7, 0f00000000;
	ld.global.f32 	%f253, [%rd8+4];
	add.f32 	%f466, %f453, %f253;
	ld.global.f32 	%f254, [%rd10+4];
	add.f32 	%f467, %f454, %f254;
	ld.global.f32 	%f255, [%rd11+4];
	add.f32 	%f468, %f455, %f255;
	mov.u32 	%r189, 2;

$L__BB0_19:
	@%p9 bra 	$L__BB0_21;

	ld.global.f32 	%f256, [%rd8+8];
	add.f32 	%f466, %f466, %f256;
	ld.global.f32 	%f257, [%rd10+8];
	add.f32 	%f467, %f467, %f257;
	ld.global.f32 	%f258, [%rd11+8];
	add.f32 	%f468, %f468, %f258;
	add.s32 	%r189, %r189, 1;

$L__BB0_21:
	add.s32 	%r115, %r11, %r3;
	mul.wide.s32 	%rd45, %r115, 4;
	add.s64 	%rd12, %rd1, %rd45;
	ld.global.f32 	%f25, [%rd12];
	add.f32 	%f472, %f466, %f25;
	add.s32 	%r116, %r115, %r4;
	shl.b64 	%rd46, %rd9, 2;
	add.s64 	%rd13, %rd12, %rd46;
	ld.global.f32 	%f27, [%rd13];
	add.f32 	%f473, %f467, %f27;
	add.s32 	%r117, %r116, %r4;
	mul.wide.s32 	%rd47, %r117, 4;
	add.s64 	%rd14, %rd1, %rd47;
	ld.global.f32 	%f29, [%rd14];
	add.f32 	%f474, %f468, %f29;
	add.s32 	%r191, %r189, 1;
	@%p20 bra 	$L__BB0_23;

	ld.global.f32 	%f259, [%rd12+4];
	add.f32 	%f472, %f472, %f259;
	ld.global.f32 	%f260, [%rd13+4];
	add.f32 	%f473, %f473, %f260;
	ld.global.f32 	%f261, [%rd14+4];
	add.f32 	%f474, %f474, %f261;
	add.s32 	%r191, %r189, 2;

$L__BB0_23:
	@%p20 bra 	$L__BB0_25;

	ld.global.f32 	%f262, [%rd12+8];
	add.f32 	%f472, %f472, %f262;
	ld.global.f32 	%f263, [%rd13+8];
	add.f32 	%f473, %f473, %f263;
	ld.global.f32 	%f264, [%rd14+8];
	add.f32 	%f474, %f474, %f264;
	add.s32 	%r191, %r191, 1;

$L__BB0_25:
	cvt.s64.s32 	%rd69, %r4;
	shl.b64 	%rd68, %rd69, 2;
	add.s32 	%r185, %r187, -1;
	add.s32 	%r118, %r185, %r8;
	mul.wide.s32 	%rd48, %r118, 4;
	add.s64 	%rd15, %rd1, %rd48;
	ld.global.f32 	%f43, [%rd15];
	add.f32 	%f478, %f472, %f43;
	add.s32 	%r119, %r118, %r4;
	add.s64 	%rd16, %rd15, %rd68;
	ld.global.f32 	%f45, [%rd16];
	add.f32 	%f479, %f473, %f45;
	add.s32 	%r120, %r119, %r4;
	mul.wide.s32 	%rd50, %r120, 4;
	add.s64 	%rd17, %rd1, %rd50;
	ld.global.f32 	%f47, [%rd17];
	add.f32 	%f480, %f474, %f47;
	add.s32 	%r193, %r191, 1;
	@%p26 bra 	$L__BB0_27;

	ld.global.f32 	%f265, [%rd15+4];
	add.f32 	%f478, %f478, %f265;
	ld.global.f32 	%f266, [%rd16+4];
	add.f32 	%f479, %f479, %f266;
	ld.global.f32 	%f267, [%rd17+4];
	add.f32 	%f480, %f480, %f267;
	add.s32 	%r193, %r191, 2;

$L__BB0_27:
	@%p26 bra 	$L__BB0_29;

	ld.global.f32 	%f268, [%rd15+8];
	add.f32 	%f478, %f478, %f268;
	ld.global.f32 	%f269, [%rd16+8];
	add.f32 	%f479, %f479, %f269;
	ld.global.f32 	%f270, [%rd17+8];
	add.f32 	%f480, %f480, %f270;
	add.s32 	%r193, %r193, 1;

$L__BB0_29:
	setp.gt.s32 	%p35, %r193, 0;
	@%p35 bra 	$L__BB0_31;
	bra.uni 	$L__BB0_30;

$L__BB0_31:
	cvt.rn.f32.s32 	%f271, %r193;
	div.rn.f32 	%f549, %f478, %f271;
	div.rn.f32 	%f550, %f479, %f271;
	div.rn.f32 	%f551, %f480, %f271;
	bra.uni 	$L__BB0_32;

$L__BB0_30:
	ld.global.f32 	%f549, [%rd12+4];
	ld.global.f32 	%f550, [%rd13+4];
	ld.global.f32 	%f551, [%rd14+4];

$L__BB0_32:
	add.f32 	%f490, %f11, 0f00000000;
	add.f32 	%f491, %f9, 0f00000000;
	add.f32 	%f492, %f7, 0f00000000;
	fma.rn.f32 	%f493, %f7, %f7, 0f00000000;
	fma.rn.f32 	%f494, %f9, %f9, 0f00000000;
	fma.rn.f32 	%f495, %f11, %f11, 0f00000000;
	mov.u32 	%r195, 1;
	@%p9 bra 	$L__BB0_34;

	add.f32 	%f458, %f11, 0f00000000;
	add.f32 	%f457, %f9, 0f00000000;
	add.f32 	%f456, %f7, 0f00000000;
	ld.global.f32 	%f272, [%rd8+4];
	add.f32 	%f492, %f456, %f272;
	ld.global.f32 	%f273, [%rd10+4];
	add.f32 	%f491, %f457, %f273;
	ld.global.f32 	%f274, [%rd11+4];
	add.f32 	%f490, %f458, %f274;
	fma.rn.f32 	%f493, %f272, %f272, %f493;
	fma.rn.f32 	%f494, %f273, %f273, %f494;
	fma.rn.f32 	%f495, %f274, %f274, %f495;
	mov.u32 	%r195, 2;

$L__BB0_34:
	@%p9 bra 	$L__BB0_36;

	ld.global.f32 	%f275, [%rd8+8];
	add.f32 	%f492, %f492, %f275;
	ld.global.f32 	%f276, [%rd10+8];
	add.f32 	%f491, %f491, %f276;
	ld.global.f32 	%f277, [%rd11+8];
	add.f32 	%f490, %f490, %f277;
	fma.rn.f32 	%f493, %f275, %f275, %f493;
	fma.rn.f32 	%f494, %f276, %f276, %f494;
	fma.rn.f32 	%f495, %f277, %f277, %f495;
	add.s32 	%r195, %r195, 1;

$L__BB0_36:
	add.f32 	%f504, %f492, %f25;
	add.f32 	%f503, %f491, %f27;
	add.f32 	%f502, %f490, %f29;
	fma.rn.f32 	%f505, %f25, %f25, %f493;
	fma.rn.f32 	%f506, %f27, %f27, %f494;
	fma.rn.f32 	%f507, %f29, %f29, %f495;
	add.s32 	%r197, %r195, 1;
	@%p20 bra 	$L__BB0_38;

	ld.global.f32 	%f278, [%rd12+4];
	add.f32 	%f504, %f504, %f278;
	ld.global.f32 	%f279, [%rd13+4];
	add.f32 	%f503, %f503, %f279;
	ld.global.f32 	%f280, [%rd14+4];
	add.f32 	%f502, %f502, %f280;
	fma.rn.f32 	%f505, %f278, %f278, %f505;
	fma.rn.f32 	%f506, %f279, %f279, %f506;
	fma.rn.f32 	%f507, %f280, %f280, %f507;
	add.s32 	%r197, %r195, 2;

$L__BB0_38:
	@%p20 bra 	$L__BB0_40;

	ld.global.f32 	%f281, [%rd12+8];
	add.f32 	%f504, %f504, %f281;
	ld.global.f32 	%f282, [%rd13+8];
	add.f32 	%f503, %f503, %f282;
	ld.global.f32 	%f283, [%rd14+8];
	add.f32 	%f502, %f502, %f283;
	fma.rn.f32 	%f505, %f281, %f281, %f505;
	fma.rn.f32 	%f506, %f282, %f282, %f506;
	fma.rn.f32 	%f507, %f283, %f283, %f507;
	add.s32 	%r197, %r197, 1;

$L__BB0_40:
	add.f32 	%f516, %f504, %f43;
	add.f32 	%f515, %f503, %f45;
	add.f32 	%f514, %f502, %f47;
	fma.rn.f32 	%f517, %f43, %f43, %f505;
	fma.rn.f32 	%f518, %f45, %f45, %f506;
	fma.rn.f32 	%f519, %f47, %f47, %f507;
	add.s32 	%r199, %r197, 1;
	@%p26 bra 	$L__BB0_42;

	ld.global.f32 	%f284, [%rd15+4];
	add.f32 	%f516, %f516, %f284;
	ld.global.f32 	%f285, [%rd16+4];
	add.f32 	%f515, %f515, %f285;
	ld.global.f32 	%f286, [%rd17+4];
	add.f32 	%f514, %f514, %f286;
	fma.rn.f32 	%f517, %f284, %f284, %f517;
	fma.rn.f32 	%f518, %f285, %f285, %f518;
	fma.rn.f32 	%f519, %f286, %f286, %f519;
	add.s32 	%r199, %r197, 2;

$L__BB0_42:
	@%p26 bra 	$L__BB0_44;

	ld.global.f32 	%f287, [%rd15+8];
	add.f32 	%f516, %f516, %f287;
	ld.global.f32 	%f288, [%rd16+8];
	add.f32 	%f515, %f515, %f288;
	ld.global.f32 	%f289, [%rd17+8];
	add.f32 	%f514, %f514, %f289;
	fma.rn.f32 	%f517, %f287, %f287, %f517;
	fma.rn.f32 	%f518, %f288, %f288, %f518;
	fma.rn.f32 	%f519, %f289, %f289, %f519;
	add.s32 	%r199, %r199, 1;

$L__BB0_44:
	setp.lt.s32 	%p42, %r199, 2;
	mov.f32 	%f290, 0f3F800000;
	mov.f32 	%f520, %f290;
	@%p42 bra 	$L__BB0_46;

	cvt.rn.f32.s32 	%f291, %r199;
	div.rn.f32 	%f292, %f516, %f291;
	div.rn.f32 	%f293, %f515, %f291;
	div.rn.f32 	%f294, %f514, %f291;
	div.rn.f32 	%f295, %f517, %f291;
	mul.f32 	%f296, %f292, %f292;
	sub.f32 	%f297, %f295, %f296;
	div.rn.f32 	%f298, %f518, %f291;
	mul.f32 	%f299, %f293, %f293;
	sub.f32 	%f300, %f298, %f299;
	div.rn.f32 	%f301, %f519, %f291;
	mul.f32 	%f302, %f294, %f294;
	sub.f32 	%f303, %f301, %f302;
	add.f32 	%f304, %f297, %f300;
	add.f32 	%f305, %f303, %f304;
	div.rn.f32 	%f520, %f305, 0f40400000;

$L__BB0_46:
	sub.f32 	%f307, %f290, %f520;
	mov.f32 	%f308, 0f3C23D70A;
	max.f32 	%f552, %f308, %f307;
	setp.lt.s32 	%p43, %r204, 0;
	@%p43 bra 	$L__BB0_69;

	add.s32 	%r200, %r204, 1;
	setp.le.s32 	%p44, %r187, %r200;
	@%p44 bra 	$L__BB0_69;

	sub.s32 	%r123, %r187, %r204;
	cvt.rn.f32.s32 	%f164, %r123;
	sub.f32 	%f165, %f549, %f6;
	sub.f32 	%f166, %f550, %f5;
	sub.f32 	%f167, %f551, %f4;
	add.f32 	%f309, %f3, %f552;
	mul.f32 	%f168, %f309, 0f3F000000;
	div.rn.f32 	%f310, %f164, %f2;
	mov.f32 	%f311, 0f3F800000;
	sub.f32 	%f169, %f311, %f310;
	not.b32 	%r124, %r204;
	add.s32 	%r125, %r187, %r124;
	and.b32  	%r39, %r125, 3;
	setp.eq.s32 	%p45, %r39, 0;
	@%p45 bra 	$L__BB0_58;

	setp.eq.s32 	%p46, %r187, %r204;
	mov.f32 	%f521, %f6;
	mov.f32 	%f522, %f5;
	mov.f32 	%f523, %f4;
	mov.f32 	%f524, %f3;
	@%p46 bra 	$L__BB0_51;

	rcp.rn.f32 	%f312, %f164;
	mov.f32 	%f313, 0f3F800000;
	sub.s32 	%r126, %r187, %r200;
	min.s32 	%r127, %r126, 1;
	cvt.rn.f32.s32 	%f314, %r127;
	div.rn.f32 	%f315, %f314, %f1;
	sub.f32 	%f316, %f313, %f315;
	mov.f32 	%f317, 0f3DCCCCCD;
	max.f32 	%f318, %f317, %f316;
	mul.f32 	%f319, %f168, %f318;
	max.f32 	%f320, %f317, %f169;
	fma.rn.f32 	%f523, %f167, %f312, %f4;
	fma.rn.f32 	%f522, %f166, %f312, %f5;
	fma.rn.f32 	%f521, %f165, %f312, %f6;
	mul.f32 	%f524, %f319, %f320;

$L__BB0_51:
	add.s32 	%r128, %r200, %r3;
	mul.wide.s32 	%rd51, %r128, 16;
	add.s64 	%rd18, %rd3, %rd51;
	st.global.f32 	[%rd18], %f521;
	st.global.f32 	[%rd18+4], %f522;
	st.global.f32 	[%rd18+8], %f523;
	st.global.f32 	[%rd18+12], %f524;
	add.s32 	%r200, %r204, 2;
	setp.eq.s32 	%p47, %r39, 1;
	@%p47 bra 	$L__BB0_58;

	mov.f32 	%f525, %f6;
	mov.f32 	%f526, %f5;
	mov.f32 	%f527, %f4;
	mov.f32 	%f528, %f3;
	@%p46 bra 	$L__BB0_54;

	mov.f32 	%f321, 0f40000000;
	div.rn.f32 	%f322, %f321, %f164;
	sub.s32 	%r129, %r187, %r200;
	min.s32 	%r130, %r129, 2;
	cvt.rn.f32.s32 	%f323, %r130;
	div.rn.f32 	%f324, %f323, %f1;
	mov.f32 	%f325, 0f3F800000;
	sub.f32 	%f326, %f325, %f324;
	mov.f32 	%f327, 0f3DCCCCCD;
	max.f32 	%f328, %f327, %f326;
	mul.f32 	%f329, %f168, %f328;
	max.f32 	%f330, %f327, %f169;
	mul.f32 	%f528, %f329, %f330;
	fma.rn.f32 	%f527, %f167, %f322, %f4;
	fma.rn.f32 	%f526, %f166, %f322, %f5;
	fma.rn.f32 	%f525, %f165, %f322, %f6;

$L__BB0_54:
	st.global.f32 	[%rd18+16], %f525;
	st.global.f32 	[%rd18+20], %f526;
	st.global.f32 	[%rd18+24], %f527;
	st.global.f32 	[%rd18+28], %f528;
	add.s32 	%r200, %r204, 3;
	setp.eq.s32 	%p49, %r39, 2;
	@%p49 bra 	$L__BB0_58;

	mov.f32 	%f529, %f6;
	mov.f32 	%f530, %f5;
	mov.f32 	%f531, %f4;
	mov.f32 	%f532, %f3;
	@%p46 bra 	$L__BB0_57;

	mov.f32 	%f331, 0f40400000;
	div.rn.f32 	%f332, %f331, %f164;
	sub.s32 	%r131, %r187, %r200;
	min.s32 	%r132, %r131, 3;
	cvt.rn.f32.s32 	%f333, %r132;
	div.rn.f32 	%f334, %f333, %f1;
	mov.f32 	%f335, 0f3F800000;
	sub.f32 	%f336, %f335, %f334;
	mov.f32 	%f337, 0f3DCCCCCD;
	max.f32 	%f338, %f337, %f336;
	mul.f32 	%f339, %f168, %f338;
	max.f32 	%f340, %f337, %f169;
	mul.f32 	%f532, %f339, %f340;
	fma.rn.f32 	%f531, %f167, %f332, %f4;
	fma.rn.f32 	%f530, %f166, %f332, %f5;
	fma.rn.f32 	%f529, %f165, %f332, %f6;

$L__BB0_57:
	st.global.f32 	[%rd18+32], %f529;
	st.global.f32 	[%rd18+36], %f530;
	st.global.f32 	[%rd18+40], %f531;
	st.global.f32 	[%rd18+44], %f532;
	add.s32 	%r200, %r204, 4;

$L__BB0_58:
	add.s32 	%r133, %r187, -2;
	sub.s32 	%r134, %r133, %r204;
	setp.lt.u32 	%p51, %r134, 3;
	@%p51 bra 	$L__BB0_69;

	add.s32 	%r135, %r200, 3;
	sub.s32 	%r202, %r135, %r204;
	add.s32 	%r136, %r3, %r200;
	sub.s32 	%r201, %r187, %r200;
	mul.wide.s32 	%rd52, %r136, 16;
	add.s64 	%rd70, %rd3, %rd52;

$L__BB0_60:
	setp.eq.s32 	%p52, %r187, %r204;
	mov.f32 	%f533, %f6;
	mov.f32 	%f534, %f5;
	mov.f32 	%f535, %f4;
	mov.f32 	%f536, %f3;
	@%p52 bra 	$L__BB0_62;

	add.s32 	%r137, %r202, -3;
	cvt.rn.f32.s32 	%f341, %r137;
	div.rn.f32 	%f342, %f341, %f164;
	min.s32 	%r138, %r137, %r201;
	cvt.rn.f32.s32 	%f343, %r138;
	div.rn.f32 	%f344, %f343, %f1;
	mov.f32 	%f345, 0f3F800000;
	sub.f32 	%f346, %f345, %f344;
	mov.f32 	%f347, 0f3DCCCCCD;
	max.f32 	%f348, %f347, %f346;
	mul.f32 	%f349, %f168, %f348;
	max.f32 	%f350, %f347, %f169;
	mul.f32 	%f536, %f349, %f350;
	fma.rn.f32 	%f535, %f167, %f342, %f4;
	fma.rn.f32 	%f534, %f166, %f342, %f5;
	fma.rn.f32 	%f533, %f165, %f342, %f6;

$L__BB0_62:
	st.global.f32 	[%rd70], %f533;
	st.global.f32 	[%rd70+4], %f534;
	st.global.f32 	[%rd70+8], %f535;
	st.global.f32 	[%rd70+12], %f536;
	mov.f32 	%f537, %f6;
	mov.f32 	%f538, %f5;
	mov.f32 	%f539, %f4;
	mov.f32 	%f540, %f3;
	@%p52 bra 	$L__BB0_64;

	add.s32 	%r139, %r202, -2;
	cvt.rn.f32.s32 	%f351, %r139;
	div.rn.f32 	%f352, %f351, %f164;
	add.s32 	%r140, %r201, -1;
	min.s32 	%r141, %r139, %r140;
	cvt.rn.f32.s32 	%f353, %r141;
	div.rn.f32 	%f354, %f353, %f1;
	mov.f32 	%f355, 0f3F800000;
	sub.f32 	%f356, %f355, %f354;
	mov.f32 	%f357, 0f3DCCCCCD;
	max.f32 	%f358, %f357, %f356;
	mul.f32 	%f359, %f168, %f358;
	max.f32 	%f360, %f357, %f169;
	mul.f32 	%f540, %f359, %f360;
	fma.rn.f32 	%f539, %f167, %f352, %f4;
	fma.rn.f32 	%f538, %f166, %f352, %f5;
	fma.rn.f32 	%f537, %f165, %f352, %f6;

$L__BB0_64:
	st.global.f32 	[%rd70+16], %f537;
	st.global.f32 	[%rd70+20], %f538;
	st.global.f32 	[%rd70+24], %f539;
	st.global.f32 	[%rd70+28], %f540;
	mov.f32 	%f541, %f6;
	mov.f32 	%f542, %f5;
	mov.f32 	%f543, %f4;
	mov.f32 	%f544, %f3;
	@%p52 bra 	$L__BB0_66;

	add.s32 	%r142, %r202, -1;
	cvt.rn.f32.s32 	%f361, %r142;
	div.rn.f32 	%f362, %f361, %f164;
	add.s32 	%r143, %r201, -2;
	min.s32 	%r144, %r142, %r143;
	cvt.rn.f32.s32 	%f363, %r144;
	div.rn.f32 	%f364, %f363, %f1;
	mov.f32 	%f365, 0f3F800000;
	sub.f32 	%f366, %f365, %f364;
	mov.f32 	%f367, 0f3DCCCCCD;
	max.f32 	%f368, %f367, %f366;
	mul.f32 	%f369, %f168, %f368;
	max.f32 	%f370, %f367, %f169;
	mul.f32 	%f544, %f369, %f370;
	fma.rn.f32 	%f543, %f167, %f362, %f4;
	fma.rn.f32 	%f542, %f166, %f362, %f5;
	fma.rn.f32 	%f541, %f165, %f362, %f6;

$L__BB0_66:
	st.global.f32 	[%rd70+32], %f541;
	st.global.f32 	[%rd70+36], %f542;
	st.global.f32 	[%rd70+40], %f543;
	st.global.f32 	[%rd70+44], %f544;
	mov.f32 	%f545, %f6;
	mov.f32 	%f546, %f5;
	mov.f32 	%f547, %f4;
	mov.f32 	%f548, %f3;
	@%p52 bra 	$L__BB0_68;

	cvt.rn.f32.s32 	%f371, %r202;
	div.rn.f32 	%f372, %f371, %f164;
	add.s32 	%r145, %r201, -3;
	min.s32 	%r146, %r202, %r145;
	cvt.rn.f32.s32 	%f373, %r146;
	div.rn.f32 	%f374, %f373, %f1;
	mov.f32 	%f375, 0f3F800000;
	sub.f32 	%f376, %f375, %f374;
	mov.f32 	%f377, 0f3DCCCCCD;
	max.f32 	%f378, %f377, %f376;
	mul.f32 	%f379, %f168, %f378;
	max.f32 	%f380, %f377, %f169;
	mul.f32 	%f548, %f379, %f380;
	fma.rn.f32 	%f547, %f167, %f372, %f4;
	fma.rn.f32 	%f546, %f166, %f372, %f5;
	fma.rn.f32 	%f545, %f165, %f372, %f6;

$L__BB0_68:
	st.global.f32 	[%rd70+48], %f545;
	st.global.f32 	[%rd70+52], %f546;
	st.global.f32 	[%rd70+56], %f547;
	add.s64 	%rd21, %rd70, 64;
	st.global.f32 	[%rd70+60], %f548;
	add.s32 	%r202, %r202, 4;
	add.s32 	%r201, %r201, -4;
	add.s32 	%r200, %r200, 4;
	setp.gt.s32 	%p56, %r187, %r200;
	mov.u64 	%rd70, %rd21;
	@%p56 bra 	$L__BB0_60;

$L__BB0_69:
	add.s32 	%r182, %r187, %r3;
	cvt.s64.s32 	%rd67, %r182;
	shl.b64 	%rd53, %rd67, 4;
	add.s64 	%rd54, %rd3, %rd53;
	st.global.f32 	[%rd54], %f549;
	st.global.f32 	[%rd54+4], %f550;
	st.global.f32 	[%rd54+8], %f551;
	st.global.f32 	[%rd54+12], %f552;
	mov.u16 	%rs9, 1;
	mov.u32 	%r204, %r187;

$L__BB0_70:
	add.s32 	%r187, %r187, 1;
	setp.lt.s32 	%p57, %r187, %r94;
	@%p57 bra 	$L__BB0_3;

$L__BB0_71:
	setp.gt.s32 	%p58, %r204, -1;
	and.b16  	%rs7, %rs9, 255;
	setp.ne.s16 	%p59, %rs7, 0;
	and.pred  	%p60, %p59, %p58;
	@%p60 bra 	$L__BB0_79;
	bra.uni 	$L__BB0_72;

$L__BB0_79:
	add.s32 	%r213, %r204, 1;
	setp.ge.s32 	%p66, %r213, %r94;
	@%p66 bra 	$L__BB0_86;

	max.s32 	%r184, %r94, %r95;
	cvt.rn.f32.s32 	%f234, %r184;
	not.b32 	%r156, %r204;
	add.s32 	%r157, %r156, %r94;
	and.b32  	%r212, %r157, 3;
	setp.eq.s32 	%p67, %r212, 0;
	@%p67 bra 	$L__BB0_83;

	mad.lo.s32 	%r159, %r94, %r1, %r204;
	add.s32 	%r160, %r159, 1;
	mov.u32 	%r210, 1;
	mul.wide.s32 	%rd59, %r160, 16;
	add.s64 	%rd60, %rd3, %rd59;
	add.s64 	%rd72, %rd60, 8;

$L__BB0_82:
	.pragma "nounroll";
	cvt.rn.f32.s32 	%f381, %r210;
	div.rn.f32 	%f382, %f381, %f234;
	mov.f32 	%f383, 0f3F800000;
	sub.f32 	%f384, %f383, %f382;
	mov.f32 	%f385, 0f3D4CCCCD;
	max.f32 	%f386, %f385, %f384;
	mul.f32 	%f387, %f552, %f386;
	st.global.f32 	[%rd72+-8], %f549;
	st.global.f32 	[%rd72+-4], %f550;
	st.global.f32 	[%rd72], %f551;
	st.global.f32 	[%rd72+4], %f387;
	add.s32 	%r213, %r213, 1;
	add.s32 	%r210, %r210, 1;
	add.s64 	%rd72, %rd72, 16;
	add.s32 	%r212, %r212, -1;
	setp.ne.s32 	%p68, %r212, 0;
	@%p68 bra 	$L__BB0_82;

$L__BB0_83:
	add.s32 	%r161, %r94, -2;
	sub.s32 	%r162, %r161, %r204;
	setp.lt.u32 	%p69, %r162, 3;
	@%p69 bra 	$L__BB0_86;

	add.s32 	%r163, %r213, 3;
	sub.s32 	%r214, %r163, %r204;
	mad.lo.s32 	%r164, %r94, %r1, %r213;
	mul.wide.s32 	%rd61, %r164, 16;
	add.s64 	%rd62, %rd3, %rd61;
	add.s64 	%rd73, %rd62, 8;
	neg.s32 	%r75, %r204;

$L__BB0_85:
	add.s32 	%r165, %r75, %r213;
	cvt.rn.f32.s32 	%f388, %r165;
	div.rn.f32 	%f389, %f388, %f234;
	mov.f32 	%f390, 0f3F800000;
	sub.f32 	%f391, %f390, %f389;
	mov.f32 	%f392, 0f3D4CCCCD;
	max.f32 	%f393, %f392, %f391;
	mul.f32 	%f394, %f552, %f393;
	st.global.f32 	[%rd73+-8], %f549;
	st.global.f32 	[%rd73+-4], %f550;
	st.global.f32 	[%rd73], %f551;
	st.global.f32 	[%rd73+4], %f394;
	add.s32 	%r166, %r214, -2;
	cvt.rn.f32.s32 	%f395, %r166;
	div.rn.f32 	%f396, %f395, %f234;
	sub.f32 	%f397, %f390, %f396;
	max.f32 	%f398, %f392, %f397;
	mul.f32 	%f399, %f552, %f398;
	st.global.f32 	[%rd73+8], %f549;
	st.global.f32 	[%rd73+12], %f550;
	st.global.f32 	[%rd73+16], %f551;
	st.global.f32 	[%rd73+20], %f399;
	add.s32 	%r167, %r214, -1;
	cvt.rn.f32.s32 	%f400, %r167;
	div.rn.f32 	%f401, %f400, %f234;
	sub.f32 	%f402, %f390, %f401;
	max.f32 	%f403, %f392, %f402;
	mul.f32 	%f404, %f552, %f403;
	st.global.f32 	[%rd73+24], %f549;
	st.global.f32 	[%rd73+28], %f550;
	st.global.f32 	[%rd73+32], %f551;
	st.global.f32 	[%rd73+36], %f404;
	cvt.rn.f32.s32 	%f405, %r214;
	div.rn.f32 	%f406, %f405, %f234;
	sub.f32 	%f407, %f390, %f406;
	max.f32 	%f408, %f392, %f407;
	mul.f32 	%f409, %f552, %f408;
	st.global.f32 	[%rd73+40], %f549;
	st.global.f32 	[%rd73+44], %f550;
	st.global.f32 	[%rd73+48], %f551;
	st.global.f32 	[%rd73+52], %f409;
	add.s32 	%r214, %r214, 4;
	add.s64 	%rd73, %rd73, 64;
	add.s32 	%r213, %r213, 4;
	setp.lt.s32 	%p70, %r213, %r94;
	@%p70 bra 	$L__BB0_85;

$L__BB0_86:
	setp.lt.s32 	%p84, %r94, 1;
	setp.lt.s32 	%p72, %r204, 1;
	or.pred  	%p73, %p72, %p84;
	@%p73 bra 	$L__BB0_103;

	max.s32 	%r183, %r94, %r95;
	mul.lo.s32 	%r80, %r1, %r94;
	cvt.rn.f32.s32 	%f235, %r183;
	neg.s32 	%r169, %r94;
	mov.u32 	%r218, 0;
	neg.s32 	%r170, %r204;
	max.u32 	%r171, %r170, %r169;
	neg.s32 	%r81, %r171;
	and.b32  	%r220, %r81, 3;
	setp.gt.u32 	%p74, %r171, -4;
	@%p74 bra 	$L__BB0_98;

	sub.s32 	%r217, %r81, %r220;
	mov.u32 	%r218, 0;

$L__BB0_89:
	add.s32 	%r173, %r218, %r80;
	mul.wide.s32 	%rd63, %r173, 16;
	add.s64 	%rd64, %rd3, %rd63;
	add.s64 	%rd31, %rd64, 12;
	ld.global.f32 	%f410, [%rd64+12];
	setp.neu.f32 	%p75, %f410, 0f00000000;
	@%p75 bra 	$L__BB0_91;

	sub.s32 	%r174, %r204, %r218;
	cvt.rn.f32.s32 	%f411, %r174;
	div.rn.f32 	%f412, %f411, %f235;
	mov.f32 	%f413, 0f3F800000;
	sub.f32 	%f414, %f413, %f412;
	mov.f32 	%f415, 0f3D4CCCCD;
	max.f32 	%f416, %f415, %f414;
	mul.f32 	%f417, %f552, %f416;
	st.global.f32 	[%rd31+-12], %f549;
	st.global.f32 	[%rd31+-8], %f550;
	st.global.f32 	[%rd31+-4], %f551;
	st.global.f32 	[%rd31], %f417;

$L__BB0_91:
	ld.global.f32 	%f418, [%rd31+16];
	setp.neu.f32 	%p76, %f418, 0f00000000;
	@%p76 bra 	$L__BB0_93;

	add.s32 	%r175, %r218, 1;
	sub.s32 	%r176, %r204, %r175;
	cvt.rn.f32.s32 	%f419, %r176;
	div.rn.f32 	%f420, %f419, %f235;
	mov.f32 	%f421, 0f3F800000;
	sub.f32 	%f422, %f421, %f420;
	mov.f32 	%f423, 0f3D4CCCCD;
	max.f32 	%f424, %f423, %f422;
	mul.f32 	%f425, %f552, %f424;
	st.global.f32 	[%rd31+4], %f549;
	st.global.f32 	[%rd31+8], %f550;
	st.global.f32 	[%rd31+12], %f551;
	st.global.f32 	[%rd31+16], %f425;

$L__BB0_93:
	ld.global.f32 	%f426, [%rd31+32];
	setp.neu.f32 	%p77, %f426, 0f00000000;
	@%p77 bra 	$L__BB0_95;

	add.s32 	%r177, %r218, 2;
	sub.s32 	%r178, %r204, %r177;
	cvt.rn.f32.s32 	%f427, %r178;
	div.rn.f32 	%f428, %f427, %f235;
	mov.f32 	%f429, 0f3F800000;
	sub.f32 	%f430, %f429, %f428;
	mov.f32 	%f431, 0f3D4CCCCD;
	max.f32 	%f432, %f431, %f430;
	mul.f32 	%f433, %f552, %f432;
	st.global.f32 	[%rd31+20], %f549;
	st.global.f32 	[%rd31+24], %f550;
	st.global.f32 	[%rd31+28], %f551;
	st.global.f32 	[%rd31+32], %f433;

$L__BB0_95:
	ld.global.f32 	%f434, [%rd31+48];
	setp.neu.f32 	%p78, %f434, 0f00000000;
	@%p78 bra 	$L__BB0_97;

	add.s32 	%r179, %r218, 3;
	sub.s32 	%r180, %r204, %r179;
	cvt.rn.f32.s32 	%f435, %r180;
	div.rn.f32 	%f436, %f435, %f235;
	mov.f32 	%f437, 0f3F800000;
	sub.f32 	%f438, %f437, %f436;
	mov.f32 	%f439, 0f3D4CCCCD;
	max.f32 	%f440, %f439, %f438;
	mul.f32 	%f441, %f552, %f440;
	st.global.f32 	[%rd31+36], %f549;
	st.global.f32 	[%rd31+40], %f550;
	st.global.f32 	[%rd31+44], %f551;
	st.global.f32 	[%rd31+48], %f441;

$L__BB0_97:
	add.s32 	%r218, %r218, 4;
	add.s32 	%r217, %r217, -4;
	setp.ne.s32 	%p79, %r217, 0;
	@%p79 bra 	$L__BB0_89;

$L__BB0_98:
	setp.eq.s32 	%p80, %r220, 0;
	@%p80 bra 	$L__BB0_103;

	sub.s32 	%r219, %r204, %r218;
	add.s32 	%r181, %r218, %r80;
	mul.wide.s32 	%rd65, %r181, 16;
	add.s64 	%rd66, %rd3, %rd65;
	add.s64 	%rd74, %rd66, 12;

$L__BB0_100:
	.pragma "nounroll";
	ld.global.f32 	%f442, [%rd74];
	setp.neu.f32 	%p81, %f442, 0f00000000;
	@%p81 bra 	$L__BB0_102;

	cvt.rn.f32.s32 	%f443, %r219;
	div.rn.f32 	%f444, %f443, %f235;
	mov.f32 	%f445, 0f3F800000;
	sub.f32 	%f446, %f445, %f444;
	mov.f32 	%f447, 0f3D4CCCCD;
	max.f32 	%f448, %f447, %f446;
	mul.f32 	%f449, %f552, %f448;
	st.global.f32 	[%rd74+-12], %f549;
	st.global.f32 	[%rd74+-8], %f550;
	st.global.f32 	[%rd74+-4], %f551;
	st.global.f32 	[%rd74], %f449;

$L__BB0_102:
	add.s32 	%r220, %r220, -1;
	add.s32 	%r219, %r219, -1;
	add.s64 	%rd74, %rd74, 16;
	setp.ne.s32 	%p82, %r220, 0;
	@%p82 bra 	$L__BB0_100;
	bra.uni 	$L__BB0_103;

$L__BB0_72:
	setp.lt.s32 	%p83, %r94, 1;
	@%p83 bra 	$L__BB0_103;

	mul.lo.s32 	%r55, %r1, %r94;
	and.b32  	%r209, %r94, 3;
	add.s32 	%r148, %r94, -1;
	setp.lt.u32 	%p62, %r148, 3;
	mov.u32 	%r208, 0;
	@%p62 bra 	$L__BB0_76;

	sub.s32 	%r207, %r94, %r209;
	mov.u32 	%r149, 0;
	mov.u32 	%r208, %r149;

$L__BB0_75:
	add.s32 	%r150, %r208, %r55;
	mul.wide.s32 	%rd55, %r150, 16;
	add.s64 	%rd56, %rd3, %rd55;
	st.global.u32 	[%rd56], %r149;
	st.global.u32 	[%rd56+4], %r149;
	st.global.u32 	[%rd56+8], %r149;
	mov.u32 	%r152, 1008981770;
	st.global.u32 	[%rd56+12], %r152;
	st.global.u32 	[%rd56+16], %r149;
	st.global.u32 	[%rd56+20], %r149;
	st.global.u32 	[%rd56+24], %r149;
	st.global.u32 	[%rd56+28], %r152;
	st.global.u32 	[%rd56+32], %r149;
	st.global.u32 	[%rd56+36], %r149;
	st.global.u32 	[%rd56+40], %r149;
	st.global.u32 	[%rd56+44], %r152;
	st.global.u32 	[%rd56+48], %r149;
	st.global.u32 	[%rd56+52], %r149;
	st.global.u32 	[%rd56+56], %r149;
	st.global.u32 	[%rd56+60], %r152;
	add.s32 	%r208, %r208, 4;
	add.s32 	%r207, %r207, -4;
	setp.ne.s32 	%p63, %r207, 0;
	@%p63 bra 	$L__BB0_75;

$L__BB0_76:
	setp.eq.s32 	%p64, %r209, 0;
	@%p64 bra 	$L__BB0_103;

	add.s32 	%r153, %r208, %r55;
	mul.wide.s32 	%rd57, %r153, 16;
	add.s64 	%rd58, %rd3, %rd57;
	add.s64 	%rd71, %rd58, 8;

$L__BB0_78:
	.pragma "nounroll";
	mov.u32 	%r154, 0;
	st.global.u32 	[%rd71+-8], %r154;
	st.global.u32 	[%rd71+-4], %r154;
	st.global.u32 	[%rd71], %r154;
	mov.u32 	%r155, 1008981770;
	st.global.u32 	[%rd71+4], %r155;
	add.s64 	%rd71, %rd71, 16;
	add.s32 	%r209, %r209, -1;
	setp.eq.s32 	%p65, %r209, 0;
	@%p65 bra 	$L__BB0_103;
	bra.uni 	$L__BB0_78;

$L__BB0_103:
	ret;

}
	// .globl	_Z32estimateBackgroundVerticalKernelPKfS0_P14BackgroundDataii
.visible .entry _Z32estimateBackgroundVerticalKernelPKfS0_P14BackgroundDataii(
	.param .u64 _Z32estimateBackgroundVerticalKernelPKfS0_P14BackgroundDataii_param_0,
	.param .u64 _Z32estimateBackgroundVerticalKernelPKfS0_P14BackgroundDataii_param_1,
	.param .u64 _Z32estimateBackgroundVerticalKernelPKfS0_P14BackgroundDataii_param_2,
	.param .u32 _Z32estimateBackgroundVerticalKernelPKfS0_P14BackgroundDataii_param_3,
	.param .u32 _Z32estimateBackgroundVerticalKernelPKfS0_P14BackgroundDataii_param_4
)
{
	.reg .pred 	%p<85>;
	.reg .b16 	%rs<11>;
	.reg .f32 	%f<546>;
	.reg .b32 	%r<230>;
	.reg .b64 	%rd<100>;


	ld.param.u64 	%rd47, [_Z32estimateBackgroundVerticalKernelPKfS0_P14BackgroundDataii_param_0];
	ld.param.u64 	%rd48, [_Z32estimateBackgroundVerticalKernelPKfS0_P14BackgroundDataii_param_1];
	ld.param.u64 	%rd49, [_Z32estimateBackgroundVerticalKernelPKfS0_P14BackgroundDataii_param_2];
	ld.param.u32 	%r92, [_Z32estimateBackgroundVerticalKernelPKfS0_P14BackgroundDataii_param_3];
	ld.param.u32 	%r93, [_Z32estimateBackgroundVerticalKernelPKfS0_P14BackgroundDataii_param_4];
	cvta.to.global.u64 	%rd1, %rd48;
	cvta.to.global.u64 	%rd2, %rd47;
	cvta.to.global.u64 	%rd3, %rd49;
	mov.u32 	%r94, %ntid.x;
	mov.u32 	%r95, %ctaid.x;
	mov.u32 	%r96, %tid.x;
	mad.lo.s32 	%r1, %r95, %r94, %r96;
	setp.ge.s32 	%p1, %r1, %r92;
	@%p1 bra 	$L__BB1_103;

	max.s32 	%r2, %r92, %r93;
	setp.lt.s32 	%p2, %r93, 1;
	mov.u32 	%r213, -1;
	mov.u16 	%rs9, 0;
	mov.f32 	%f538, 0f00000000;
	mov.f32 	%f539, %f538;
	mov.f32 	%f540, %f538;
	mov.f32 	%f541, %f538;
	@%p2 bra 	$L__BB1_71;

	mul.lo.s32 	%r3, %r93, %r92;
	cvt.rn.f32.s32 	%f1, %r2;
	add.f32 	%f2, %f1, %f1;
	add.s32 	%r4, %r1, -1;
	mov.u32 	%r213, -1;
	add.s32 	%r5, %r1, 1;
	shl.b32 	%r6, %r92, 2;
	mul.wide.s32 	%rd4, %r92, 16;
	mov.f32 	%f541, 0f00000000;
	mov.u32 	%r195, 0;
	mov.u16 	%rs9, 0;
	mov.f32 	%f540, %f541;
	mov.f32 	%f539, %f541;
	mov.f32 	%f538, %f541;

$L__BB1_3:
	mov.f32 	%f6, %f538;
	mov.f32 	%f5, %f539;
	mov.f32 	%f4, %f540;
	mul.lo.s32 	%r9, %r195, %r92;
	add.s32 	%r100, %r9, %r1;
	cvt.s64.s32 	%rd5, %r100;
	mul.wide.s32 	%rd50, %r100, 4;
	add.s64 	%rd6, %rd2, %rd50;
	ld.global.f32 	%f226, [%rd6];
	setp.gt.f32 	%p3, %f226, 0f3CA0A0A1;
	@%p3 bra 	$L__BB1_70;

	setp.gt.s32 	%p4, %r1, %r92;
	sub.s32 	%r10, %r9, %r92;
	add.s32 	%r11, %r195, -1;
	or.b32  	%r101, %r4, %r11;
	setp.lt.s32 	%p5, %r101, 0;
	or.pred  	%p6, %p4, %p5;
	setp.gt.s32 	%p7, %r195, %r93;
	or.pred  	%p8, %p7, %p6;
	mov.f32 	%f538, %f6;
	mov.f32 	%f539, %f5;
	mov.f32 	%f540, %f4;
	@%p8 bra 	$L__BB1_70;

	add.s32 	%r102, %r1, %r10;
	mul.wide.s32 	%rd51, %r102, 4;
	add.s64 	%rd7, %rd2, %rd51;
	ld.global.f32 	%f227, [%rd7+-4];
	setp.gt.f32 	%p9, %f227, 0f3CA0A0A1;
	or.b32  	%r103, %r1, %r11;
	setp.lt.s32 	%p10, %r103, 0;
	or.pred  	%p11, %p9, %p10;
	mov.f32 	%f538, %f6;
	mov.f32 	%f539, %f5;
	mov.f32 	%f540, %f4;
	@%p11 bra 	$L__BB1_70;

	ld.global.f32 	%f228, [%rd7];
	setp.gt.f32 	%p12, %f228, 0f3CA0A0A1;
	mov.f32 	%f538, %f6;
	mov.f32 	%f539, %f5;
	mov.f32 	%f540, %f4;
	@%p12 bra 	$L__BB1_70;

	setp.ge.s32 	%p13, %r5, %r92;
	or.b32  	%r104, %r5, %r11;
	setp.lt.s32 	%p14, %r104, 0;
	or.pred  	%p15, %p13, %p14;
	mov.f32 	%f538, %f6;
	mov.f32 	%f539, %f5;
	mov.f32 	%f540, %f4;
	@%p15 bra 	$L__BB1_70;

	setp.lt.s32 	%p16, %r1, 1;
	ld.global.f32 	%f229, [%rd7+4];
	setp.gt.f32 	%p17, %f229, 0f3CA0A0A1;
	setp.ge.s32 	%p18, %r195, %r93;
	or.pred  	%p19, %p18, %p16;
	or.pred  	%p20, %p17, %p19;
	mov.f32 	%f538, %f6;
	mov.f32 	%f539, %f5;
	mov.f32 	%f540, %f4;
	@%p20 bra 	$L__BB1_70;

	ld.global.f32 	%f230, [%rd6+-4];
	setp.gt.f32 	%p21, %f230, 0f3CA0A0A1;
	mov.f32 	%f538, %f6;
	mov.f32 	%f539, %f5;
	mov.f32 	%f540, %f4;
	@%p21 bra 	$L__BB1_70;

	setp.lt.s32 	%p22, %r1, -1;
	mov.f32 	%f538, %f6;
	mov.f32 	%f539, %f5;
	mov.f32 	%f540, %f4;
	@%p22 bra 	$L__BB1_70;

	ld.global.f32 	%f231, [%rd6+4];
	setp.gt.f32 	%p23, %f231, 0f3CA0A0A1;
	mov.f32 	%f538, %f6;
	mov.f32 	%f539, %f5;
	mov.f32 	%f540, %f4;
	@%p23 bra 	$L__BB1_70;

	add.s32 	%r105, %r195, 1;
	setp.ge.s32 	%p24, %r105, %r93;
	shl.b32 	%r106, %r92, 1;
	add.s32 	%r12, %r10, %r106;
	mov.f32 	%f538, %f6;
	mov.f32 	%f539, %f5;
	mov.f32 	%f540, %f4;
	@%p24 bra 	$L__BB1_70;

	mul.lo.s32 	%r193, %r195, %r92;
	shl.b32 	%r192, %r92, 1;
	sub.s32 	%r191, %r193, %r92;
	add.s32 	%r190, %r191, %r192;
	add.s32 	%r107, %r1, %r190;
	mul.wide.s32 	%rd52, %r107, 4;
	add.s64 	%rd8, %rd2, %rd52;
	ld.global.f32 	%f232, [%rd8+-4];
	setp.gt.f32 	%p25, %f232, 0f3CA0A0A1;
	mov.f32 	%f538, %f6;
	mov.f32 	%f539, %f5;
	mov.f32 	%f540, %f4;
	@%p25 bra 	$L__BB1_70;

	setp.lt.s32 	%p26, %r1, 0;
	mov.f32 	%f538, %f6;
	mov.f32 	%f539, %f5;
	mov.f32 	%f540, %f4;
	@%p26 bra 	$L__BB1_70;

	ld.global.f32 	%f233, [%rd8];
	setp.gt.f32 	%p27, %f233, 0f3CA0A0A1;
	mov.f32 	%f538, %f6;
	mov.f32 	%f539, %f5;
	mov.f32 	%f540, %f4;
	@%p27 bra 	$L__BB1_70;

	ld.global.f32 	%f234, [%rd8+4];
	setp.gt.f32 	%p28, %f234, 0f3CA0A0A1;
	mov.f32 	%f538, %f6;
	mov.f32 	%f539, %f5;
	mov.f32 	%f540, %f4;
	@%p28 bra 	$L__BB1_70;

	add.s32 	%r109, %r4, %r10;
	mul.wide.s32 	%rd53, %r109, 4;
	add.s64 	%rd54, %rd1, %rd53;
	ld.global.f32 	%f7, [%rd54];
	add.f32 	%f235, %f7, 0f00000000;
	add.s32 	%r110, %r109, %r3;
	mul.wide.s32 	%rd55, %r3, 4;
	add.s64 	%rd56, %rd54, %rd55;
	ld.global.f32 	%f8, [%rd56];
	add.f32 	%f236, %f8, 0f00000000;
	add.s32 	%r111, %r110, %r3;
	mul.wide.s32 	%rd57, %r111, 4;
	add.s64 	%rd58, %rd1, %rd57;
	ld.global.f32 	%f9, [%rd58];
	add.f32 	%f237, %f9, 0f00000000;
	ld.global.f32 	%f10, [%rd54+4];
	add.f32 	%f238, %f235, %f10;
	ld.global.f32 	%f11, [%rd56+4];
	add.f32 	%f239, %f236, %f11;
	ld.global.f32 	%f12, [%rd58+4];
	add.f32 	%f240, %f237, %f12;
	ld.global.f32 	%f13, [%rd54+8];
	add.f32 	%f481, %f238, %f13;
	ld.global.f32 	%f15, [%rd56+8];
	add.f32 	%f480, %f239, %f15;
	ld.global.f32 	%f17, [%rd58+8];
	add.f32 	%f479, %f240, %f17;
	shl.b64 	%rd59, %rd5, 2;
	add.s64 	%rd10, %rd1, %rd59;
	add.s64 	%rd11, %rd10, %rd55;
	cvt.u32.u64 	%r112, %rd5;
	add.s32 	%r113, %r112, %r3;
	add.s32 	%r114, %r113, %r3;
	mul.wide.s32 	%rd60, %r114, 4;
	add.s64 	%rd12, %rd1, %rd60;
	mov.u32 	%r197, 3;
	mov.f32 	%f455, %f481;
	mov.f32 	%f456, %f480;
	mov.f32 	%f457, %f479;
	@%p16 bra 	$L__BB1_19;

	ld.global.f32 	%f241, [%rd10+-4];
	add.f32 	%f455, %f481, %f241;
	ld.global.f32 	%f242, [%rd11+-4];
	add.f32 	%f456, %f480, %f242;
	ld.global.f32 	%f243, [%rd12+-4];
	add.f32 	%f457, %f479, %f243;
	mov.u32 	%r197, 4;

$L__BB1_19:
	@%p26 bra 	$L__BB1_21;

	ld.global.f32 	%f244, [%rd10];
	add.f32 	%f455, %f455, %f244;
	ld.global.f32 	%f245, [%rd11];
	add.f32 	%f456, %f456, %f245;
	ld.global.f32 	%f246, [%rd12];
	add.f32 	%f457, %f457, %f246;
	add.s32 	%r197, %r197, 1;

$L__BB1_21:
	@%p22 bra 	$L__BB1_23;

	ld.global.f32 	%f247, [%rd10+4];
	add.f32 	%f455, %f455, %f247;
	ld.global.f32 	%f248, [%rd11+4];
	add.f32 	%f456, %f456, %f248;
	ld.global.f32 	%f249, [%rd12+4];
	add.f32 	%f457, %f457, %f249;
	add.s32 	%r197, %r197, 1;

$L__BB1_23:
	mul.lo.s32 	%r187, %r195, %r92;
	shl.b32 	%r186, %r92, 1;
	sub.s32 	%r185, %r187, %r92;
	add.s32 	%r184, %r185, %r186;
	add.s32 	%r183, %r1, %r184;
	cvt.s64.s32 	%rd93, %r3;
	add.s64 	%rd13, %rd1, %rd52;
	shl.b64 	%rd62, %rd93, 2;
	add.s64 	%rd14, %rd13, %rd62;
	add.s32 	%r117, %r183, %r3;
	add.s32 	%r118, %r117, %r3;
	mul.wide.s32 	%rd63, %r118, 4;
	add.s64 	%rd15, %rd1, %rd63;
	@%p16 bra 	$L__BB1_25;

	ld.global.f32 	%f250, [%rd13+-4];
	add.f32 	%f455, %f455, %f250;
	ld.global.f32 	%f251, [%rd14+-4];
	add.f32 	%f456, %f456, %f251;
	ld.global.f32 	%f252, [%rd15+-4];
	add.f32 	%f457, %f457, %f252;
	add.s32 	%r197, %r197, 1;

$L__BB1_25:
	@%p26 bra 	$L__BB1_27;

	ld.global.f32 	%f253, [%rd13];
	add.f32 	%f455, %f455, %f253;
	ld.global.f32 	%f254, [%rd14];
	add.f32 	%f456, %f456, %f254;
	ld.global.f32 	%f255, [%rd15];
	add.f32 	%f457, %f457, %f255;
	add.s32 	%r197, %r197, 1;

$L__BB1_27:
	@%p22 bra 	$L__BB1_29;

	ld.global.f32 	%f256, [%rd13+4];
	add.f32 	%f455, %f455, %f256;
	ld.global.f32 	%f257, [%rd14+4];
	add.f32 	%f456, %f456, %f257;
	ld.global.f32 	%f258, [%rd15+4];
	add.f32 	%f457, %f457, %f258;
	add.s32 	%r197, %r197, 1;

$L__BB1_29:
	setp.gt.s32 	%p35, %r197, 0;
	@%p35 bra 	$L__BB1_31;
	bra.uni 	$L__BB1_30;

$L__BB1_31:
	cvt.rn.f32.s32 	%f259, %r197;
	div.rn.f32 	%f538, %f455, %f259;
	div.rn.f32 	%f539, %f456, %f259;
	div.rn.f32 	%f540, %f457, %f259;
	bra.uni 	$L__BB1_32;

$L__BB1_30:
	ld.global.f32 	%f538, [%rd10];
	ld.global.f32 	%f539, [%rd11];
	ld.global.f32 	%f540, [%rd12];

$L__BB1_32:
	fma.rn.f32 	%f260, %f7, %f7, 0f00000000;
	fma.rn.f32 	%f261, %f8, %f8, 0f00000000;
	fma.rn.f32 	%f262, %f9, %f9, 0f00000000;
	fma.rn.f32 	%f263, %f10, %f10, %f260;
	fma.rn.f32 	%f264, %f11, %f11, %f261;
	fma.rn.f32 	%f265, %f12, %f12, %f262;
	fma.rn.f32 	%f482, %f13, %f13, %f263;
	fma.rn.f32 	%f483, %f15, %f15, %f264;
	fma.rn.f32 	%f484, %f17, %f17, %f265;
	mov.u32 	%r203, 3;
	@%p16 bra 	$L__BB1_34;

	ld.global.f32 	%f266, [%rd10+-4];
	add.f32 	%f481, %f481, %f266;
	ld.global.f32 	%f267, [%rd11+-4];
	add.f32 	%f480, %f480, %f267;
	ld.global.f32 	%f268, [%rd12+-4];
	add.f32 	%f479, %f479, %f268;
	fma.rn.f32 	%f482, %f266, %f266, %f482;
	fma.rn.f32 	%f483, %f267, %f267, %f483;
	fma.rn.f32 	%f484, %f268, %f268, %f484;
	mov.u32 	%r203, 4;

$L__BB1_34:
	@%p26 bra 	$L__BB1_36;

	ld.global.f32 	%f269, [%rd10];
	add.f32 	%f481, %f481, %f269;
	ld.global.f32 	%f270, [%rd11];
	add.f32 	%f480, %f480, %f270;
	ld.global.f32 	%f271, [%rd12];
	add.f32 	%f479, %f479, %f271;
	fma.rn.f32 	%f482, %f269, %f269, %f482;
	fma.rn.f32 	%f483, %f270, %f270, %f483;
	fma.rn.f32 	%f484, %f271, %f271, %f484;
	add.s32 	%r203, %r203, 1;

$L__BB1_36:
	@%p22 bra 	$L__BB1_38;

	ld.global.f32 	%f272, [%rd10+4];
	add.f32 	%f481, %f481, %f272;
	ld.global.f32 	%f273, [%rd11+4];
	add.f32 	%f480, %f480, %f273;
	ld.global.f32 	%f274, [%rd12+4];
	add.f32 	%f479, %f479, %f274;
	fma.rn.f32 	%f482, %f272, %f272, %f482;
	fma.rn.f32 	%f483, %f273, %f273, %f483;
	fma.rn.f32 	%f484, %f274, %f274, %f484;
	add.s32 	%r203, %r203, 1;

$L__BB1_38:
	@%p16 bra 	$L__BB1_40;

	ld.global.f32 	%f275, [%rd13+-4];
	add.f32 	%f481, %f481, %f275;
	ld.global.f32 	%f276, [%rd14+-4];
	add.f32 	%f480, %f480, %f276;
	ld.global.f32 	%f277, [%rd15+-4];
	add.f32 	%f479, %f479, %f277;
	fma.rn.f32 	%f482, %f275, %f275, %f482;
	fma.rn.f32 	%f483, %f276, %f276, %f483;
	fma.rn.f32 	%f484, %f277, %f277, %f484;
	add.s32 	%r203, %r203, 1;

$L__BB1_40:
	@%p26 bra 	$L__BB1_42;

	ld.global.f32 	%f278, [%rd13];
	add.f32 	%f481, %f481, %f278;
	ld.global.f32 	%f279, [%rd14];
	add.f32 	%f480, %f480, %f279;
	ld.global.f32 	%f280, [%rd15];
	add.f32 	%f479, %f479, %f280;
	fma.rn.f32 	%f482, %f278, %f278, %f482;
	fma.rn.f32 	%f483, %f279, %f279, %f483;
	fma.rn.f32 	%f484, %f280, %f280, %f484;
	add.s32 	%r203, %r203, 1;

$L__BB1_42:
	@%p22 bra 	$L__BB1_44;

	ld.global.f32 	%f281, [%rd13+4];
	add.f32 	%f481, %f481, %f281;
	ld.global.f32 	%f282, [%rd14+4];
	add.f32 	%f480, %f480, %f282;
	ld.global.f32 	%f283, [%rd15+4];
	add.f32 	%f479, %f479, %f283;
	fma.rn.f32 	%f482, %f281, %f281, %f482;
	fma.rn.f32 	%f483, %f282, %f282, %f483;
	fma.rn.f32 	%f484, %f283, %f283, %f484;
	add.s32 	%r203, %r203, 1;

$L__BB1_44:
	setp.lt.s32 	%p42, %r203, 2;
	mov.f32 	%f284, 0f3F800000;
	mov.f32 	%f509, %f284;
	@%p42 bra 	$L__BB1_46;

	cvt.rn.f32.s32 	%f285, %r203;
	div.rn.f32 	%f286, %f481, %f285;
	div.rn.f32 	%f287, %f480, %f285;
	div.rn.f32 	%f288, %f479, %f285;
	div.rn.f32 	%f289, %f482, %f285;
	mul.f32 	%f290, %f286, %f286;
	sub.f32 	%f291, %f289, %f290;
	div.rn.f32 	%f292, %f483, %f285;
	mul.f32 	%f293, %f287, %f287;
	sub.f32 	%f294, %f292, %f293;
	div.rn.f32 	%f295, %f484, %f285;
	mul.f32 	%f296, %f288, %f288;
	sub.f32 	%f297, %f295, %f296;
	add.f32 	%f298, %f291, %f294;
	add.f32 	%f299, %f297, %f298;
	div.rn.f32 	%f509, %f299, 0f40400000;

$L__BB1_46:
	sub.f32 	%f301, %f284, %f509;
	mov.f32 	%f302, 0f3C23D70A;
	max.f32 	%f141, %f302, %f301;
	setp.lt.s32 	%p43, %r213, 0;
	@%p43 bra 	$L__BB1_69;

	add.s32 	%r208, %r213, 1;
	setp.le.s32 	%p44, %r195, %r208;
	@%p44 bra 	$L__BB1_69;

	sub.s32 	%r121, %r195, %r213;
	cvt.rn.f32.s32 	%f146, %r121;
	sub.f32 	%f147, %f538, %f6;
	sub.f32 	%f148, %f539, %f5;
	sub.f32 	%f149, %f540, %f4;
	add.f32 	%f303, %f541, %f141;
	mul.f32 	%f150, %f303, 0f3F000000;
	div.rn.f32 	%f304, %f146, %f2;
	mov.f32 	%f305, 0f3F800000;
	sub.f32 	%f151, %f305, %f304;
	not.b32 	%r122, %r213;
	add.s32 	%r123, %r195, %r122;
	and.b32  	%r36, %r123, 3;
	setp.eq.s32 	%p45, %r36, 0;
	@%p45 bra 	$L__BB1_58;

	setp.eq.s32 	%p46, %r195, %r213;
	mad.lo.s32 	%r124, %r208, %r92, %r1;
	cvt.s64.s32 	%rd16, %r124;
	mov.f32 	%f510, %f6;
	mov.f32 	%f511, %f5;
	mov.f32 	%f512, %f4;
	mov.f32 	%f513, %f541;
	@%p46 bra 	$L__BB1_51;

	rcp.rn.f32 	%f306, %f146;
	mov.f32 	%f307, 0f3F800000;
	sub.s32 	%r125, %r195, %r208;
	min.s32 	%r126, %r125, 1;
	cvt.rn.f32.s32 	%f308, %r126;
	div.rn.f32 	%f309, %f308, %f1;
	sub.f32 	%f310, %f307, %f309;
	mov.f32 	%f311, 0f3DCCCCCD;
	max.f32 	%f312, %f311, %f310;
	mul.f32 	%f313, %f150, %f312;
	max.f32 	%f314, %f311, %f151;
	fma.rn.f32 	%f512, %f149, %f306, %f4;
	fma.rn.f32 	%f511, %f148, %f306, %f5;
	fma.rn.f32 	%f510, %f147, %f306, %f6;
	mul.f32 	%f513, %f313, %f314;

$L__BB1_51:
	shl.b64 	%rd64, %rd16, 4;
	add.s64 	%rd65, %rd3, %rd64;
	st.global.f32 	[%rd65], %f510;
	st.global.f32 	[%rd65+4], %f511;
	st.global.f32 	[%rd65+8], %f512;
	st.global.f32 	[%rd65+12], %f513;
	add.s32 	%r208, %r213, 2;
	setp.eq.s32 	%p47, %r36, 1;
	@%p47 bra 	$L__BB1_58;

	cvt.u32.u64 	%r127, %rd16;
	add.s32 	%r128, %r127, %r92;
	cvt.s64.s32 	%rd17, %r128;
	mov.f32 	%f514, %f6;
	mov.f32 	%f515, %f5;
	mov.f32 	%f516, %f4;
	mov.f32 	%f517, %f541;
	@%p46 bra 	$L__BB1_54;

	mov.f32 	%f315, 0f40000000;
	div.rn.f32 	%f316, %f315, %f146;
	sub.s32 	%r129, %r195, %r208;
	min.s32 	%r130, %r129, 2;
	cvt.rn.f32.s32 	%f317, %r130;
	div.rn.f32 	%f318, %f317, %f1;
	mov.f32 	%f319, 0f3F800000;
	sub.f32 	%f320, %f319, %f318;
	mov.f32 	%f321, 0f3DCCCCCD;
	max.f32 	%f322, %f321, %f320;
	mul.f32 	%f323, %f150, %f322;
	max.f32 	%f324, %f321, %f151;
	mul.f32 	%f517, %f323, %f324;
	fma.rn.f32 	%f516, %f149, %f316, %f4;
	fma.rn.f32 	%f515, %f148, %f316, %f5;
	fma.rn.f32 	%f514, %f147, %f316, %f6;

$L__BB1_54:
	shl.b64 	%rd66, %rd17, 4;
	add.s64 	%rd67, %rd3, %rd66;
	st.global.f32 	[%rd67], %f514;
	st.global.f32 	[%rd67+4], %f515;
	st.global.f32 	[%rd67+8], %f516;
	st.global.f32 	[%rd67+12], %f517;
	add.s32 	%r208, %r213, 3;
	setp.eq.s32 	%p49, %r36, 2;
	@%p49 bra 	$L__BB1_58;

	mov.f32 	%f518, %f6;
	mov.f32 	%f519, %f5;
	mov.f32 	%f520, %f4;
	mov.f32 	%f521, %f541;
	@%p46 bra 	$L__BB1_57;

	mov.f32 	%f325, 0f40400000;
	div.rn.f32 	%f326, %f325, %f146;
	sub.s32 	%r131, %r195, %r208;
	min.s32 	%r132, %r131, 3;
	cvt.rn.f32.s32 	%f327, %r132;
	div.rn.f32 	%f328, %f327, %f1;
	mov.f32 	%f329, 0f3F800000;
	sub.f32 	%f330, %f329, %f328;
	mov.f32 	%f331, 0f3DCCCCCD;
	max.f32 	%f332, %f331, %f330;
	mul.f32 	%f333, %f150, %f332;
	max.f32 	%f334, %f331, %f151;
	mul.f32 	%f521, %f333, %f334;
	fma.rn.f32 	%f520, %f149, %f326, %f4;
	fma.rn.f32 	%f519, %f148, %f326, %f5;
	fma.rn.f32 	%f518, %f147, %f326, %f6;

$L__BB1_57:
	cvt.u32.u64 	%r133, %rd17;
	add.s32 	%r134, %r133, %r92;
	mul.wide.s32 	%rd68, %r134, 16;
	add.s64 	%rd69, %rd3, %rd68;
	st.global.f32 	[%rd69], %f518;
	st.global.f32 	[%rd69+4], %f519;
	st.global.f32 	[%rd69+8], %f520;
	st.global.f32 	[%rd69+12], %f521;
	add.s32 	%r208, %r213, 4;

$L__BB1_58:
	add.s32 	%r135, %r195, -2;
	sub.s32 	%r136, %r135, %r213;
	setp.lt.u32 	%p51, %r136, 3;
	@%p51 bra 	$L__BB1_69;

	add.s32 	%r137, %r208, 3;
	sub.s32 	%r211, %r137, %r213;
	sub.s32 	%r210, %r195, %r208;
	mad.lo.s32 	%r209, %r92, %r208, %r1;

$L__BB1_60:
	setp.eq.s32 	%p52, %r195, %r213;
	mov.f32 	%f522, %f6;
	mov.f32 	%f523, %f5;
	mov.f32 	%f524, %f4;
	mov.f32 	%f525, %f541;
	@%p52 bra 	$L__BB1_62;

	add.s32 	%r138, %r211, -3;
	cvt.rn.f32.s32 	%f335, %r138;
	div.rn.f32 	%f336, %f335, %f146;
	min.s32 	%r139, %r138, %r210;
	cvt.rn.f32.s32 	%f337, %r139;
	div.rn.f32 	%f338, %f337, %f1;
	mov.f32 	%f339, 0f3F800000;
	sub.f32 	%f340, %f339, %f338;
	mov.f32 	%f341, 0f3DCCCCCD;
	max.f32 	%f342, %f341, %f340;
	mul.f32 	%f343, %f150, %f342;
	max.f32 	%f344, %f341, %f151;
	mul.f32 	%f525, %f343, %f344;
	fma.rn.f32 	%f524, %f149, %f336, %f4;
	fma.rn.f32 	%f523, %f148, %f336, %f5;
	fma.rn.f32 	%f522, %f147, %f336, %f6;

$L__BB1_62:
	cvt.s64.s32 	%rd95, %r209;
	shl.b64 	%rd70, %rd95, 4;
	add.s64 	%rd19, %rd3, %rd70;
	st.global.f32 	[%rd19], %f522;
	st.global.f32 	[%rd19+4], %f523;
	st.global.f32 	[%rd19+8], %f524;
	st.global.f32 	[%rd19+12], %f525;
	mov.f32 	%f526, %f6;
	mov.f32 	%f527, %f5;
	mov.f32 	%f528, %f4;
	mov.f32 	%f529, %f541;
	@%p52 bra 	$L__BB1_64;

	add.s32 	%r140, %r211, -2;
	cvt.rn.f32.s32 	%f345, %r140;
	div.rn.f32 	%f346, %f345, %f146;
	add.s32 	%r141, %r210, -1;
	min.s32 	%r142, %r140, %r141;
	cvt.rn.f32.s32 	%f347, %r142;
	div.rn.f32 	%f348, %f347, %f1;
	mov.f32 	%f349, 0f3F800000;
	sub.f32 	%f350, %f349, %f348;
	mov.f32 	%f351, 0f3DCCCCCD;
	max.f32 	%f352, %f351, %f350;
	mul.f32 	%f353, %f150, %f352;
	max.f32 	%f354, %f351, %f151;
	mul.f32 	%f529, %f353, %f354;
	fma.rn.f32 	%f528, %f149, %f346, %f4;
	fma.rn.f32 	%f527, %f148, %f346, %f5;
	fma.rn.f32 	%f526, %f147, %f346, %f6;

$L__BB1_64:
	add.s64 	%rd20, %rd19, %rd4;
	st.global.f32 	[%rd20], %f526;
	st.global.f32 	[%rd20+4], %f527;
	st.global.f32 	[%rd20+8], %f528;
	st.global.f32 	[%rd20+12], %f529;
	mov.f32 	%f530, %f6;
	mov.f32 	%f531, %f5;
	mov.f32 	%f532, %f4;
	mov.f32 	%f533, %f541;
	@%p52 bra 	$L__BB1_66;

	add.s32 	%r143, %r211, -1;
	cvt.rn.f32.s32 	%f355, %r143;
	div.rn.f32 	%f356, %f355, %f146;
	add.s32 	%r144, %r210, -2;
	min.s32 	%r145, %r143, %r144;
	cvt.rn.f32.s32 	%f357, %r145;
	div.rn.f32 	%f358, %f357, %f1;
	mov.f32 	%f359, 0f3F800000;
	sub.f32 	%f360, %f359, %f358;
	mov.f32 	%f361, 0f3DCCCCCD;
	max.f32 	%f362, %f361, %f360;
	mul.f32 	%f363, %f150, %f362;
	max.f32 	%f364, %f361, %f151;
	mul.f32 	%f533, %f363, %f364;
	fma.rn.f32 	%f532, %f149, %f356, %f4;
	fma.rn.f32 	%f531, %f148, %f356, %f5;
	fma.rn.f32 	%f530, %f147, %f356, %f6;

$L__BB1_66:
	add.s64 	%rd21, %rd20, %rd4;
	st.global.f32 	[%rd21], %f530;
	st.global.f32 	[%rd21+4], %f531;
	st.global.f32 	[%rd21+8], %f532;
	st.global.f32 	[%rd21+12], %f533;
	mov.f32 	%f534, %f6;
	mov.f32 	%f535, %f5;
	mov.f32 	%f536, %f4;
	mov.f32 	%f537, %f541;
	@%p52 bra 	$L__BB1_68;

	cvt.rn.f32.s32 	%f365, %r211;
	div.rn.f32 	%f366, %f365, %f146;
	add.s32 	%r146, %r210, -3;
	min.s32 	%r147, %r211, %r146;
	cvt.rn.f32.s32 	%f367, %r147;
	div.rn.f32 	%f368, %f367, %f1;
	mov.f32 	%f369, 0f3F800000;
	sub.f32 	%f370, %f369, %f368;
	mov.f32 	%f371, 0f3DCCCCCD;
	max.f32 	%f372, %f371, %f370;
	mul.f32 	%f373, %f150, %f372;
	max.f32 	%f374, %f371, %f151;
	mul.f32 	%f537, %f373, %f374;
	fma.rn.f32 	%f536, %f149, %f366, %f4;
	fma.rn.f32 	%f535, %f148, %f366, %f5;
	fma.rn.f32 	%f534, %f147, %f366, %f6;

$L__BB1_68:
	cvt.s64.s32 	%rd94, %r209;
	add.s64 	%rd71, %rd21, %rd4;
	st.global.f32 	[%rd71], %f534;
	st.global.f32 	[%rd71+4], %f535;
	st.global.f32 	[%rd71+8], %f536;
	st.global.f32 	[%rd71+12], %f537;
	add.s32 	%r211, %r211, 4;
	add.s32 	%r210, %r210, -4;
	cvt.u32.u64 	%r148, %rd94;
	add.s32 	%r209, %r148, %r6;
	add.s32 	%r208, %r208, 4;
	setp.gt.s32 	%p56, %r195, %r208;
	@%p56 bra 	$L__BB1_60;

$L__BB1_69:
	mov.f32 	%f447, 0f3F800000;
	sub.f32 	%f446, %f447, %f509;
	mov.f32 	%f445, 0f3C23D70A;
	max.f32 	%f541, %f445, %f446;
	shl.b64 	%rd72, %rd5, 4;
	add.s64 	%rd73, %rd3, %rd72;
	st.global.f32 	[%rd73], %f538;
	st.global.f32 	[%rd73+4], %f539;
	st.global.f32 	[%rd73+8], %f540;
	st.global.f32 	[%rd73+12], %f541;
	mov.u16 	%rs9, 1;
	mov.u32 	%r213, %r195;

$L__BB1_70:
	add.s32 	%r195, %r195, 1;
	setp.lt.s32 	%p57, %r195, %r93;
	@%p57 bra 	$L__BB1_3;

$L__BB1_71:
	setp.gt.s32 	%p58, %r213, -1;
	and.b16  	%rs7, %rs9, 255;
	setp.ne.s16 	%p59, %rs7, 0;
	and.pred  	%p60, %p59, %p58;
	@%p60 bra 	$L__BB1_79;
	bra.uni 	$L__BB1_72;

$L__BB1_79:
	add.s32 	%r222, %r213, 1;
	setp.ge.s32 	%p66, %r222, %r93;
	@%p66 bra 	$L__BB1_86;

	max.s32 	%r189, %r92, %r93;
	cvt.rn.f32.s32 	%f216, %r189;
	not.b32 	%r158, %r213;
	add.s32 	%r159, %r158, %r93;
	and.b32  	%r221, %r159, 3;
	setp.eq.s32 	%p67, %r221, 0;
	@%p67 bra 	$L__BB1_83;

	mad.lo.s32 	%r161, %r92, %r222, %r1;
	mul.wide.s32 	%rd81, %r161, 16;
	add.s64 	%rd82, %rd3, %rd81;
	add.s64 	%rd97, %rd82, 8;
	mul.wide.s32 	%rd28, %r92, 16;
	mov.u32 	%r219, 1;

$L__BB1_82:
	.pragma "nounroll";
	cvt.rn.f32.s32 	%f375, %r219;
	div.rn.f32 	%f376, %f375, %f216;
	mov.f32 	%f377, 0f3F800000;
	sub.f32 	%f378, %f377, %f376;
	mov.f32 	%f379, 0f3D4CCCCD;
	max.f32 	%f380, %f379, %f378;
	mul.f32 	%f381, %f541, %f380;
	st.global.f32 	[%rd97+-8], %f538;
	st.global.f32 	[%rd97+-4], %f539;
	st.global.f32 	[%rd97], %f540;
	st.global.f32 	[%rd97+4], %f381;
	add.s32 	%r222, %r222, 1;
	add.s32 	%r219, %r219, 1;
	add.s64 	%rd97, %rd97, %rd28;
	add.s32 	%r221, %r221, -1;
	setp.ne.s32 	%p68, %r221, 0;
	@%p68 bra 	$L__BB1_82;

$L__BB1_83:
	add.s32 	%r162, %r93, -2;
	sub.s32 	%r163, %r162, %r213;
	setp.lt.u32 	%p69, %r163, 3;
	@%p69 bra 	$L__BB1_86;

	add.s32 	%r164, %r222, 3;
	sub.s32 	%r223, %r164, %r213;
	neg.s32 	%r74, %r213;
	mad.lo.s32 	%r165, %r222, %r92, %r1;
	mul.wide.s32 	%rd83, %r165, 16;
	add.s64 	%rd98, %rd3, %rd83;
	mul.wide.s32 	%rd32, %r92, 16;

$L__BB1_85:
	add.s32 	%r166, %r74, %r222;
	cvt.rn.f32.s32 	%f382, %r166;
	div.rn.f32 	%f383, %f382, %f216;
	mov.f32 	%f384, 0f3F800000;
	sub.f32 	%f385, %f384, %f383;
	mov.f32 	%f386, 0f3D4CCCCD;
	max.f32 	%f387, %f386, %f385;
	mul.f32 	%f388, %f541, %f387;
	st.global.f32 	[%rd98], %f538;
	st.global.f32 	[%rd98+4], %f539;
	st.global.f32 	[%rd98+8], %f540;
	st.global.f32 	[%rd98+12], %f388;
	add.s32 	%r167, %r223, -2;
	cvt.rn.f32.s32 	%f389, %r167;
	div.rn.f32 	%f390, %f389, %f216;
	sub.f32 	%f391, %f384, %f390;
	max.f32 	%f392, %f386, %f391;
	mul.f32 	%f393, %f541, %f392;
	add.s64 	%rd84, %rd98, %rd32;
	st.global.f32 	[%rd84], %f538;
	st.global.f32 	[%rd84+4], %f539;
	st.global.f32 	[%rd84+8], %f540;
	st.global.f32 	[%rd84+12], %f393;
	add.s32 	%r168, %r223, -1;
	cvt.rn.f32.s32 	%f394, %r168;
	div.rn.f32 	%f395, %f394, %f216;
	sub.f32 	%f396, %f384, %f395;
	max.f32 	%f397, %f386, %f396;
	mul.f32 	%f398, %f541, %f397;
	add.s64 	%rd85, %rd84, %rd32;
	st.global.f32 	[%rd85], %f538;
	st.global.f32 	[%rd85+4], %f539;
	st.global.f32 	[%rd85+8], %f540;
	st.global.f32 	[%rd85+12], %f398;
	cvt.rn.f32.s32 	%f399, %r223;
	div.rn.f32 	%f400, %f399, %f216;
	sub.f32 	%f401, %f384, %f400;
	max.f32 	%f402, %f386, %f401;
	mul.f32 	%f403, %f541, %f402;
	add.s64 	%rd86, %rd85, %rd32;
	add.s64 	%rd98, %rd86, %rd32;
	st.global.f32 	[%rd86], %f538;
	st.global.f32 	[%rd86+4], %f539;
	st.global.f32 	[%rd86+8], %f540;
	st.global.f32 	[%rd86+12], %f403;
	add.s32 	%r223, %r223, 4;
	add.s32 	%r222, %r222, 4;
	setp.lt.s32 	%p70, %r222, %r93;
	@%p70 bra 	$L__BB1_85;

$L__BB1_86:
	setp.lt.s32 	%p84, %r93, 1;
	setp.lt.s32 	%p72, %r213, 1;
	or.pred  	%p73, %p72, %p84;
	@%p73 bra 	$L__BB1_103;

	max.s32 	%r188, %r92, %r93;
	cvt.rn.f32.s32 	%f217, %r188;
	neg.s32 	%r170, %r93;
	mov.u32 	%r227, 0;
	neg.s32 	%r171, %r213;
	max.u32 	%r172, %r171, %r170;
	neg.s32 	%r79, %r172;
	and.b32  	%r229, %r79, 3;
	setp.gt.u32 	%p74, %r172, -4;
	@%p74 bra 	$L__BB1_98;

	sub.s32 	%r226, %r79, %r229;
	mul.wide.s32 	%rd35, %r92, 16;
	or.b64  	%rd36, %rd35, 12;
	mov.u32 	%r227, 0;

$L__BB1_89:
	mad.lo.s32 	%r174, %r227, %r92, %r1;
	mul.wide.s32 	%rd87, %r174, 16;
	add.s64 	%rd88, %rd3, %rd87;
	add.s64 	%rd37, %rd88, 12;
	ld.global.f32 	%f404, [%rd88+12];
	setp.neu.f32 	%p75, %f404, 0f00000000;
	@%p75 bra 	$L__BB1_91;

	sub.s32 	%r175, %r213, %r227;
	cvt.rn.f32.s32 	%f405, %r175;
	div.rn.f32 	%f406, %f405, %f217;
	mov.f32 	%f407, 0f3F800000;
	sub.f32 	%f408, %f407, %f406;
	mov.f32 	%f409, 0f3D4CCCCD;
	max.f32 	%f410, %f409, %f408;
	mul.f32 	%f411, %f541, %f410;
	st.global.f32 	[%rd37+-12], %f538;
	st.global.f32 	[%rd37+-8], %f539;
	st.global.f32 	[%rd37+-4], %f540;
	st.global.f32 	[%rd37], %f411;

$L__BB1_91:
	add.s64 	%rd38, %rd37, %rd35;
	ld.global.f32 	%f412, [%rd38];
	setp.neu.f32 	%p76, %f412, 0f00000000;
	@%p76 bra 	$L__BB1_93;

	not.b32 	%r176, %r227;
	add.s32 	%r177, %r213, %r176;
	cvt.rn.f32.s32 	%f413, %r177;
	div.rn.f32 	%f414, %f413, %f217;
	mov.f32 	%f415, 0f3F800000;
	sub.f32 	%f416, %f415, %f414;
	mov.f32 	%f417, 0f3D4CCCCD;
	max.f32 	%f418, %f417, %f416;
	mul.f32 	%f419, %f541, %f418;
	st.global.f32 	[%rd38+-12], %f538;
	st.global.f32 	[%rd38+-8], %f539;
	st.global.f32 	[%rd38+-4], %f540;
	st.global.f32 	[%rd38], %f419;

$L__BB1_93:
	add.s64 	%rd39, %rd38, -12;
	add.s64 	%rd40, %rd39, %rd36;
	ld.global.f32 	%f420, [%rd40];
	setp.neu.f32 	%p77, %f420, 0f00000000;
	@%p77 bra 	$L__BB1_95;

	add.s32 	%r178, %r227, 2;
	sub.s32 	%r179, %r213, %r178;
	cvt.rn.f32.s32 	%f421, %r179;
	div.rn.f32 	%f422, %f421, %f217;
	mov.f32 	%f423, 0f3F800000;
	sub.f32 	%f424, %f423, %f422;
	mov.f32 	%f425, 0f3D4CCCCD;
	max.f32 	%f426, %f425, %f424;
	mul.f32 	%f427, %f541, %f426;
	st.global.f32 	[%rd40+-12], %f538;
	st.global.f32 	[%rd40+-8], %f539;
	st.global.f32 	[%rd40+-4], %f540;
	st.global.f32 	[%rd40], %f427;

$L__BB1_95:
	add.s64 	%rd90, %rd39, %rd35;
	add.s64 	%rd41, %rd90, %rd36;
	ld.global.f32 	%f428, [%rd41];
	setp.neu.f32 	%p78, %f428, 0f00000000;
	@%p78 bra 	$L__BB1_97;

	add.s32 	%r180, %r227, 3;
	sub.s32 	%r181, %r213, %r180;
	cvt.rn.f32.s32 	%f429, %r181;
	div.rn.f32 	%f430, %f429, %f217;
	mov.f32 	%f431, 0f3F800000;
	sub.f32 	%f432, %f431, %f430;
	mov.f32 	%f433, 0f3D4CCCCD;
	max.f32 	%f434, %f433, %f432;
	mul.f32 	%f435, %f541, %f434;
	st.global.f32 	[%rd41+-12], %f538;
	st.global.f32 	[%rd41+-8], %f539;
	st.global.f32 	[%rd41+-4], %f540;
	st.global.f32 	[%rd41], %f435;

$L__BB1_97:
	add.s32 	%r227, %r227, 4;
	add.s32 	%r226, %r226, -4;
	setp.ne.s32 	%p79, %r226, 0;
	@%p79 bra 	$L__BB1_89;

$L__BB1_98:
	setp.eq.s32 	%p80, %r229, 0;
	@%p80 bra 	$L__BB1_103;

	sub.s32 	%r228, %r213, %r227;
	mad.lo.s32 	%r182, %r227, %r92, %r1;
	mul.wide.s32 	%rd91, %r182, 16;
	add.s64 	%rd92, %rd3, %rd91;
	add.s64 	%rd99, %rd92, 12;
	mul.wide.s32 	%rd43, %r92, 16;

$L__BB1_100:
	.pragma "nounroll";
	ld.global.f32 	%f436, [%rd99];
	setp.neu.f32 	%p81, %f436, 0f00000000;
	@%p81 bra 	$L__BB1_102;

	cvt.rn.f32.s32 	%f437, %r228;
	div.rn.f32 	%f438, %f437, %f217;
	mov.f32 	%f439, 0f3F800000;
	sub.f32 	%f440, %f439, %f438;
	mov.f32 	%f441, 0f3D4CCCCD;
	max.f32 	%f442, %f441, %f440;
	mul.f32 	%f443, %f541, %f442;
	st.global.f32 	[%rd99+-12], %f538;
	st.global.f32 	[%rd99+-8], %f539;
	st.global.f32 	[%rd99+-4], %f540;
	st.global.f32 	[%rd99], %f443;

$L__BB1_102:
	add.s32 	%r228, %r228, -1;
	add.s64 	%rd99, %rd99, %rd43;
	add.s32 	%r229, %r229, -1;
	setp.ne.s32 	%p82, %r229, 0;
	@%p82 bra 	$L__BB1_100;
	bra.uni 	$L__BB1_103;

$L__BB1_72:
	setp.lt.s32 	%p83, %r93, 1;
	@%p83 bra 	$L__BB1_103;

	add.s32 	%r150, %r93, -1;
	and.b32  	%r218, %r93, 3;
	setp.lt.u32 	%p62, %r150, 3;
	mov.u32 	%r217, 0;
	@%p62 bra 	$L__BB1_76;

	sub.s32 	%r216, %r93, %r218;
	mul.wide.s32 	%rd22, %r92, 16;
	mov.u32 	%r151, 0;
	mov.u32 	%r217, %r151;

$L__BB1_75:
	mad.lo.s32 	%r152, %r217, %r92, %r1;
	mul.wide.s32 	%rd74, %r152, 16;
	add.s64 	%rd75, %rd3, %rd74;
	st.global.u32 	[%rd75], %r151;
	st.global.u32 	[%rd75+4], %r151;
	st.global.u32 	[%rd75+8], %r151;
	mov.u32 	%r154, 1008981770;
	st.global.u32 	[%rd75+12], %r154;
	add.s64 	%rd76, %rd75, %rd22;
	st.global.u32 	[%rd76], %r151;
	st.global.u32 	[%rd76+4], %r151;
	st.global.u32 	[%rd76+8], %r151;
	st.global.u32 	[%rd76+12], %r154;
	add.s64 	%rd77, %rd76, %rd22;
	st.global.u32 	[%rd77], %r151;
	st.global.u32 	[%rd77+4], %r151;
	st.global.u32 	[%rd77+8], %r151;
	st.global.u32 	[%rd77+12], %r154;
	add.s64 	%rd78, %rd77, %rd22;
	st.global.u32 	[%rd78], %r151;
	st.global.u32 	[%rd78+4], %r151;
	st.global.u32 	[%rd78+8], %r151;
	st.global.u32 	[%rd78+12], %r154;
	add.s32 	%r217, %r217, 4;
	add.s32 	%r216, %r216, -4;
	setp.ne.s32 	%p63, %r216, 0;
	@%p63 bra 	$L__BB1_75;

$L__BB1_76:
	setp.eq.s32 	%p64, %r218, 0;
	@%p64 bra 	$L__BB1_103;

	mad.lo.s32 	%r155, %r217, %r92, %r1;
	mul.wide.s32 	%rd79, %r155, 16;
	add.s64 	%rd80, %rd3, %rd79;
	add.s64 	%rd96, %rd80, 8;
	mul.wide.s32 	%rd24, %r92, 16;

$L__BB1_78:
	.pragma "nounroll";
	mov.u32 	%r156, 0;
	st.global.u32 	[%rd96+-8], %r156;
	st.global.u32 	[%rd96+-4], %r156;
	st.global.u32 	[%rd96], %r156;
	mov.u32 	%r157, 1008981770;
	st.global.u32 	[%rd96+4], %r157;
	add.s64 	%rd96, %rd96, %rd24;
	add.s32 	%r218, %r218, -1;
	setp.eq.s32 	%p65, %r218, 0;
	@%p65 bra 	$L__BB1_103;
	bra.uni 	$L__BB1_78;

$L__BB1_103:
	ret;

}
	// .globl	_Z23extractForegroundKernelPKfS0_PK14BackgroundDataS3_Pfii
.visible .entry _Z23extractForegroundKernelPKfS0_PK14BackgroundDataS3_Pfii(
	.param .u64 _Z23extractForegroundKernelPKfS0_PK14BackgroundDataS3_Pfii_param_0,
	.param .u64 _Z23extractForegroundKernelPKfS0_PK14BackgroundDataS3_Pfii_param_1,
	.param .u64 _Z23extractForegroundKernelPKfS0_PK14BackgroundDataS3_Pfii_param_2,
	.param .u64 _Z23extractForegroundKernelPKfS0_PK14BackgroundDataS3_Pfii_param_3,
	.param .u64 _Z23extractForegroundKernelPKfS0_PK14BackgroundDataS3_Pfii_param_4,
	.param .u32 _Z23extractForegroundKernelPKfS0_PK14BackgroundDataS3_Pfii_param_5,
	.param .u32 _Z23extractForegroundKernelPKfS0_PK14BackgroundDataS3_Pfii_param_6
)
{
	.reg .pred 	%p<7>;
	.reg .f32 	%f<47>;
	.reg .b32 	%r<26>;
	.reg .b64 	%rd<27>;


	ld.param.u64 	%rd3, [_Z23extractForegroundKernelPKfS0_PK14BackgroundDataS3_Pfii_param_0];
	ld.param.u64 	%rd4, [_Z23extractForegroundKernelPKfS0_PK14BackgroundDataS3_Pfii_param_1];
	ld.param.u64 	%rd5, [_Z23extractForegroundKernelPKfS0_PK14BackgroundDataS3_Pfii_param_2];
	ld.param.u64 	%rd6, [_Z23extractForegroundKernelPKfS0_PK14BackgroundDataS3_Pfii_param_3];
	ld.param.u64 	%rd7, [_Z23extractForegroundKernelPKfS0_PK14BackgroundDataS3_Pfii_param_4];
	ld.param.u32 	%r3, [_Z23extractForegroundKernelPKfS0_PK14BackgroundDataS3_Pfii_param_5];
	ld.param.u32 	%r4, [_Z23extractForegroundKernelPKfS0_PK14BackgroundDataS3_Pfii_param_6];
	mov.u32 	%r5, %ntid.x;
	mov.u32 	%r6, %ctaid.x;
	mov.u32 	%r7, %tid.x;
	mad.lo.s32 	%r1, %r6, %r5, %r7;
	mov.u32 	%r8, %ntid.y;
	mov.u32 	%r9, %ctaid.y;
	mov.u32 	%r10, %tid.y;
	mad.lo.s32 	%r2, %r9, %r8, %r10;
	setp.ge.s32 	%p1, %r1, %r3;
	setp.ge.s32 	%p2, %r2, %r4;
	or.pred  	%p3, %p1, %p2;
	@%p3 bra 	$L__BB2_8;

	cvta.to.global.u64 	%rd8, %rd4;
	cvta.to.global.u64 	%rd9, %rd3;
	mad.lo.s32 	%r11, %r2, %r3, %r1;
	shl.b32 	%r12, %r11, 2;
	cvt.s64.s32 	%rd1, %r11;
	mul.wide.s32 	%rd10, %r11, 4;
	add.s64 	%rd11, %rd9, %rd10;
	add.s64 	%rd12, %rd8, %rd10;
	ld.global.f32 	%f1, [%rd12];
	mul.lo.s32 	%r13, %r4, %r3;
	add.s32 	%r14, %r13, %r11;
	mul.wide.s32 	%rd13, %r13, 4;
	add.s64 	%rd14, %rd12, %rd13;
	ld.global.f32 	%f2, [%rd14];
	add.s32 	%r15, %r14, %r13;
	mul.wide.s32 	%rd15, %r15, 4;
	add.s64 	%rd16, %rd8, %rd15;
	ld.global.f32 	%f3, [%rd16];
	ld.global.f32 	%f4, [%rd11];
	setp.gtu.f32 	%p4, %f4, 0f3CA0A0A1;
	cvta.to.global.u64 	%rd17, %rd7;
	mul.wide.s32 	%rd18, %r12, 4;
	add.s64 	%rd2, %rd17, %rd18;
	@%p4 bra 	$L__BB2_3;
	bra.uni 	$L__BB2_2;

$L__BB2_3:
	setp.ltu.f32 	%p5, %f4, 0f3F7AFAFB;
	@%p5 bra 	$L__BB2_5;
	bra.uni 	$L__BB2_4;

$L__BB2_5:
	cvta.to.global.u64 	%rd19, %rd5;
	shl.b64 	%rd20, %rd1, 4;
	add.s64 	%rd21, %rd19, %rd20;
	ld.global.f32 	%f5, [%rd21];
	ld.global.f32 	%f6, [%rd21+4];
	ld.global.f32 	%f7, [%rd21+8];
	cvta.to.global.u64 	%rd22, %rd6;
	add.s64 	%rd23, %rd22, %rd20;
	ld.global.f32 	%f8, [%rd23];
	ld.global.f32 	%f9, [%rd23+4];
	ld.global.f32 	%f10, [%rd23+8];
	ld.global.f32 	%f11, [%rd23+12];
	ld.global.f32 	%f12, [%rd21+12];
	add.f32 	%f19, %f12, %f11;
	setp.leu.f32 	%p6, %f19, 0f3C23D70A;
	mov.f32 	%f45, 0f3F000000;
	mov.f32 	%f46, %f45;
	@%p6 bra 	$L__BB2_7;

	div.rn.f32 	%f45, %f12, %f19;
	div.rn.f32 	%f46, %f11, %f19;

$L__BB2_7:
	mul.f32 	%f21, %f8, %f46;
	fma.rn.f32 	%f22, %f5, %f45, %f21;
	mul.f32 	%f23, %f9, %f46;
	fma.rn.f32 	%f24, %f6, %f45, %f23;
	mul.f32 	%f25, %f10, %f46;
	fma.rn.f32 	%f26, %f7, %f45, %f25;
	mov.f32 	%f27, 0f3F800000;
	sub.f32 	%f28, %f27, %f4;
	mul.f32 	%f29, %f28, %f22;
	sub.f32 	%f30, %f1, %f29;
	div.rn.f32 	%f31, %f30, %f4;
	mul.f32 	%f32, %f28, %f24;
	sub.f32 	%f33, %f2, %f32;
	div.rn.f32 	%f34, %f33, %f4;
	mul.f32 	%f35, %f28, %f26;
	sub.f32 	%f36, %f3, %f35;
	div.rn.f32 	%f37, %f36, %f4;
	min.f32 	%f38, %f27, %f31;
	mov.f32 	%f39, 0f00000000;
	max.f32 	%f40, %f39, %f38;
	min.f32 	%f41, %f27, %f34;
	max.f32 	%f42, %f39, %f41;
	min.f32 	%f43, %f27, %f37;
	max.f32 	%f44, %f39, %f43;
	st.global.f32 	[%rd2], %f40;
	st.global.f32 	[%rd2+4], %f42;
	st.global.f32 	[%rd2+8], %f44;
	st.global.f32 	[%rd2+12], %f4;
	bra.uni 	$L__BB2_8;

$L__BB2_2:
	st.global.f32 	[%rd2], %f1;
	st.global.f32 	[%rd2+4], %f2;
	st.global.f32 	[%rd2+8], %f3;
	st.global.f32 	[%rd2+12], %f4;
	bra.uni 	$L__BB2_8;

$L__BB2_4:
	st.global.f32 	[%rd2], %f1;
	st.global.f32 	[%rd2+4], %f2;
	st.global.f32 	[%rd2+8], %f3;
	st.global.f32 	[%rd2+12], %f4;

$L__BB2_8:
	ret;

}
	// .globl	LanczosResizeDownKernel
.visible .entry LanczosResizeDownKernel(
	.param .u64 LanczosResizeDownKernel_param_0,
	.param .u32 LanczosResizeDownKernel_param_1,
	.param .u32 LanczosResizeDownKernel_param_2,
	.param .u64 LanczosResizeDownKernel_param_3,
	.param .u32 LanczosResizeDownKernel_param_4,
	.param .u32 LanczosResizeDownKernel_param_5,
	.param .u32 LanczosResizeDownKernel_param_6
)
{
	.local .align 4 .b8 	__local_depot3[28];
	.reg .b64 	%SP;
	.reg .b64 	%SPL;
	.reg .pred 	%p<56>;
	.reg .f32 	%f<220>;
	.reg .b32 	%r<253>;
	.reg .f64 	%fd<9>;
	.reg .b64 	%rd<91>;


	mov.u64 	%SPL, __local_depot3;
	ld.param.u64 	%rd27, [LanczosResizeDownKernel_param_0];
	ld.param.u32 	%r79, [LanczosResizeDownKernel_param_1];
	ld.param.u32 	%r80, [LanczosResizeDownKernel_param_2];
	ld.param.u64 	%rd28, [LanczosResizeDownKernel_param_3];
	ld.param.u32 	%r81, [LanczosResizeDownKernel_param_4];
	ld.param.u32 	%r82, [LanczosResizeDownKernel_param_5];
	ld.param.u32 	%r83, [LanczosResizeDownKernel_param_6];
	add.u64 	%rd1, %SPL, 0;
	mov.u32 	%r84, %ntid.x;
	mov.u32 	%r85, %ctaid.x;
	mov.u32 	%r86, %tid.x;
	mad.lo.s32 	%r1, %r85, %r84, %r86;
	mov.u32 	%r87, %ntid.y;
	mov.u32 	%r88, %ctaid.y;
	mov.u32 	%r89, %tid.y;
	mad.lo.s32 	%r2, %r88, %r87, %r89;
	setp.ge.s32 	%p1, %r1, %r79;
	setp.ge.s32 	%p2, %r2, %r80;
	or.pred  	%p3, %p1, %p2;
	@%p3 bra 	$L__BB3_68;

	cvt.rn.f32.s32 	%f79, %r81;
	cvt.rn.f32.s32 	%f80, %r79;
	div.rn.f32 	%f81, %f79, %f80;
	cvt.rn.f32.s32 	%f82, %r80;
	cvt.rn.f32.s32 	%f83, %r82;
	div.rn.f32 	%f84, %f83, %f82;
	cvt.rn.f32.s32 	%f85, %r1;
	add.f32 	%f86, %f85, 0f3F000000;
	fma.rn.f32 	%f1, %f86, %f81, 0fBF000000;
	cvt.rn.f32.s32 	%f87, %r2;
	add.f32 	%f88, %f87, 0f3F000000;
	fma.rn.f32 	%f2, %f88, %f84, 0fBF000000;
	cvt.rmi.f32.f32 	%f89, %f1;
	cvt.rzi.s32.f32 	%r90, %f89;
	add.s32 	%r91, %r90, -2;
	max.s32 	%r3, %r91, 0;
	cvt.rpi.f32.f32 	%f90, %f1;
	cvt.rzi.s32.f32 	%r92, %f90;
	add.s32 	%r93, %r92, 2;
	add.s32 	%r94, %r81, -1;
	min.s32 	%r4, %r94, %r93;
	cvt.rmi.f32.f32 	%f91, %f2;
	cvt.rzi.s32.f32 	%r95, %f91;
	add.s32 	%r96, %r95, -2;
	max.s32 	%r5, %r96, 0;
	cvt.rpi.f32.f32 	%f92, %f2;
	cvt.rzi.s32.f32 	%r97, %f92;
	add.s32 	%r98, %r97, 2;
	add.s32 	%r99, %r82, -1;
	min.s32 	%r6, %r99, %r98;
	setp.lt.s32 	%p4, %r83, 1;
	@%p4 bra 	$L__BB3_68;

	cvta.to.global.u64 	%rd2, %rd27;
	cvta.to.global.u64 	%rd3, %rd28;
	mov.u32 	%r237, 0;

$L__BB3_3:
	setp.gt.s32 	%p5, %r5, %r6;
	mov.f32 	%f219, 0f00000000;
	mov.f32 	%f213, %f219;
	mov.f32 	%f214, %f219;
	@%p5 bra 	$L__BB3_65;

	ld.param.u32 	%r233, [LanczosResizeDownKernel_param_5];
	mov.f32 	%f214, 0f00000000;
	mov.u32 	%r238, %r5;
	mov.f32 	%f213, %f214;

$L__BB3_5:
	setp.gt.s32 	%p6, %r3, %r4;
	@%p6 bra 	$L__BB3_64;

	ld.param.u32 	%r236, [LanczosResizeDownKernel_param_5];
	mul.lo.s32 	%r235, %r237, %r233;
	ld.param.u32 	%r234, [LanczosResizeDownKernel_param_4];
	cvt.rn.f32.s32 	%f97, %r238;
	sub.f32 	%f5, %f2, %f97;
	add.s32 	%r101, %r238, %r235;
	mul.lo.s32 	%r10, %r101, %r234;
	mul.f32 	%f6, %f5, 0f40490FDB;
	div.rn.f32 	%f7, %f6, 0f40400000;
	mul.f32 	%f8, %f6, 0f3F22F983;
	mov.b32 	%r102, %f6;
	and.b32  	%r11, %r102, -2147483648;
	bfe.u32 	%r103, %r102, 23, 8;
	add.s32 	%r104, %r103, -128;
	shl.b32 	%r105, %r102, 8;
	or.b32  	%r12, %r105, -2147483648;
	shr.u32 	%r106, %r104, 5;
	mov.u32 	%r107, 4;
	sub.s32 	%r108, %r107, %r106;
	and.b32  	%r13, %r104, 31;
	mov.u32 	%r109, 6;
	sub.s32 	%r110, %r109, %r106;
	mul.wide.s32 	%rd30, %r110, 4;
	add.s64 	%rd4, %rd1, %rd30;
	mov.b32 	%r111, %f7;
	and.b32  	%r14, %r111, -2147483648;
	bfe.u32 	%r112, %r111, 23, 8;
	add.s32 	%r113, %r112, -128;
	shr.u32 	%r115, %r113, 5;
	sub.s32 	%r116, %r107, %r115;
	xor.b32  	%r16, %r11, -2147483648;
	mov.u32 	%r117, 32;
	sub.s32 	%r17, %r117, %r13;
	mul.wide.s32 	%rd31, %r108, 4;
	add.s64 	%rd5, %rd1, %rd31;
	mul.f32 	%f10, %f6, %f6;
	and.b32  	%r18, %r113, 31;
	sub.s32 	%r118, %r109, %r115;
	mul.wide.s32 	%rd32, %r118, 4;
	add.s64 	%rd6, %rd1, %rd32;
	xor.b32  	%r19, %r14, -2147483648;
	sub.s32 	%r20, %r117, %r18;
	mul.wide.s32 	%rd33, %r116, 4;
	add.s64 	%rd7, %rd1, %rd33;
	mov.u32 	%r239, %r3;

$L__BB3_7:
	cvt.rn.f32.s32 	%f99, %r239;
	sub.f32 	%f13, %f1, %f99;
	setp.eq.f32 	%p7, %f13, 0f00000000;
	add.s64 	%rd8, %rd1, 24;
	mov.f32 	%f212, 0f3F800000;
	mov.f32 	%f205, %f212;
	@%p7 bra 	$L__BB3_34;

	abs.f32 	%f101, %f13;
	setp.ge.f32 	%p8, %f101, 0f40400000;
	mov.f32 	%f205, 0f00000000;
	@%p8 bra 	$L__BB3_34;

	mul.f32 	%f14, %f13, 0f40490FDB;
	div.rn.f32 	%f15, %f14, 0f40400000;
	mul.f32 	%f102, %f14, 0f3F22F983;
	cvt.rni.s32.f32 	%r243, %f102;
	cvt.rn.f32.s32 	%f103, %r243;
	mov.f32 	%f104, 0fBFC90FDA;
	fma.rn.f32 	%f105, %f103, %f104, %f14;
	mov.f32 	%f106, 0fB3A22168;
	fma.rn.f32 	%f107, %f103, %f106, %f105;
	mov.f32 	%f108, 0fA7C234C5;
	fma.rn.f32 	%f199, %f103, %f108, %f107;
	abs.f32 	%f17, %f14;
	setp.ltu.f32 	%p9, %f17, 0f47CE4780;
	@%p9 bra 	$L__BB3_17;

	setp.eq.f32 	%p10, %f17, 0f7F800000;
	@%p10 bra 	$L__BB3_16;
	bra.uni 	$L__BB3_11;

$L__BB3_16:
	mov.f32 	%f111, 0f00000000;
	mul.rn.f32 	%f199, %f14, %f111;
	mov.u32 	%r243, 0;
	bra.uni 	$L__BB3_17;

$L__BB3_11:
	mov.b32 	%r23, %f14;
	bfe.u32 	%r120, %r23, 23, 8;
	add.s32 	%r24, %r120, -128;
	shl.b32 	%r121, %r23, 8;
	or.b32  	%r25, %r121, -2147483648;
	shr.u32 	%r26, %r24, 5;
	mov.u64 	%rd84, 0;
	mov.u32 	%r240, 0;
	mov.u64 	%rd83, __cudart_i2opi_f;
	mov.u64 	%rd82, %rd1;

$L__BB3_12:
	.pragma "nounroll";
	ld.global.nc.u32 	%r122, [%rd83];
	mad.wide.u32 	%rd36, %r122, %r25, %rd84;
	shr.u64 	%rd84, %rd36, 32;
	st.local.u32 	[%rd82], %rd36;
	add.s64 	%rd83, %rd83, 4;
	add.s64 	%rd82, %rd82, 4;
	add.s32 	%r240, %r240, 1;
	setp.ne.s32 	%p11, %r240, 6;
	@%p11 bra 	$L__BB3_12;

	st.local.u32 	[%rd8], %rd84;
	mov.u32 	%r123, 4;
	sub.s32 	%r29, %r123, %r26;
	mov.u32 	%r124, 6;
	sub.s32 	%r125, %r124, %r26;
	mul.wide.s32 	%rd37, %r125, 4;
	add.s64 	%rd38, %rd1, %rd37;
	ld.local.u32 	%r241, [%rd38];
	ld.local.u32 	%r242, [%rd38+-4];
	and.b32  	%r32, %r24, 31;
	setp.eq.s32 	%p12, %r32, 0;
	@%p12 bra 	$L__BB3_15;

	mov.u32 	%r126, 32;
	sub.s32 	%r127, %r126, %r32;
	shr.u32 	%r128, %r242, %r127;
	shl.b32 	%r129, %r241, %r32;
	add.s32 	%r241, %r128, %r129;
	mul.wide.s32 	%rd39, %r29, 4;
	add.s64 	%rd40, %rd1, %rd39;
	ld.local.u32 	%r130, [%rd40];
	shr.u32 	%r131, %r130, %r127;
	shl.b32 	%r132, %r242, %r32;
	add.s32 	%r242, %r131, %r132;

$L__BB3_15:
	and.b32  	%r133, %r23, -2147483648;
	shr.u32 	%r134, %r242, 30;
	shl.b32 	%r135, %r241, 2;
	or.b32  	%r136, %r134, %r135;
	shr.u32 	%r137, %r136, 31;
	shr.u32 	%r138, %r241, 30;
	add.s32 	%r139, %r137, %r138;
	neg.s32 	%r140, %r139;
	setp.eq.s32 	%p13, %r133, 0;
	selp.b32 	%r243, %r139, %r140, %p13;
	setp.ne.s32 	%p14, %r137, 0;
	xor.b32  	%r141, %r133, -2147483648;
	selp.b32 	%r142, %r141, %r133, %p14;
	selp.b32 	%r143, -1, 0, %p14;
	xor.b32  	%r144, %r136, %r143;
	shl.b32 	%r145, %r242, 2;
	xor.b32  	%r146, %r145, %r143;
	cvt.u64.u32 	%rd41, %r144;
	cvt.u64.u32 	%rd42, %r146;
	bfi.b64 	%rd43, %rd41, %rd42, 32, 32;
	cvt.rn.f64.s64 	%fd1, %rd43;
	mul.f64 	%fd2, %fd1, 0d3BF921FB54442D19;
	cvt.rn.f32.f64 	%f109, %fd2;
	setp.eq.s32 	%p15, %r142, 0;
	neg.f32 	%f110, %f109;
	selp.f32 	%f199, %f109, %f110, %p15;

$L__BB3_17:
	and.b32  	%r39, %r243, 1;
	setp.eq.s32 	%p16, %r39, 0;
	selp.f32 	%f21, %f199, 0f3F800000, %p16;
	mul.rn.f32 	%f22, %f199, %f199;
	mov.f32 	%f200, 0fB94D4153;
	@%p16 bra 	$L__BB3_19;

	mov.f32 	%f113, 0fBAB607ED;
	mov.f32 	%f114, 0f37CBAC00;
	fma.rn.f32 	%f200, %f114, %f22, %f113;

$L__BB3_19:
	selp.f32 	%f115, 0f3C0885E4, 0f3D2AAABB, %p16;
	fma.rn.f32 	%f116, %f200, %f22, %f115;
	selp.f32 	%f117, 0fBE2AAAA8, 0fBEFFFFFF, %p16;
	fma.rn.f32 	%f118, %f116, %f22, %f117;
	mov.f32 	%f119, 0f00000000;
	fma.rn.f32 	%f120, %f22, %f21, %f119;
	fma.rn.f32 	%f201, %f118, %f120, %f21;
	and.b32  	%r148, %r243, 2;
	setp.eq.s32 	%p18, %r148, 0;
	@%p18 bra 	$L__BB3_21;

	mov.f32 	%f122, 0fBF800000;
	fma.rn.f32 	%f201, %f201, %f122, %f119;

$L__BB3_21:
	mul.f32 	%f123, %f15, 0f3F22F983;
	cvt.rni.s32.f32 	%r246, %f123;
	cvt.rn.f32.s32 	%f124, %r246;
	mov.f32 	%f125, 0fBFC90FDA;
	fma.rn.f32 	%f126, %f124, %f125, %f15;
	mov.f32 	%f127, 0fB3A22168;
	fma.rn.f32 	%f128, %f124, %f127, %f126;
	mov.f32 	%f129, 0fA7C234C5;
	fma.rn.f32 	%f202, %f124, %f129, %f128;
	abs.f32 	%f29, %f15;
	setp.ltu.f32 	%p19, %f29, 0f47CE4780;
	@%p19 bra 	$L__BB3_29;

	setp.eq.f32 	%p20, %f29, 0f7F800000;
	@%p20 bra 	$L__BB3_28;
	bra.uni 	$L__BB3_23;

$L__BB3_28:
	mov.f32 	%f132, 0f00000000;
	mul.rn.f32 	%f202, %f15, %f132;
	mov.u32 	%r246, 0;
	bra.uni 	$L__BB3_29;

$L__BB3_23:
	mov.b32 	%r41, %f15;
	bfe.u32 	%r149, %r41, 23, 8;
	add.s32 	%r42, %r149, -128;
	shl.b32 	%r150, %r41, 8;
	or.b32  	%r43, %r150, -2147483648;
	shr.u32 	%r44, %r42, 5;
	mov.u64 	%rd85, 0;
	mov.u64 	%rd86, %rd85;

$L__BB3_24:
	.pragma "nounroll";
	shl.b64 	%rd46, %rd85, 2;
	mov.u64 	%rd47, __cudart_i2opi_f;
	add.s64 	%rd48, %rd47, %rd46;
	ld.global.nc.u32 	%r151, [%rd48];
	mad.wide.u32 	%rd49, %r151, %r43, %rd86;
	shr.u64 	%rd86, %rd49, 32;
	add.s64 	%rd50, %rd1, %rd46;
	st.local.u32 	[%rd50], %rd49;
	cvt.u32.u64 	%r152, %rd85;
	add.s32 	%r153, %r152, 1;
	cvt.s64.s32 	%rd85, %r153;
	setp.ne.s32 	%p21, %r153, 6;
	@%p21 bra 	$L__BB3_24;

	st.local.u32 	[%rd8], %rd86;
	mov.u32 	%r154, 4;
	sub.s32 	%r45, %r154, %r44;
	mov.u32 	%r155, 6;
	sub.s32 	%r156, %r155, %r44;
	mul.wide.s32 	%rd51, %r156, 4;
	add.s64 	%rd52, %rd1, %rd51;
	ld.local.u32 	%r244, [%rd52];
	ld.local.u32 	%r245, [%rd52+-4];
	and.b32  	%r48, %r42, 31;
	setp.eq.s32 	%p22, %r48, 0;
	@%p22 bra 	$L__BB3_27;

	mov.u32 	%r157, 32;
	sub.s32 	%r158, %r157, %r48;
	shr.u32 	%r159, %r245, %r158;
	shl.b32 	%r160, %r244, %r48;
	add.s32 	%r244, %r159, %r160;
	mul.wide.s32 	%rd53, %r45, 4;
	add.s64 	%rd54, %rd1, %rd53;
	ld.local.u32 	%r161, [%rd54];
	shr.u32 	%r162, %r161, %r158;
	shl.b32 	%r163, %r245, %r48;
	add.s32 	%r245, %r162, %r163;

$L__BB3_27:
	and.b32  	%r164, %r41, -2147483648;
	shr.u32 	%r165, %r245, 30;
	shl.b32 	%r166, %r244, 2;
	or.b32  	%r167, %r165, %r166;
	shr.u32 	%r168, %r167, 31;
	shr.u32 	%r169, %r244, 30;
	add.s32 	%r170, %r168, %r169;
	neg.s32 	%r171, %r170;
	setp.eq.s32 	%p23, %r164, 0;
	selp.b32 	%r246, %r170, %r171, %p23;
	setp.ne.s32 	%p24, %r168, 0;
	xor.b32  	%r172, %r164, -2147483648;
	selp.b32 	%r173, %r172, %r164, %p24;
	selp.b32 	%r174, -1, 0, %p24;
	xor.b32  	%r175, %r167, %r174;
	shl.b32 	%r176, %r245, 2;
	xor.b32  	%r177, %r176, %r174;
	cvt.u64.u32 	%rd55, %r175;
	cvt.u64.u32 	%rd56, %r177;
	bfi.b64 	%rd57, %rd55, %rd56, 32, 32;
	cvt.rn.f64.s64 	%fd3, %rd57;
	mul.f64 	%fd4, %fd3, 0d3BF921FB54442D19;
	cvt.rn.f32.f64 	%f130, %fd4;
	setp.eq.s32 	%p25, %r173, 0;
	neg.f32 	%f131, %f130;
	selp.f32 	%f202, %f130, %f131, %p25;

$L__BB3_29:
	and.b32  	%r55, %r246, 1;
	setp.eq.s32 	%p26, %r55, 0;
	selp.f32 	%f33, %f202, 0f3F800000, %p26;
	mul.rn.f32 	%f34, %f202, %f202;
	mov.f32 	%f203, 0fB94D4153;
	@%p26 bra 	$L__BB3_31;

	mov.f32 	%f134, 0fBAB607ED;
	mov.f32 	%f135, 0f37CBAC00;
	fma.rn.f32 	%f203, %f135, %f34, %f134;

$L__BB3_31:
	selp.f32 	%f136, 0f3C0885E4, 0f3D2AAABB, %p26;
	fma.rn.f32 	%f137, %f203, %f34, %f136;
	selp.f32 	%f138, 0fBE2AAAA8, 0fBEFFFFFF, %p26;
	fma.rn.f32 	%f139, %f137, %f34, %f138;
	mov.f32 	%f140, 0f00000000;
	fma.rn.f32 	%f141, %f34, %f33, %f140;
	fma.rn.f32 	%f204, %f139, %f141, %f33;
	and.b32  	%r179, %r246, 2;
	setp.eq.s32 	%p28, %r179, 0;
	@%p28 bra 	$L__BB3_33;

	mov.f32 	%f143, 0fBF800000;
	fma.rn.f32 	%f204, %f204, %f143, %f140;

$L__BB3_33:
	mul.f32 	%f144, %f14, %f14;
	mul.f32 	%f145, %f201, 0f40400000;
	mul.f32 	%f146, %f145, %f204;
	div.rn.f32 	%f205, %f146, %f144;

$L__BB3_34:
	setp.eq.f32 	%p29, %f5, 0f00000000;
	@%p29 bra 	$L__BB3_61;

	abs.f32 	%f149, %f5;
	setp.ge.f32 	%p30, %f149, 0f40400000;
	mov.f32 	%f212, 0f00000000;
	@%p30 bra 	$L__BB3_61;

	cvt.rni.s32.f32 	%r249, %f8;
	cvt.rn.f32.s32 	%f150, %r249;
	mov.f32 	%f151, 0fBFC90FDA;
	fma.rn.f32 	%f152, %f150, %f151, %f6;
	mov.f32 	%f153, 0fB3A22168;
	fma.rn.f32 	%f154, %f150, %f153, %f152;
	mov.f32 	%f155, 0fA7C234C5;
	fma.rn.f32 	%f206, %f150, %f155, %f154;
	abs.f32 	%f43, %f6;
	setp.ltu.f32 	%p31, %f43, 0f47CE4780;
	@%p31 bra 	$L__BB3_44;

	setp.eq.f32 	%p32, %f43, 0f7F800000;
	@%p32 bra 	$L__BB3_43;
	bra.uni 	$L__BB3_38;

$L__BB3_43:
	mov.f32 	%f158, 0f00000000;
	mul.rn.f32 	%f206, %f6, %f158;
	mov.u32 	%r249, 0;
	bra.uni 	$L__BB3_44;

$L__BB3_38:
	mov.u64 	%rd87, 0;
	mov.u64 	%rd88, %rd87;

$L__BB3_39:
	.pragma "nounroll";
	shl.b64 	%rd60, %rd87, 2;
	mov.u64 	%rd61, __cudart_i2opi_f;
	add.s64 	%rd62, %rd61, %rd60;
	ld.global.nc.u32 	%r180, [%rd62];
	mad.wide.u32 	%rd63, %r180, %r12, %rd88;
	shr.u64 	%rd88, %rd63, 32;
	add.s64 	%rd64, %rd1, %rd60;
	st.local.u32 	[%rd64], %rd63;
	cvt.u32.u64 	%r181, %rd87;
	add.s32 	%r182, %r181, 1;
	cvt.s64.s32 	%rd87, %r182;
	setp.ne.s32 	%p33, %r182, 6;
	@%p33 bra 	$L__BB3_39;

	setp.eq.s32 	%p34, %r13, 0;
	st.local.u32 	[%rd8], %rd88;
	ld.local.u32 	%r247, [%rd4];
	ld.local.u32 	%r248, [%rd4+-4];
	@%p34 bra 	$L__BB3_42;

	shl.b32 	%r183, %r247, %r13;
	shr.u32 	%r184, %r248, %r17;
	add.s32 	%r247, %r184, %r183;
	ld.local.u32 	%r185, [%rd5];
	shr.u32 	%r186, %r185, %r17;
	shl.b32 	%r187, %r248, %r13;
	add.s32 	%r248, %r186, %r187;

$L__BB3_42:
	shr.u32 	%r188, %r248, 30;
	shl.b32 	%r189, %r247, 2;
	or.b32  	%r190, %r188, %r189;
	shr.u32 	%r191, %r190, 31;
	shr.u32 	%r192, %r247, 30;
	add.s32 	%r193, %r191, %r192;
	neg.s32 	%r194, %r193;
	setp.eq.s32 	%p35, %r11, 0;
	selp.b32 	%r249, %r193, %r194, %p35;
	setp.ne.s32 	%p36, %r191, 0;
	selp.b32 	%r195, %r16, %r11, %p36;
	selp.b32 	%r196, -1, 0, %p36;
	xor.b32  	%r197, %r190, %r196;
	shl.b32 	%r198, %r248, 2;
	xor.b32  	%r199, %r198, %r196;
	cvt.u64.u32 	%rd65, %r197;
	cvt.u64.u32 	%rd66, %r199;
	bfi.b64 	%rd67, %rd65, %rd66, 32, 32;
	cvt.rn.f64.s64 	%fd5, %rd67;
	mul.f64 	%fd6, %fd5, 0d3BF921FB54442D19;
	cvt.rn.f32.f64 	%f156, %fd6;
	setp.eq.s32 	%p37, %r195, 0;
	neg.f32 	%f157, %f156;
	selp.f32 	%f206, %f156, %f157, %p37;

$L__BB3_44:
	and.b32  	%r65, %r249, 1;
	setp.eq.s32 	%p38, %r65, 0;
	selp.f32 	%f47, %f206, 0f3F800000, %p38;
	mul.rn.f32 	%f48, %f206, %f206;
	mov.f32 	%f207, 0fB94D4153;
	@%p38 bra 	$L__BB3_46;

	mov.f32 	%f160, 0fBAB607ED;
	mov.f32 	%f161, 0f37CBAC00;
	fma.rn.f32 	%f207, %f161, %f48, %f160;

$L__BB3_46:
	selp.f32 	%f162, 0f3C0885E4, 0f3D2AAABB, %p38;
	fma.rn.f32 	%f163, %f207, %f48, %f162;
	selp.f32 	%f164, 0fBE2AAAA8, 0fBEFFFFFF, %p38;
	fma.rn.f32 	%f165, %f163, %f48, %f164;
	mov.f32 	%f166, 0f00000000;
	fma.rn.f32 	%f167, %f48, %f47, %f166;
	fma.rn.f32 	%f208, %f165, %f167, %f47;
	and.b32  	%r201, %r249, 2;
	setp.eq.s32 	%p40, %r201, 0;
	@%p40 bra 	$L__BB3_48;

	mov.f32 	%f169, 0fBF800000;
	fma.rn.f32 	%f208, %f208, %f169, %f166;

$L__BB3_48:
	mul.f32 	%f194, %f7, 0f3F22F983;
	cvt.rni.s32.f32 	%r252, %f194;
	cvt.rn.f32.s32 	%f170, %r252;
	mov.f32 	%f171, 0fBFC90FDA;
	fma.rn.f32 	%f172, %f170, %f171, %f7;
	mov.f32 	%f173, 0fB3A22168;
	fma.rn.f32 	%f174, %f170, %f173, %f172;
	mov.f32 	%f175, 0fA7C234C5;
	fma.rn.f32 	%f209, %f170, %f175, %f174;
	abs.f32 	%f55, %f7;
	setp.ltu.f32 	%p41, %f55, 0f47CE4780;
	@%p41 bra 	$L__BB3_56;

	setp.eq.f32 	%p42, %f55, 0f7F800000;
	@%p42 bra 	$L__BB3_55;
	bra.uni 	$L__BB3_50;

$L__BB3_55:
	mov.f32 	%f178, 0f00000000;
	mul.rn.f32 	%f209, %f7, %f178;
	mov.u32 	%r252, 0;
	bra.uni 	$L__BB3_56;

$L__BB3_50:
	mov.u64 	%rd89, 0;
	mov.u64 	%rd90, %rd89;

$L__BB3_51:
	.pragma "nounroll";
	mov.b32 	%r229, %f7;
	shl.b32 	%r228, %r229, 8;
	or.b32  	%r227, %r228, -2147483648;
	shl.b64 	%rd70, %rd89, 2;
	mov.u64 	%rd71, __cudart_i2opi_f;
	add.s64 	%rd72, %rd71, %rd70;
	ld.global.nc.u32 	%r202, [%rd72];
	mad.wide.u32 	%rd73, %r202, %r227, %rd90;
	shr.u64 	%rd90, %rd73, 32;
	add.s64 	%rd74, %rd1, %rd70;
	st.local.u32 	[%rd74], %rd73;
	cvt.u32.u64 	%r203, %rd89;
	add.s32 	%r204, %r203, 1;
	cvt.s64.s32 	%rd89, %r204;
	setp.ne.s32 	%p43, %r204, 6;
	@%p43 bra 	$L__BB3_51;

	setp.eq.s32 	%p44, %r18, 0;
	st.local.u32 	[%rd8], %rd90;
	ld.local.u32 	%r250, [%rd6];
	ld.local.u32 	%r251, [%rd6+-4];
	@%p44 bra 	$L__BB3_54;

	shl.b32 	%r205, %r250, %r18;
	shr.u32 	%r206, %r251, %r20;
	add.s32 	%r250, %r206, %r205;
	ld.local.u32 	%r207, [%rd7];
	shr.u32 	%r208, %r207, %r20;
	shl.b32 	%r209, %r251, %r18;
	add.s32 	%r251, %r208, %r209;

$L__BB3_54:
	shr.u32 	%r210, %r251, 30;
	shl.b32 	%r211, %r250, 2;
	or.b32  	%r212, %r210, %r211;
	shr.u32 	%r213, %r212, 31;
	shr.u32 	%r214, %r250, 30;
	add.s32 	%r215, %r213, %r214;
	neg.s32 	%r216, %r215;
	setp.eq.s32 	%p45, %r14, 0;
	selp.b32 	%r252, %r215, %r216, %p45;
	setp.ne.s32 	%p46, %r213, 0;
	selp.b32 	%r217, %r19, %r14, %p46;
	selp.b32 	%r218, -1, 0, %p46;
	xor.b32  	%r219, %r212, %r218;
	shl.b32 	%r220, %r251, 2;
	xor.b32  	%r221, %r220, %r218;
	cvt.u64.u32 	%rd75, %r219;
	cvt.u64.u32 	%rd76, %r221;
	bfi.b64 	%rd77, %rd75, %rd76, 32, 32;
	cvt.rn.f64.s64 	%fd7, %rd77;
	mul.f64 	%fd8, %fd7, 0d3BF921FB54442D19;
	cvt.rn.f32.f64 	%f176, %fd8;
	setp.eq.s32 	%p47, %r217, 0;
	neg.f32 	%f177, %f176;
	selp.f32 	%f209, %f176, %f177, %p47;

$L__BB3_56:
	and.b32  	%r75, %r252, 1;
	setp.eq.s32 	%p48, %r75, 0;
	selp.f32 	%f59, %f209, 0f3F800000, %p48;
	mul.rn.f32 	%f60, %f209, %f209;
	mov.f32 	%f210, 0fB94D4153;
	@%p48 bra 	$L__BB3_58;

	mov.f32 	%f180, 0fBAB607ED;
	mov.f32 	%f181, 0f37CBAC00;
	fma.rn.f32 	%f210, %f181, %f60, %f180;

$L__BB3_58:
	selp.f32 	%f182, 0f3C0885E4, 0f3D2AAABB, %p48;
	fma.rn.f32 	%f183, %f210, %f60, %f182;
	selp.f32 	%f184, 0fBE2AAAA8, 0fBEFFFFFF, %p48;
	fma.rn.f32 	%f185, %f183, %f60, %f184;
	mov.f32 	%f186, 0f00000000;
	fma.rn.f32 	%f187, %f60, %f59, %f186;
	fma.rn.f32 	%f211, %f185, %f187, %f59;
	and.b32  	%r223, %r252, 2;
	setp.eq.s32 	%p50, %r223, 0;
	@%p50 bra 	$L__BB3_60;

	mov.f32 	%f189, 0fBF800000;
	fma.rn.f32 	%f211, %f211, %f189, %f186;

$L__BB3_60:
	mul.f32 	%f190, %f208, 0f40400000;
	mul.f32 	%f191, %f190, %f211;
	div.rn.f32 	%f212, %f191, %f10;

$L__BB3_61:
	mul.f32 	%f68, %f205, %f212;
	setp.eq.f32 	%p51, %f68, 0f00000000;
	@%p51 bra 	$L__BB3_63;

	add.s32 	%r224, %r239, %r10;
	mul.wide.s32 	%rd78, %r224, 4;
	add.s64 	%rd79, %rd3, %rd78;
	ld.global.f32 	%f192, [%rd79];
	fma.rn.f32 	%f213, %f68, %f192, %f213;
	add.f32 	%f214, %f214, %f68;

$L__BB3_63:
	add.s32 	%r76, %r239, 1;
	setp.lt.s32 	%p52, %r239, %r4;
	mov.u32 	%r239, %r76;
	@%p52 bra 	$L__BB3_7;

$L__BB3_64:
	add.s32 	%r77, %r238, 1;
	setp.lt.s32 	%p53, %r238, %r6;
	mov.u32 	%r238, %r77;
	@%p53 bra 	$L__BB3_5;

$L__BB3_65:
	setp.leu.f32 	%p54, %f214, 0f00000000;
	@%p54 bra 	$L__BB3_67;

	div.rn.f32 	%f219, %f213, %f214;

$L__BB3_67:
	ld.param.u32 	%r232, [LanczosResizeDownKernel_param_6];
	ld.param.u32 	%r231, [LanczosResizeDownKernel_param_2];
	ld.param.u32 	%r230, [LanczosResizeDownKernel_param_1];
	mad.lo.s32 	%r225, %r237, %r231, %r2;
	mad.lo.s32 	%r226, %r225, %r230, %r1;
	mul.wide.s32 	%rd80, %r226, 4;
	add.s64 	%rd81, %rd2, %rd80;
	st.global.f32 	[%rd81], %f219;
	add.s32 	%r237, %r237, 1;
	setp.lt.s32 	%p55, %r237, %r232;
	@%p55 bra 	$L__BB3_3;

$L__BB3_68:
	ret;

}
	// .globl	MitchellResizeDownKernel
.visible .entry MitchellResizeDownKernel(
	.param .u64 MitchellResizeDownKernel_param_0,
	.param .u32 MitchellResizeDownKernel_param_1,
	.param .u32 MitchellResizeDownKernel_param_2,
	.param .u64 MitchellResizeDownKernel_param_3,
	.param .u32 MitchellResizeDownKernel_param_4,
	.param .u32 MitchellResizeDownKernel_param_5,
	.param .u32 MitchellResizeDownKernel_param_6
)
{
	.reg .pred 	%p<40>;
	.reg .f32 	%f<271>;
	.reg .b32 	%r<62>;
	.reg .b64 	%rd<14>;


	ld.param.u64 	%rd7, [MitchellResizeDownKernel_param_0];
	ld.param.u32 	%r25, [MitchellResizeDownKernel_param_1];
	ld.param.u32 	%r26, [MitchellResizeDownKernel_param_2];
	ld.param.u64 	%rd8, [MitchellResizeDownKernel_param_3];
	ld.param.u32 	%r27, [MitchellResizeDownKernel_param_4];
	ld.param.u32 	%r28, [MitchellResizeDownKernel_param_5];
	ld.param.u32 	%r29, [MitchellResizeDownKernel_param_6];
	cvta.to.global.u64 	%rd1, %rd8;
	mov.u32 	%r30, %ntid.x;
	mov.u32 	%r31, %ctaid.x;
	mov.u32 	%r32, %tid.x;
	mad.lo.s32 	%r1, %r31, %r30, %r32;
	mov.u32 	%r33, %ntid.y;
	mov.u32 	%r34, %ctaid.y;
	mov.u32 	%r35, %tid.y;
	mad.lo.s32 	%r2, %r34, %r33, %r35;
	setp.ge.s32 	%p1, %r1, %r25;
	setp.ge.s32 	%p2, %r2, %r26;
	or.pred  	%p3, %p1, %p2;
	@%p3 bra 	$L__BB4_58;

	cvt.rn.f32.s32 	%f86, %r27;
	cvt.rn.f32.s32 	%f87, %r25;
	div.rn.f32 	%f88, %f86, %f87;
	cvt.rn.f32.s32 	%f89, %r26;
	cvt.rn.f32.s32 	%f90, %r28;
	div.rn.f32 	%f91, %f90, %f89;
	cvt.rn.f32.s32 	%f92, %r1;
	add.f32 	%f93, %f92, 0f3F000000;
	fma.rn.f32 	%f1, %f93, %f88, 0fBF000000;
	cvt.rn.f32.s32 	%f94, %r2;
	add.f32 	%f95, %f94, 0f3F000000;
	fma.rn.f32 	%f2, %f95, %f91, 0fBF000000;
	cvt.rmi.f32.f32 	%f96, %f1;
	cvt.rzi.s32.f32 	%r36, %f96;
	add.s32 	%r37, %r36, -1;
	max.s32 	%r3, %r37, 0;
	cvt.rpi.f32.f32 	%f97, %f1;
	cvt.rzi.s32.f32 	%r4, %f97;
	add.s32 	%r38, %r4, 1;
	add.s32 	%r39, %r27, -1;
	min.s32 	%r5, %r39, %r38;
	cvt.rmi.f32.f32 	%f98, %f2;
	cvt.rzi.s32.f32 	%r40, %f98;
	add.s32 	%r41, %r40, -1;
	max.s32 	%r6, %r41, 0;
	cvt.rpi.f32.f32 	%f99, %f2;
	cvt.rzi.s32.f32 	%r42, %f99;
	add.s32 	%r43, %r42, 1;
	add.s32 	%r44, %r28, -1;
	min.s32 	%r7, %r44, %r43;
	setp.lt.s32 	%p4, %r29, 1;
	@%p4 bra 	$L__BB4_58;

	neg.s32 	%r46, %r3;
	mov.u32 	%r58, 0;
	mov.u32 	%r47, -2;
	sub.s32 	%r48, %r47, %r4;
	neg.s32 	%r49, %r27;
	max.s32 	%r50, %r48, %r49;
	sub.s32 	%r51, %r46, %r50;
	not.b32 	%r52, %r3;
	sub.s32 	%r8, %r52, %r50;
	and.b32  	%r9, %r51, 3;
	cvt.rn.f32.s32 	%f100, %r3;
	sub.f32 	%f3, %f1, %f100;
	add.s32 	%r10, %r3, 1;
	cvt.rn.f32.s32 	%f101, %r10;
	sub.f32 	%f4, %f1, %f101;
	add.s32 	%r11, %r3, 2;
	cvt.rn.f32.s32 	%f102, %r11;
	sub.f32 	%f5, %f1, %f102;
	add.s32 	%r12, %r3, 3;
	cvta.to.global.u64 	%rd2, %rd7;
	abs.f32 	%f11, %f3;
	abs.f32 	%f21, %f4;
	abs.f32 	%f30, %f5;

$L__BB4_3:
	setp.gt.s32 	%p5, %r6, %r7;
	mov.f32 	%f270, 0f00000000;
	mov.f32 	%f255, %f270;
	mov.f32 	%f256, %f270;
	@%p5 bra 	$L__BB4_55;

	mul.lo.s32 	%r14, %r58, %r28;
	mov.f32 	%f256, 0f00000000;
	mov.u32 	%r59, %r6;
	mov.f32 	%f255, %f256;

$L__BB4_5:
	mov.u32 	%r15, %r59;
	setp.gt.s32 	%p6, %r3, %r5;
	@%p6 bra 	$L__BB4_54;

	setp.eq.s32 	%p7, %r9, 0;
	cvt.rn.f32.s32 	%f108, %r15;
	sub.f32 	%f109, %f2, %f108;
	abs.f32 	%f8, %f109;
	mul.f32 	%f110, %f8, 0f40E00000;
	mul.f32 	%f111, %f8, %f110;
	mul.f32 	%f112, %f8, %f111;
	mul.f32 	%f113, %f8, 0fC1400000;
	fma.rn.f32 	%f114, %f8, %f113, %f112;
	add.f32 	%f115, %f114, 0f40AAAAAB;
	div.rn.f32 	%f9, %f115, 0f40C00000;
	mul.f32 	%f116, %f8, 0fC0155555;
	mul.f32 	%f117, %f8, %f116;
	mul.f32 	%f118, %f8, %f117;
	mul.f32 	%f119, %f8, 0f41400000;
	fma.rn.f32 	%f120, %f8, %f119, %f118;
	fma.rn.f32 	%f121, %f8, 0fC1A00000, %f120;
	add.f32 	%f122, %f121, 0f412AAAAB;
	div.rn.f32 	%f10, %f122, 0f40C00000;
	add.s32 	%r53, %r15, %r14;
	mul.lo.s32 	%r16, %r53, %r27;
	mov.u32 	%r60, %r3;
	@%p7 bra 	$L__BB4_27;

	setp.ge.f32 	%p8, %f11, 0f40000000;
	mov.f32 	%f241, 0f00000000;
	@%p8 bra 	$L__BB4_11;

	setp.lt.f32 	%p9, %f11, 0f3F800000;
	@%p9 bra 	$L__BB4_10;
	bra.uni 	$L__BB4_9;

$L__BB4_10:
	mul.f32 	%f131, %f11, 0f40E00000;
	mul.f32 	%f132, %f11, %f131;
	mul.f32 	%f133, %f11, %f132;
	mul.f32 	%f134, %f11, 0fC1400000;
	fma.rn.f32 	%f135, %f11, %f134, %f133;
	add.f32 	%f136, %f135, 0f40AAAAAB;
	div.rn.f32 	%f241, %f136, 0f40C00000;
	bra.uni 	$L__BB4_11;

$L__BB4_9:
	mul.f32 	%f124, %f11, 0fC0155555;
	mul.f32 	%f125, %f11, %f124;
	mul.f32 	%f126, %f11, %f125;
	mul.f32 	%f127, %f11, 0f41400000;
	fma.rn.f32 	%f128, %f11, %f127, %f126;
	fma.rn.f32 	%f129, %f11, 0fC1A00000, %f128;
	add.f32 	%f130, %f129, 0f412AAAAB;
	div.rn.f32 	%f241, %f130, 0f40C00000;

$L__BB4_11:
	setp.lt.f32 	%p10, %f8, 0f3F800000;
	selp.f32 	%f137, %f9, %f10, %p10;
	setp.ltu.f32 	%p11, %f8, 0f40000000;
	selp.f32 	%f15, %f137, 0f00000000, %p11;
	mul.f32 	%f16, %f241, %f15;
	setp.eq.f32 	%p12, %f16, 0f00000000;
	add.s32 	%r54, %r3, %r16;
	mul.wide.s32 	%rd9, %r54, 4;
	add.s64 	%rd3, %rd1, %rd9;
	@%p12 bra 	$L__BB4_13;

	ld.global.f32 	%f138, [%rd3];
	fma.rn.f32 	%f255, %f16, %f138, %f255;
	add.f32 	%f256, %f256, %f16;

$L__BB4_13:
	setp.eq.s32 	%p13, %r9, 1;
	mov.u32 	%r60, %r10;
	@%p13 bra 	$L__BB4_27;

	setp.ge.f32 	%p14, %f21, 0f40000000;
	mov.f32 	%f244, 0f00000000;
	@%p14 bra 	$L__BB4_18;

	setp.lt.f32 	%p15, %f21, 0f3F800000;
	@%p15 bra 	$L__BB4_17;
	bra.uni 	$L__BB4_16;

$L__BB4_17:
	mul.f32 	%f147, %f21, 0f40E00000;
	mul.f32 	%f148, %f21, %f147;
	mul.f32 	%f149, %f21, %f148;
	mul.f32 	%f150, %f21, 0fC1400000;
	fma.rn.f32 	%f151, %f21, %f150, %f149;
	add.f32 	%f152, %f151, 0f40AAAAAB;
	div.rn.f32 	%f244, %f152, 0f40C00000;
	bra.uni 	$L__BB4_18;

$L__BB4_16:
	mul.f32 	%f140, %f21, 0fC0155555;
	mul.f32 	%f141, %f21, %f140;
	mul.f32 	%f142, %f21, %f141;
	mul.f32 	%f143, %f21, 0f41400000;
	fma.rn.f32 	%f144, %f21, %f143, %f142;
	fma.rn.f32 	%f145, %f21, 0fC1A00000, %f144;
	add.f32 	%f146, %f145, 0f412AAAAB;
	div.rn.f32 	%f244, %f146, 0f40C00000;

$L__BB4_18:
	mul.f32 	%f25, %f244, %f15;
	setp.eq.f32 	%p16, %f25, 0f00000000;
	@%p16 bra 	$L__BB4_20;

	ld.global.f32 	%f153, [%rd3+4];
	fma.rn.f32 	%f255, %f25, %f153, %f255;
	add.f32 	%f256, %f256, %f25;

$L__BB4_20:
	setp.eq.s32 	%p17, %r9, 2;
	mov.u32 	%r60, %r11;
	@%p17 bra 	$L__BB4_27;

	setp.ge.f32 	%p18, %f30, 0f40000000;
	mov.f32 	%f247, 0f00000000;
	@%p18 bra 	$L__BB4_25;

	setp.lt.f32 	%p19, %f30, 0f3F800000;
	@%p19 bra 	$L__BB4_24;
	bra.uni 	$L__BB4_23;

$L__BB4_24:
	mul.f32 	%f162, %f30, 0f40E00000;
	mul.f32 	%f163, %f30, %f162;
	mul.f32 	%f164, %f30, %f163;
	mul.f32 	%f165, %f30, 0fC1400000;
	fma.rn.f32 	%f166, %f30, %f165, %f164;
	add.f32 	%f167, %f166, 0f40AAAAAB;
	div.rn.f32 	%f247, %f167, 0f40C00000;
	bra.uni 	$L__BB4_25;

$L__BB4_23:
	mul.f32 	%f155, %f30, 0fC0155555;
	mul.f32 	%f156, %f30, %f155;
	mul.f32 	%f157, %f30, %f156;
	mul.f32 	%f158, %f30, 0f41400000;
	fma.rn.f32 	%f159, %f30, %f158, %f157;
	fma.rn.f32 	%f160, %f30, 0fC1A00000, %f159;
	add.f32 	%f161, %f160, 0f412AAAAB;
	div.rn.f32 	%f247, %f161, 0f40C00000;

$L__BB4_25:
	mul.f32 	%f34, %f247, %f15;
	setp.eq.f32 	%p20, %f34, 0f00000000;
	mov.u32 	%r60, %r12;
	@%p20 bra 	$L__BB4_27;

	ld.global.f32 	%f168, [%rd3+8];
	fma.rn.f32 	%f255, %f34, %f168, %f255;
	add.f32 	%f256, %f256, %f34;
	mov.u32 	%r60, %r12;

$L__BB4_27:
	setp.lt.u32 	%p21, %r8, 3;
	@%p21 bra 	$L__BB4_54;

	setp.lt.f32 	%p22, %f8, 0f3F800000;
	selp.f32 	%f169, %f9, %f10, %p22;
	setp.ltu.f32 	%p23, %f8, 0f40000000;
	selp.f32 	%f41, %f169, 0f00000000, %p23;
	add.s32 	%r55, %r60, %r16;
	mul.wide.s32 	%rd10, %r55, 4;
	add.s64 	%rd13, %rd1, %rd10;

$L__BB4_29:
	cvt.rn.f32.s32 	%f171, %r60;
	sub.f32 	%f172, %f1, %f171;
	abs.f32 	%f44, %f172;
	setp.ge.f32 	%p24, %f44, 0f40000000;
	mov.f32 	%f254, 0f00000000;
	@%p24 bra 	$L__BB4_33;

	setp.lt.f32 	%p25, %f44, 0f3F800000;
	@%p25 bra 	$L__BB4_32;
	bra.uni 	$L__BB4_31;

$L__BB4_32:
	mul.f32 	%f180, %f44, 0f40E00000;
	mul.f32 	%f181, %f44, %f180;
	mul.f32 	%f182, %f44, %f181;
	mul.f32 	%f183, %f44, 0fC1400000;
	fma.rn.f32 	%f184, %f44, %f183, %f182;
	add.f32 	%f185, %f184, 0f40AAAAAB;
	div.rn.f32 	%f254, %f185, 0f40C00000;
	bra.uni 	$L__BB4_33;

$L__BB4_31:
	mul.f32 	%f173, %f44, 0fC0155555;
	mul.f32 	%f174, %f44, %f173;
	mul.f32 	%f175, %f44, %f174;
	mul.f32 	%f176, %f44, 0f41400000;
	fma.rn.f32 	%f177, %f44, %f176, %f175;
	fma.rn.f32 	%f178, %f44, 0fC1A00000, %f177;
	add.f32 	%f179, %f178, 0f412AAAAB;
	div.rn.f32 	%f254, %f179, 0f40C00000;

$L__BB4_33:
	mul.f32 	%f48, %f254, %f41;
	setp.eq.f32 	%p26, %f48, 0f00000000;
	@%p26 bra 	$L__BB4_35;

	ld.global.f32 	%f186, [%rd13];
	fma.rn.f32 	%f255, %f48, %f186, %f255;
	add.f32 	%f256, %f256, %f48;

$L__BB4_35:
	add.s32 	%r19, %r60, 1;
	cvt.rn.f32.s32 	%f188, %r19;
	sub.f32 	%f189, %f1, %f188;
	abs.f32 	%f53, %f189;
	setp.ge.f32 	%p27, %f53, 0f40000000;
	mov.f32 	%f257, 0f00000000;
	@%p27 bra 	$L__BB4_39;

	setp.lt.f32 	%p28, %f53, 0f3F800000;
	@%p28 bra 	$L__BB4_38;
	bra.uni 	$L__BB4_37;

$L__BB4_38:
	mul.f32 	%f197, %f53, 0f40E00000;
	mul.f32 	%f198, %f53, %f197;
	mul.f32 	%f199, %f53, %f198;
	mul.f32 	%f200, %f53, 0fC1400000;
	fma.rn.f32 	%f201, %f53, %f200, %f199;
	add.f32 	%f202, %f201, 0f40AAAAAB;
	div.rn.f32 	%f257, %f202, 0f40C00000;
	bra.uni 	$L__BB4_39;

$L__BB4_37:
	mul.f32 	%f190, %f53, 0fC0155555;
	mul.f32 	%f191, %f53, %f190;
	mul.f32 	%f192, %f53, %f191;
	mul.f32 	%f193, %f53, 0f41400000;
	fma.rn.f32 	%f194, %f53, %f193, %f192;
	fma.rn.f32 	%f195, %f53, 0fC1A00000, %f194;
	add.f32 	%f196, %f195, 0f412AAAAB;
	div.rn.f32 	%f257, %f196, 0f40C00000;

$L__BB4_39:
	mul.f32 	%f57, %f257, %f41;
	setp.eq.f32 	%p29, %f57, 0f00000000;
	@%p29 bra 	$L__BB4_41;

	ld.global.f32 	%f203, [%rd13+4];
	fma.rn.f32 	%f255, %f57, %f203, %f255;
	add.f32 	%f256, %f256, %f57;

$L__BB4_41:
	add.s32 	%r20, %r19, 1;
	cvt.rn.f32.s32 	%f205, %r20;
	sub.f32 	%f206, %f1, %f205;
	abs.f32 	%f62, %f206;
	setp.ge.f32 	%p30, %f62, 0f40000000;
	mov.f32 	%f260, 0f00000000;
	@%p30 bra 	$L__BB4_45;

	setp.lt.f32 	%p31, %f62, 0f3F800000;
	@%p31 bra 	$L__BB4_44;
	bra.uni 	$L__BB4_43;

$L__BB4_44:
	mul.f32 	%f214, %f62, 0f40E00000;
	mul.f32 	%f215, %f62, %f214;
	mul.f32 	%f216, %f62, %f215;
	mul.f32 	%f217, %f62, 0fC1400000;
	fma.rn.f32 	%f218, %f62, %f217, %f216;
	add.f32 	%f219, %f218, 0f40AAAAAB;
	div.rn.f32 	%f260, %f219, 0f40C00000;
	bra.uni 	$L__BB4_45;

$L__BB4_43:
	mul.f32 	%f207, %f62, 0fC0155555;
	mul.f32 	%f208, %f62, %f207;
	mul.f32 	%f209, %f62, %f208;
	mul.f32 	%f210, %f62, 0f41400000;
	fma.rn.f32 	%f211, %f62, %f210, %f209;
	fma.rn.f32 	%f212, %f62, 0fC1A00000, %f211;
	add.f32 	%f213, %f212, 0f412AAAAB;
	div.rn.f32 	%f260, %f213, 0f40C00000;

$L__BB4_45:
	mul.f32 	%f66, %f260, %f41;
	setp.eq.f32 	%p32, %f66, 0f00000000;
	@%p32 bra 	$L__BB4_47;

	ld.global.f32 	%f220, [%rd13+8];
	fma.rn.f32 	%f255, %f66, %f220, %f255;
	add.f32 	%f256, %f256, %f66;

$L__BB4_47:
	add.s32 	%r21, %r20, 1;
	cvt.rn.f32.s32 	%f222, %r21;
	sub.f32 	%f223, %f1, %f222;
	abs.f32 	%f71, %f223;
	setp.ge.f32 	%p33, %f71, 0f40000000;
	mov.f32 	%f263, 0f00000000;
	@%p33 bra 	$L__BB4_51;

	setp.lt.f32 	%p34, %f71, 0f3F800000;
	@%p34 bra 	$L__BB4_50;
	bra.uni 	$L__BB4_49;

$L__BB4_50:
	mul.f32 	%f231, %f71, 0f40E00000;
	mul.f32 	%f232, %f71, %f231;
	mul.f32 	%f233, %f71, %f232;
	mul.f32 	%f234, %f71, 0fC1400000;
	fma.rn.f32 	%f235, %f71, %f234, %f233;
	add.f32 	%f236, %f235, 0f40AAAAAB;
	div.rn.f32 	%f263, %f236, 0f40C00000;
	bra.uni 	$L__BB4_51;

$L__BB4_49:
	mul.f32 	%f224, %f71, 0fC0155555;
	mul.f32 	%f225, %f71, %f224;
	mul.f32 	%f226, %f71, %f225;
	mul.f32 	%f227, %f71, 0f41400000;
	fma.rn.f32 	%f228, %f71, %f227, %f226;
	fma.rn.f32 	%f229, %f71, 0fC1A00000, %f228;
	add.f32 	%f230, %f229, 0f412AAAAB;
	div.rn.f32 	%f263, %f230, 0f40C00000;

$L__BB4_51:
	mul.f32 	%f75, %f263, %f41;
	setp.eq.f32 	%p35, %f75, 0f00000000;
	@%p35 bra 	$L__BB4_53;

	ld.global.f32 	%f237, [%rd13+12];
	fma.rn.f32 	%f255, %f75, %f237, %f255;
	add.f32 	%f256, %f256, %f75;

$L__BB4_53:
	add.s64 	%rd13, %rd13, 16;
	add.s32 	%r60, %r21, 1;
	setp.lt.s32 	%p36, %r21, %r5;
	@%p36 bra 	$L__BB4_29;

$L__BB4_54:
	add.s32 	%r59, %r15, 1;
	setp.lt.s32 	%p37, %r15, %r7;
	@%p37 bra 	$L__BB4_5;

$L__BB4_55:
	setp.leu.f32 	%p38, %f256, 0f00000000;
	@%p38 bra 	$L__BB4_57;

	div.rn.f32 	%f270, %f255, %f256;

$L__BB4_57:
	mad.lo.s32 	%r56, %r58, %r26, %r2;
	mad.lo.s32 	%r57, %r56, %r25, %r1;
	mul.wide.s32 	%rd11, %r57, 4;
	add.s64 	%rd12, %rd2, %rd11;
	st.global.f32 	[%rd12], %f270;
	add.s32 	%r58, %r58, 1;
	setp.lt.s32 	%p39, %r58, %r29;
	@%p39 bra 	$L__BB4_3;

$L__BB4_58:
	ret;

}
	// .globl	LanczosResizeAndPadKernel
.visible .entry LanczosResizeAndPadKernel(
	.param .u64 LanczosResizeAndPadKernel_param_0,
	.param .u32 LanczosResizeAndPadKernel_param_1,
	.param .u32 LanczosResizeAndPadKernel_param_2,
	.param .u64 LanczosResizeAndPadKernel_param_3,
	.param .u32 LanczosResizeAndPadKernel_param_4,
	.param .u32 LanczosResizeAndPadKernel_param_5,
	.param .f32 LanczosResizeAndPadKernel_param_6
)
{
	.local .align 4 .b8 	__local_depot5[28];
	.reg .b64 	%SP;
	.reg .b64 	%SPL;
	.reg .pred 	%p<65>;
	.reg .f32 	%f<232>;
	.reg .b32 	%r<257>;
	.reg .f64 	%fd<9>;
	.reg .b64 	%rd<91>;


	mov.u64 	%SPL, __local_depot5;
	ld.param.u64 	%rd23, [LanczosResizeAndPadKernel_param_0];
	ld.param.u32 	%r84, [LanczosResizeAndPadKernel_param_1];
	ld.param.u32 	%r85, [LanczosResizeAndPadKernel_param_2];
	ld.param.u64 	%rd24, [LanczosResizeAndPadKernel_param_3];
	ld.param.u32 	%r86, [LanczosResizeAndPadKernel_param_4];
	ld.param.u32 	%r87, [LanczosResizeAndPadKernel_param_5];
	ld.param.f32 	%f231, [LanczosResizeAndPadKernel_param_6];
	add.u64 	%rd1, %SPL, 0;
	mov.u32 	%r88, %ntid.x;
	mov.u32 	%r89, %ctaid.x;
	mov.u32 	%r90, %tid.x;
	mad.lo.s32 	%r1, %r89, %r88, %r90;
	mov.u32 	%r91, %ntid.y;
	mov.u32 	%r92, %ctaid.y;
	mov.u32 	%r93, %tid.y;
	mad.lo.s32 	%r2, %r92, %r91, %r93;
	mov.u32 	%r94, %ntid.z;
	mov.u32 	%r95, %ctaid.z;
	mov.u32 	%r96, %tid.z;
	mad.lo.s32 	%r3, %r95, %r94, %r96;
	setp.ge.s32 	%p1, %r1, %r84;
	setp.ge.s32 	%p2, %r2, %r85;
	or.pred  	%p3, %p1, %p2;
	setp.gt.s32 	%p4, %r3, 2;
	or.pred  	%p5, %p4, %p3;
	@%p5 bra 	$L__BB5_68;

	cvt.rn.f32.s32 	%f80, %r84;
	cvt.rn.f32.s32 	%f81, %r86;
	div.rn.f32 	%f82, %f80, %f81;
	cvt.rn.f32.s32 	%f83, %r87;
	cvt.rn.f32.s32 	%f84, %r85;
	div.rn.f32 	%f85, %f84, %f83;
	min.f32 	%f86, %f82, %f85;
	fma.rn.f32 	%f87, %f86, %f81, 0f3F000000;
	cvt.rzi.s32.f32 	%r4, %f87;
	fma.rn.f32 	%f88, %f86, %f83, 0f3F000000;
	cvt.rzi.s32.f32 	%r5, %f88;
	sub.s32 	%r97, %r84, %r4;
	shr.u32 	%r98, %r97, 31;
	add.s32 	%r99, %r97, %r98;
	shr.s32 	%r6, %r99, 1;
	sub.s32 	%r100, %r85, %r5;
	shr.u32 	%r101, %r100, 31;
	add.s32 	%r102, %r100, %r101;
	shr.s32 	%r7, %r102, 1;
	setp.lt.s32 	%p6, %r1, %r6;
	add.s32 	%r103, %r6, %r4;
	setp.ge.s32 	%p7, %r1, %r103;
	or.pred  	%p8, %p6, %p7;
	setp.lt.s32 	%p9, %r2, %r7;
	or.pred  	%p10, %p9, %p8;
	add.s32 	%r104, %r7, %r5;
	setp.ge.s32 	%p11, %r2, %r104;
	or.pred  	%p12, %p11, %p10;
	mad.lo.s32 	%r105, %r3, %r85, %r2;
	mad.lo.s32 	%r106, %r105, %r84, %r1;
	cvta.to.global.u64 	%rd26, %rd23;
	mul.wide.s32 	%rd27, %r106, 4;
	add.s64 	%rd2, %rd26, %rd27;
	@%p12 bra 	$L__BB5_67;
	bra.uni 	$L__BB5_2;

$L__BB5_67:
	st.global.f32 	[%rd2], %f231;
	bra.uni 	$L__BB5_68;

$L__BB5_2:
	sub.s32 	%r107, %r1, %r6;
	cvt.rn.f32.s32 	%f91, %r107;
	add.s32 	%r108, %r86, -1;
	cvt.rn.f32.s32 	%f92, %r108;
	mul.f32 	%f93, %f92, %f91;
	add.s32 	%r109, %r4, -1;
	cvt.rn.f32.s32 	%f94, %r109;
	div.rn.f32 	%f1, %f93, %f94;
	sub.s32 	%r110, %r2, %r7;
	cvt.rn.f32.s32 	%f95, %r110;
	add.s32 	%r111, %r87, -1;
	cvt.rn.f32.s32 	%f96, %r111;
	mul.f32 	%f97, %f96, %f95;
	add.s32 	%r112, %r5, -1;
	cvt.rn.f32.s32 	%f98, %r112;
	div.rn.f32 	%f2, %f97, %f98;
	add.f32 	%f99, %f1, 0fC0400000;
	add.f32 	%f100, %f99, 0f3F000000;
	cvt.rmi.f32.f32 	%f101, %f100;
	cvt.rzi.s32.f32 	%r113, %f101;
	max.s32 	%r8, %r113, 0;
	add.f32 	%f102, %f1, 0f40400000;
	add.f32 	%f103, %f102, 0f3F000000;
	cvt.rmi.f32.f32 	%f104, %f103;
	cvt.rzi.s32.f32 	%r114, %f104;
	min.s32 	%r9, %r108, %r114;
	add.f32 	%f105, %f2, 0fC0400000;
	add.f32 	%f106, %f105, 0f3F000000;
	cvt.rmi.f32.f32 	%f107, %f106;
	cvt.rzi.s32.f32 	%r115, %f107;
	max.s32 	%r242, %r115, 0;
	add.f32 	%f108, %f2, 0f40400000;
	add.f32 	%f109, %f108, 0f3F000000;
	cvt.rmi.f32.f32 	%f110, %f109;
	cvt.rzi.s32.f32 	%r116, %f110;
	min.s32 	%r11, %r111, %r116;
	setp.gt.s32 	%p13, %r242, %r11;
	mov.f32 	%f225, 0f00000000;
	mov.f32 	%f226, %f225;
	@%p13 bra 	$L__BB5_64;

	mul.lo.s32 	%r12, %r3, %r87;
	cvta.to.global.u64 	%rd3, %rd24;
	mov.f32 	%f226, 0f00000000;
	add.s64 	%rd4, %rd1, 24;
	mov.u64 	%rd28, __cudart_i2opi_f;
	mov.f32 	%f225, %f226;

$L__BB5_4:
	cvt.rn.f32.s32 	%f114, %r242;
	sub.f32 	%f5, %f2, %f114;
	abs.f32 	%f6, %f5;
	setp.lt.f32 	%p14, %f6, 0f358637BD;
	mov.f32 	%f215, 0f3F800000;
	@%p14 bra 	$L__BB5_31;

	setp.ge.f32 	%p15, %f6, 0f40400000;
	mov.f32 	%f215, 0f00000000;
	@%p15 bra 	$L__BB5_31;

	mul.f32 	%f7, %f5, 0f40490FDB;
	div.rn.f32 	%f8, %f7, 0f40400000;
	mul.f32 	%f116, %f7, 0f3F22F983;
	cvt.rni.s32.f32 	%r246, %f116;
	cvt.rn.f32.s32 	%f117, %r246;
	mov.f32 	%f118, 0fBFC90FDA;
	fma.rn.f32 	%f119, %f117, %f118, %f7;
	mov.f32 	%f120, 0fB3A22168;
	fma.rn.f32 	%f121, %f117, %f120, %f119;
	mov.f32 	%f122, 0fA7C234C5;
	fma.rn.f32 	%f209, %f117, %f122, %f121;
	abs.f32 	%f10, %f7;
	setp.ltu.f32 	%p16, %f10, 0f47CE4780;
	@%p16 bra 	$L__BB5_14;

	setp.eq.f32 	%p17, %f10, 0f7F800000;
	@%p17 bra 	$L__BB5_13;
	bra.uni 	$L__BB5_8;

$L__BB5_13:
	mov.f32 	%f125, 0f00000000;
	mul.rn.f32 	%f209, %f7, %f125;
	mov.u32 	%r246, 0;
	bra.uni 	$L__BB5_14;

$L__BB5_8:
	mov.b32 	%r15, %f7;
	bfe.u32 	%r118, %r15, 23, 8;
	add.s32 	%r16, %r118, -128;
	shl.b32 	%r119, %r15, 8;
	or.b32  	%r17, %r119, -2147483648;
	shr.u32 	%r18, %r16, 5;
	mov.u64 	%rd84, 0;
	mov.u32 	%r243, 0;
	mov.u64 	%rd82, %rd1;
	mov.u64 	%rd83, %rd28;

$L__BB5_9:
	.pragma "nounroll";
	ld.global.nc.u32 	%r120, [%rd83];
	mad.wide.u32 	%rd30, %r120, %r17, %rd84;
	shr.u64 	%rd84, %rd30, 32;
	st.local.u32 	[%rd82], %rd30;
	add.s64 	%rd83, %rd83, 4;
	add.s64 	%rd82, %rd82, 4;
	add.s32 	%r243, %r243, 1;
	setp.ne.s32 	%p18, %r243, 6;
	@%p18 bra 	$L__BB5_9;

	st.local.u32 	[%rd4], %rd84;
	mov.u32 	%r121, 4;
	sub.s32 	%r21, %r121, %r18;
	mov.u32 	%r122, 6;
	sub.s32 	%r123, %r122, %r18;
	mul.wide.s32 	%rd31, %r123, 4;
	add.s64 	%rd32, %rd1, %rd31;
	ld.local.u32 	%r244, [%rd32];
	ld.local.u32 	%r245, [%rd32+-4];
	and.b32  	%r24, %r16, 31;
	setp.eq.s32 	%p19, %r24, 0;
	@%p19 bra 	$L__BB5_12;

	mov.u32 	%r124, 32;
	sub.s32 	%r125, %r124, %r24;
	shr.u32 	%r126, %r245, %r125;
	shl.b32 	%r127, %r244, %r24;
	add.s32 	%r244, %r126, %r127;
	mul.wide.s32 	%rd33, %r21, 4;
	add.s64 	%rd34, %rd1, %rd33;
	ld.local.u32 	%r128, [%rd34];
	shr.u32 	%r129, %r128, %r125;
	shl.b32 	%r130, %r245, %r24;
	add.s32 	%r245, %r129, %r130;

$L__BB5_12:
	and.b32  	%r131, %r15, -2147483648;
	shr.u32 	%r132, %r245, 30;
	shl.b32 	%r133, %r244, 2;
	or.b32  	%r134, %r132, %r133;
	shr.u32 	%r135, %r134, 31;
	shr.u32 	%r136, %r244, 30;
	add.s32 	%r137, %r135, %r136;
	neg.s32 	%r138, %r137;
	setp.eq.s32 	%p20, %r131, 0;
	selp.b32 	%r246, %r137, %r138, %p20;
	setp.ne.s32 	%p21, %r135, 0;
	xor.b32  	%r139, %r131, -2147483648;
	selp.b32 	%r140, %r139, %r131, %p21;
	selp.b32 	%r141, -1, 0, %p21;
	xor.b32  	%r142, %r134, %r141;
	shl.b32 	%r143, %r245, 2;
	xor.b32  	%r144, %r143, %r141;
	cvt.u64.u32 	%rd35, %r142;
	cvt.u64.u32 	%rd36, %r144;
	bfi.b64 	%rd37, %rd35, %rd36, 32, 32;
	cvt.rn.f64.s64 	%fd1, %rd37;
	mul.f64 	%fd2, %fd1, 0d3BF921FB54442D19;
	cvt.rn.f32.f64 	%f123, %fd2;
	setp.eq.s32 	%p22, %r140, 0;
	neg.f32 	%f124, %f123;
	selp.f32 	%f209, %f123, %f124, %p22;

$L__BB5_14:
	and.b32  	%r31, %r246, 1;
	setp.eq.s32 	%p23, %r31, 0;
	selp.f32 	%f14, %f209, 0f3F800000, %p23;
	mul.rn.f32 	%f15, %f209, %f209;
	mov.f32 	%f210, 0fB94D4153;
	@%p23 bra 	$L__BB5_16;

	mov.f32 	%f127, 0fBAB607ED;
	mov.f32 	%f128, 0f37CBAC00;
	fma.rn.f32 	%f210, %f128, %f15, %f127;

$L__BB5_16:
	selp.f32 	%f129, 0f3C0885E4, 0f3D2AAABB, %p23;
	fma.rn.f32 	%f130, %f210, %f15, %f129;
	selp.f32 	%f131, 0fBE2AAAA8, 0fBEFFFFFF, %p23;
	fma.rn.f32 	%f132, %f130, %f15, %f131;
	mov.f32 	%f133, 0f00000000;
	fma.rn.f32 	%f134, %f15, %f14, %f133;
	fma.rn.f32 	%f211, %f132, %f134, %f14;
	and.b32  	%r146, %r246, 2;
	setp.eq.s32 	%p25, %r146, 0;
	@%p25 bra 	$L__BB5_18;

	mov.f32 	%f136, 0fBF800000;
	fma.rn.f32 	%f211, %f211, %f136, %f133;

$L__BB5_18:
	div.rn.f32 	%f21, %f211, %f7;
	mul.f32 	%f137, %f8, 0f3F22F983;
	cvt.rni.s32.f32 	%r249, %f137;
	cvt.rn.f32.s32 	%f138, %r249;
	mov.f32 	%f139, 0fBFC90FDA;
	fma.rn.f32 	%f140, %f138, %f139, %f8;
	mov.f32 	%f141, 0fB3A22168;
	fma.rn.f32 	%f142, %f138, %f141, %f140;
	mov.f32 	%f143, 0fA7C234C5;
	fma.rn.f32 	%f212, %f138, %f143, %f142;
	abs.f32 	%f23, %f8;
	setp.ltu.f32 	%p26, %f23, 0f47CE4780;
	@%p26 bra 	$L__BB5_26;

	setp.eq.f32 	%p27, %f23, 0f7F800000;
	@%p27 bra 	$L__BB5_25;
	bra.uni 	$L__BB5_20;

$L__BB5_25:
	mov.f32 	%f146, 0f00000000;
	mul.rn.f32 	%f212, %f8, %f146;
	mov.u32 	%r249, 0;
	bra.uni 	$L__BB5_26;

$L__BB5_20:
	mov.b32 	%r33, %f8;
	bfe.u32 	%r147, %r33, 23, 8;
	add.s32 	%r34, %r147, -128;
	shl.b32 	%r148, %r33, 8;
	or.b32  	%r35, %r148, -2147483648;
	shr.u32 	%r36, %r34, 5;
	mov.u64 	%rd85, 0;
	mov.u64 	%rd86, %rd85;

$L__BB5_21:
	.pragma "nounroll";
	shl.b64 	%rd40, %rd85, 2;
	mov.u64 	%rd41, __cudart_i2opi_f;
	add.s64 	%rd42, %rd41, %rd40;
	ld.global.nc.u32 	%r149, [%rd42];
	mad.wide.u32 	%rd43, %r149, %r35, %rd86;
	shr.u64 	%rd86, %rd43, 32;
	add.s64 	%rd44, %rd1, %rd40;
	st.local.u32 	[%rd44], %rd43;
	cvt.u32.u64 	%r150, %rd85;
	add.s32 	%r151, %r150, 1;
	cvt.s64.s32 	%rd85, %r151;
	setp.ne.s32 	%p28, %r151, 6;
	@%p28 bra 	$L__BB5_21;

	st.local.u32 	[%rd4], %rd86;
	mov.u32 	%r152, 4;
	sub.s32 	%r37, %r152, %r36;
	mov.u32 	%r153, 6;
	sub.s32 	%r154, %r153, %r36;
	mul.wide.s32 	%rd45, %r154, 4;
	add.s64 	%rd46, %rd1, %rd45;
	ld.local.u32 	%r247, [%rd46];
	ld.local.u32 	%r248, [%rd46+-4];
	and.b32  	%r40, %r34, 31;
	setp.eq.s32 	%p29, %r40, 0;
	@%p29 bra 	$L__BB5_24;

	mov.u32 	%r155, 32;
	sub.s32 	%r156, %r155, %r40;
	shr.u32 	%r157, %r248, %r156;
	shl.b32 	%r158, %r247, %r40;
	add.s32 	%r247, %r157, %r158;
	mul.wide.s32 	%rd47, %r37, 4;
	add.s64 	%rd48, %rd1, %rd47;
	ld.local.u32 	%r159, [%rd48];
	shr.u32 	%r160, %r159, %r156;
	shl.b32 	%r161, %r248, %r40;
	add.s32 	%r248, %r160, %r161;

$L__BB5_24:
	and.b32  	%r162, %r33, -2147483648;
	shr.u32 	%r163, %r248, 30;
	shl.b32 	%r164, %r247, 2;
	or.b32  	%r165, %r163, %r164;
	shr.u32 	%r166, %r165, 31;
	shr.u32 	%r167, %r247, 30;
	add.s32 	%r168, %r166, %r167;
	neg.s32 	%r169, %r168;
	setp.eq.s32 	%p30, %r162, 0;
	selp.b32 	%r249, %r168, %r169, %p30;
	setp.ne.s32 	%p31, %r166, 0;
	xor.b32  	%r170, %r162, -2147483648;
	selp.b32 	%r171, %r170, %r162, %p31;
	selp.b32 	%r172, -1, 0, %p31;
	xor.b32  	%r173, %r165, %r172;
	shl.b32 	%r174, %r248, 2;
	xor.b32  	%r175, %r174, %r172;
	cvt.u64.u32 	%rd49, %r173;
	cvt.u64.u32 	%rd50, %r175;
	bfi.b64 	%rd51, %rd49, %rd50, 32, 32;
	cvt.rn.f64.s64 	%fd3, %rd51;
	mul.f64 	%fd4, %fd3, 0d3BF921FB54442D19;
	cvt.rn.f32.f64 	%f144, %fd4;
	setp.eq.s32 	%p32, %r171, 0;
	neg.f32 	%f145, %f144;
	selp.f32 	%f212, %f144, %f145, %p32;

$L__BB5_26:
	and.b32  	%r47, %r249, 1;
	setp.eq.s32 	%p33, %r47, 0;
	selp.f32 	%f27, %f212, 0f3F800000, %p33;
	mul.rn.f32 	%f28, %f212, %f212;
	mov.f32 	%f213, 0fB94D4153;
	@%p33 bra 	$L__BB5_28;

	mov.f32 	%f148, 0fBAB607ED;
	mov.f32 	%f149, 0f37CBAC00;
	fma.rn.f32 	%f213, %f149, %f28, %f148;

$L__BB5_28:
	selp.f32 	%f150, 0f3C0885E4, 0f3D2AAABB, %p33;
	fma.rn.f32 	%f151, %f213, %f28, %f150;
	selp.f32 	%f152, 0fBE2AAAA8, 0fBEFFFFFF, %p33;
	fma.rn.f32 	%f153, %f151, %f28, %f152;
	mov.f32 	%f154, 0f00000000;
	fma.rn.f32 	%f155, %f28, %f27, %f154;
	fma.rn.f32 	%f214, %f153, %f155, %f27;
	and.b32  	%r177, %r249, 2;
	setp.eq.s32 	%p35, %r177, 0;
	@%p35 bra 	$L__BB5_30;

	mov.f32 	%f157, 0fBF800000;
	fma.rn.f32 	%f214, %f214, %f157, %f154;

$L__BB5_30:
	div.rn.f32 	%f158, %f214, %f8;
	mul.f32 	%f215, %f21, %f158;

$L__BB5_31:
	setp.eq.f32 	%p36, %f215, 0f00000000;
	setp.gt.s32 	%p37, %r8, %r9;
	or.pred  	%p38, %p36, %p37;
	@%p38 bra 	$L__BB5_63;

	add.s32 	%r178, %r242, %r12;
	mul.lo.s32 	%r48, %r178, %r86;
	mov.u32 	%r250, %r8;

$L__BB5_33:
	cvt.rn.f32.s32 	%f160, %r250;
	sub.f32 	%f38, %f1, %f160;
	abs.f32 	%f39, %f38;
	setp.lt.f32 	%p39, %f39, 0f358637BD;
	mov.f32 	%f224, 0f3F800000;
	@%p39 bra 	$L__BB5_60;

	setp.ge.f32 	%p40, %f39, 0f40400000;
	mov.f32 	%f224, 0f00000000;
	@%p40 bra 	$L__BB5_60;

	mul.f32 	%f40, %f38, 0f40490FDB;
	div.rn.f32 	%f41, %f40, 0f40400000;
	mul.f32 	%f162, %f40, 0f3F22F983;
	cvt.rni.s32.f32 	%r253, %f162;
	cvt.rn.f32.s32 	%f163, %r253;
	mov.f32 	%f164, 0fBFC90FDA;
	fma.rn.f32 	%f165, %f163, %f164, %f40;
	mov.f32 	%f166, 0fB3A22168;
	fma.rn.f32 	%f167, %f163, %f166, %f165;
	mov.f32 	%f168, 0fA7C234C5;
	fma.rn.f32 	%f218, %f163, %f168, %f167;
	abs.f32 	%f43, %f40;
	setp.ltu.f32 	%p41, %f43, 0f47CE4780;
	@%p41 bra 	$L__BB5_43;

	setp.eq.f32 	%p42, %f43, 0f7F800000;
	@%p42 bra 	$L__BB5_42;
	bra.uni 	$L__BB5_37;

$L__BB5_42:
	mov.f32 	%f171, 0f00000000;
	mul.rn.f32 	%f218, %f40, %f171;
	mov.u32 	%r253, 0;
	bra.uni 	$L__BB5_43;

$L__BB5_37:
	mov.b32 	%r51, %f40;
	bfe.u32 	%r179, %r51, 23, 8;
	add.s32 	%r52, %r179, -128;
	shl.b32 	%r180, %r51, 8;
	or.b32  	%r53, %r180, -2147483648;
	shr.u32 	%r54, %r52, 5;
	mov.u64 	%rd87, 0;
	mov.u64 	%rd88, %rd87;

$L__BB5_38:
	.pragma "nounroll";
	shl.b64 	%rd54, %rd87, 2;
	mov.u64 	%rd55, __cudart_i2opi_f;
	add.s64 	%rd56, %rd55, %rd54;
	ld.global.nc.u32 	%r181, [%rd56];
	mad.wide.u32 	%rd57, %r181, %r53, %rd88;
	shr.u64 	%rd88, %rd57, 32;
	add.s64 	%rd58, %rd1, %rd54;
	st.local.u32 	[%rd58], %rd57;
	cvt.u32.u64 	%r182, %rd87;
	add.s32 	%r183, %r182, 1;
	cvt.s64.s32 	%rd87, %r183;
	setp.ne.s32 	%p43, %r183, 6;
	@%p43 bra 	$L__BB5_38;

	st.local.u32 	[%rd4], %rd88;
	mov.u32 	%r184, 4;
	sub.s32 	%r55, %r184, %r54;
	mov.u32 	%r185, 6;
	sub.s32 	%r186, %r185, %r54;
	mul.wide.s32 	%rd59, %r186, 4;
	add.s64 	%rd60, %rd1, %rd59;
	ld.local.u32 	%r251, [%rd60];
	ld.local.u32 	%r252, [%rd60+-4];
	and.b32  	%r58, %r52, 31;
	setp.eq.s32 	%p44, %r58, 0;
	@%p44 bra 	$L__BB5_41;

	mov.u32 	%r187, 32;
	sub.s32 	%r188, %r187, %r58;
	shr.u32 	%r189, %r252, %r188;
	shl.b32 	%r190, %r251, %r58;
	add.s32 	%r251, %r189, %r190;
	mul.wide.s32 	%rd61, %r55, 4;
	add.s64 	%rd62, %rd1, %rd61;
	ld.local.u32 	%r191, [%rd62];
	shr.u32 	%r192, %r191, %r188;
	shl.b32 	%r193, %r252, %r58;
	add.s32 	%r252, %r192, %r193;

$L__BB5_41:
	and.b32  	%r194, %r51, -2147483648;
	shr.u32 	%r195, %r252, 30;
	shl.b32 	%r196, %r251, 2;
	or.b32  	%r197, %r195, %r196;
	shr.u32 	%r198, %r197, 31;
	shr.u32 	%r199, %r251, 30;
	add.s32 	%r200, %r198, %r199;
	neg.s32 	%r201, %r200;
	setp.eq.s32 	%p45, %r194, 0;
	selp.b32 	%r253, %r200, %r201, %p45;
	setp.ne.s32 	%p46, %r198, 0;
	xor.b32  	%r202, %r194, -2147483648;
	selp.b32 	%r203, %r202, %r194, %p46;
	selp.b32 	%r204, -1, 0, %p46;
	xor.b32  	%r205, %r197, %r204;
	shl.b32 	%r206, %r252, 2;
	xor.b32  	%r207, %r206, %r204;
	cvt.u64.u32 	%rd63, %r205;
	cvt.u64.u32 	%rd64, %r207;
	bfi.b64 	%rd65, %rd63, %rd64, 32, 32;
	cvt.rn.f64.s64 	%fd5, %rd65;
	mul.f64 	%fd6, %fd5, 0d3BF921FB54442D19;
	cvt.rn.f32.f64 	%f169, %fd6;
	setp.eq.s32 	%p47, %r203, 0;
	neg.f32 	%f170, %f169;
	selp.f32 	%f218, %f169, %f170, %p47;

$L__BB5_43:
	and.b32  	%r65, %r253, 1;
	setp.eq.s32 	%p48, %r65, 0;
	selp.f32 	%f47, %f218, 0f3F800000, %p48;
	mul.rn.f32 	%f48, %f218, %f218;
	mov.f32 	%f219, 0fB94D4153;
	@%p48 bra 	$L__BB5_45;

	mov.f32 	%f173, 0fBAB607ED;
	mov.f32 	%f174, 0f37CBAC00;
	fma.rn.f32 	%f219, %f174, %f48, %f173;

$L__BB5_45:
	selp.f32 	%f175, 0f3C0885E4, 0f3D2AAABB, %p48;
	fma.rn.f32 	%f176, %f219, %f48, %f175;
	selp.f32 	%f177, 0fBE2AAAA8, 0fBEFFFFFF, %p48;
	fma.rn.f32 	%f178, %f176, %f48, %f177;
	mov.f32 	%f179, 0f00000000;
	fma.rn.f32 	%f180, %f48, %f47, %f179;
	fma.rn.f32 	%f220, %f178, %f180, %f47;
	and.b32  	%r209, %r253, 2;
	setp.eq.s32 	%p50, %r209, 0;
	@%p50 bra 	$L__BB5_47;

	mov.f32 	%f182, 0fBF800000;
	fma.rn.f32 	%f220, %f220, %f182, %f179;

$L__BB5_47:
	div.rn.f32 	%f54, %f220, %f40;
	mul.f32 	%f183, %f41, 0f3F22F983;
	cvt.rni.s32.f32 	%r256, %f183;
	cvt.rn.f32.s32 	%f184, %r256;
	mov.f32 	%f185, 0fBFC90FDA;
	fma.rn.f32 	%f186, %f184, %f185, %f41;
	mov.f32 	%f187, 0fB3A22168;
	fma.rn.f32 	%f188, %f184, %f187, %f186;
	mov.f32 	%f189, 0fA7C234C5;
	fma.rn.f32 	%f221, %f184, %f189, %f188;
	abs.f32 	%f56, %f41;
	setp.ltu.f32 	%p51, %f56, 0f47CE4780;
	@%p51 bra 	$L__BB5_55;

	setp.eq.f32 	%p52, %f56, 0f7F800000;
	@%p52 bra 	$L__BB5_54;
	bra.uni 	$L__BB5_49;

$L__BB5_54:
	mov.f32 	%f192, 0f00000000;
	mul.rn.f32 	%f221, %f41, %f192;
	mov.u32 	%r256, 0;
	bra.uni 	$L__BB5_55;

$L__BB5_49:
	mov.b32 	%r67, %f41;
	bfe.u32 	%r210, %r67, 23, 8;
	add.s32 	%r68, %r210, -128;
	shl.b32 	%r211, %r67, 8;
	or.b32  	%r69, %r211, -2147483648;
	shr.u32 	%r70, %r68, 5;
	mov.u64 	%rd89, 0;
	mov.u64 	%rd90, %rd89;

$L__BB5_50:
	.pragma "nounroll";
	shl.b64 	%rd68, %rd89, 2;
	mov.u64 	%rd69, __cudart_i2opi_f;
	add.s64 	%rd70, %rd69, %rd68;
	ld.global.nc.u32 	%r212, [%rd70];
	mad.wide.u32 	%rd71, %r212, %r69, %rd90;
	shr.u64 	%rd90, %rd71, 32;
	add.s64 	%rd72, %rd1, %rd68;
	st.local.u32 	[%rd72], %rd71;
	cvt.u32.u64 	%r213, %rd89;
	add.s32 	%r214, %r213, 1;
	cvt.s64.s32 	%rd89, %r214;
	setp.ne.s32 	%p53, %r214, 6;
	@%p53 bra 	$L__BB5_50;

	st.local.u32 	[%rd4], %rd90;
	mov.u32 	%r215, 4;
	sub.s32 	%r71, %r215, %r70;
	mov.u32 	%r216, 6;
	sub.s32 	%r217, %r216, %r70;
	mul.wide.s32 	%rd73, %r217, 4;
	add.s64 	%rd74, %rd1, %rd73;
	ld.local.u32 	%r254, [%rd74];
	ld.local.u32 	%r255, [%rd74+-4];
	and.b32  	%r74, %r68, 31;
	setp.eq.s32 	%p54, %r74, 0;
	@%p54 bra 	$L__BB5_53;

	mov.u32 	%r218, 32;
	sub.s32 	%r219, %r218, %r74;
	shr.u32 	%r220, %r255, %r219;
	shl.b32 	%r221, %r254, %r74;
	add.s32 	%r254, %r220, %r221;
	mul.wide.s32 	%rd75, %r71, 4;
	add.s64 	%rd76, %rd1, %rd75;
	ld.local.u32 	%r222, [%rd76];
	shr.u32 	%r223, %r222, %r219;
	shl.b32 	%r224, %r255, %r74;
	add.s32 	%r255, %r223, %r224;

$L__BB5_53:
	and.b32  	%r225, %r67, -2147483648;
	shr.u32 	%r226, %r255, 30;
	shl.b32 	%r227, %r254, 2;
	or.b32  	%r228, %r226, %r227;
	shr.u32 	%r229, %r228, 31;
	shr.u32 	%r230, %r254, 30;
	add.s32 	%r231, %r229, %r230;
	neg.s32 	%r232, %r231;
	setp.eq.s32 	%p55, %r225, 0;
	selp.b32 	%r256, %r231, %r232, %p55;
	setp.ne.s32 	%p56, %r229, 0;
	xor.b32  	%r233, %r225, -2147483648;
	selp.b32 	%r234, %r233, %r225, %p56;
	selp.b32 	%r235, -1, 0, %p56;
	xor.b32  	%r236, %r228, %r235;
	shl.b32 	%r237, %r255, 2;
	xor.b32  	%r238, %r237, %r235;
	cvt.u64.u32 	%rd77, %r236;
	cvt.u64.u32 	%rd78, %r238;
	bfi.b64 	%rd79, %rd77, %rd78, 32, 32;
	cvt.rn.f64.s64 	%fd7, %rd79;
	mul.f64 	%fd8, %fd7, 0d3BF921FB54442D19;
	cvt.rn.f32.f64 	%f190, %fd8;
	setp.eq.s32 	%p57, %r234, 0;
	neg.f32 	%f191, %f190;
	selp.f32 	%f221, %f190, %f191, %p57;

$L__BB5_55:
	and.b32  	%r81, %r256, 1;
	setp.eq.s32 	%p58, %r81, 0;
	selp.f32 	%f60, %f221, 0f3F800000, %p58;
	mul.rn.f32 	%f61, %f221, %f221;
	mov.f32 	%f222, 0fB94D4153;
	@%p58 bra 	$L__BB5_57;

	mov.f32 	%f194, 0fBAB607ED;
	mov.f32 	%f195, 0f37CBAC00;
	fma.rn.f32 	%f222, %f195, %f61, %f194;

$L__BB5_57:
	selp.f32 	%f196, 0f3C0885E4, 0f3D2AAABB, %p58;
	fma.rn.f32 	%f197, %f222, %f61, %f196;
	selp.f32 	%f198, 0fBE2AAAA8, 0fBEFFFFFF, %p58;
	fma.rn.f32 	%f199, %f197, %f61, %f198;
	mov.f32 	%f200, 0f00000000;
	fma.rn.f32 	%f201, %f61, %f60, %f200;
	fma.rn.f32 	%f223, %f199, %f201, %f60;
	and.b32  	%r240, %r256, 2;
	setp.eq.s32 	%p60, %r240, 0;
	@%p60 bra 	$L__BB5_59;

	mov.f32 	%f203, 0fBF800000;
	fma.rn.f32 	%f223, %f223, %f203, %f200;

$L__BB5_59:
	div.rn.f32 	%f204, %f223, %f41;
	mul.f32 	%f224, %f54, %f204;

$L__BB5_60:
	setp.eq.f32 	%p61, %f224, 0f00000000;
	@%p61 bra 	$L__BB5_62;

	mul.f32 	%f205, %f215, %f224;
	add.s32 	%r241, %r250, %r48;
	mul.wide.s32 	%rd80, %r241, 4;
	add.s64 	%rd81, %rd3, %rd80;
	ld.global.f32 	%f206, [%rd81];
	fma.rn.f32 	%f225, %f205, %f206, %f225;
	add.f32 	%f226, %f226, %f205;

$L__BB5_62:
	add.s32 	%r82, %r250, 1;
	setp.lt.s32 	%p62, %r250, %r9;
	mov.u32 	%r250, %r82;
	@%p62 bra 	$L__BB5_33;

$L__BB5_63:
	add.s32 	%r83, %r242, 1;
	setp.lt.s32 	%p63, %r242, %r11;
	mov.u32 	%r242, %r83;
	@%p63 bra 	$L__BB5_4;

$L__BB5_64:
	setp.leu.f32 	%p64, %f226, 0f00000000;
	@%p64 bra 	$L__BB5_66;

	div.rn.f32 	%f231, %f225, %f226;

$L__BB5_66:
	st.global.f32 	[%rd2], %f231;

$L__BB5_68:
	ret;

}
	// .globl	_Z19drawRectangleKernelPfiiiiiifffi
.visible .entry _Z19drawRectangleKernelPfiiiiiifffi(
	.param .u64 _Z19drawRectangleKernelPfiiiiiifffi_param_0,
	.param .u32 _Z19drawRectangleKernelPfiiiiiifffi_param_1,
	.param .u32 _Z19drawRectangleKernelPfiiiiiifffi_param_2,
	.param .u32 _Z19drawRectangleKernelPfiiiiiifffi_param_3,
	.param .u32 _Z19drawRectangleKernelPfiiiiiifffi_param_4,
	.param .u32 _Z19drawRectangleKernelPfiiiiiifffi_param_5,
	.param .u32 _Z19drawRectangleKernelPfiiiiiifffi_param_6,
	.param .f32 _Z19drawRectangleKernelPfiiiiiifffi_param_7,
	.param .f32 _Z19drawRectangleKernelPfiiiiiifffi_param_8,
	.param .f32 _Z19drawRectangleKernelPfiiiiiifffi_param_9,
	.param .u32 _Z19drawRectangleKernelPfiiiiiifffi_param_10
)
{
	.reg .pred 	%p<34>;
	.reg .b16 	%rs<5>;
	.reg .f32 	%f<4>;
	.reg .b32 	%r<22>;
	.reg .b64 	%rd<5>;


	ld.param.u64 	%rd1, [_Z19drawRectangleKernelPfiiiiiifffi_param_0];
	ld.param.u32 	%r3, [_Z19drawRectangleKernelPfiiiiiifffi_param_1];
	ld.param.u32 	%r9, [_Z19drawRectangleKernelPfiiiiiifffi_param_2];
	ld.param.u32 	%r4, [_Z19drawRectangleKernelPfiiiiiifffi_param_3];
	ld.param.u32 	%r5, [_Z19drawRectangleKernelPfiiiiiifffi_param_4];
	ld.param.u32 	%r6, [_Z19drawRectangleKernelPfiiiiiifffi_param_5];
	ld.param.u32 	%r7, [_Z19drawRectangleKernelPfiiiiiifffi_param_6];
	ld.param.f32 	%f1, [_Z19drawRectangleKernelPfiiiiiifffi_param_7];
	ld.param.f32 	%f2, [_Z19drawRectangleKernelPfiiiiiifffi_param_8];
	ld.param.f32 	%f3, [_Z19drawRectangleKernelPfiiiiiifffi_param_9];
	ld.param.u32 	%r8, [_Z19drawRectangleKernelPfiiiiiifffi_param_10];
	mov.u32 	%r10, %ntid.x;
	mov.u32 	%r11, %ctaid.x;
	mov.u32 	%r12, %tid.x;
	mad.lo.s32 	%r1, %r11, %r10, %r12;
	mov.u32 	%r13, %ntid.y;
	mov.u32 	%r14, %ctaid.y;
	mov.u32 	%r15, %tid.y;
	mad.lo.s32 	%r2, %r14, %r13, %r15;
	setp.ge.s32 	%p1, %r1, %r3;
	setp.ge.s32 	%p2, %r2, %r9;
	or.pred  	%p3, %p1, %p2;
	@%p3 bra 	$L__BB6_6;

	setp.ge.s32 	%p4, %r2, %r5;
	add.s32 	%r16, %r8, %r5;
	setp.lt.s32 	%p5, %r2, %r16;
	and.pred  	%p6, %p4, %p5;
	setp.ge.s32 	%p7, %r1, %r4;
	and.pred  	%p8, %p7, %p6;
	setp.le.s32 	%p9, %r1, %r6;
	and.pred  	%p10, %p9, %p8;
	mov.u16 	%rs4, 1;
	@%p10 bra 	$L__BB6_3;

	sub.s32 	%r17, %r7, %r8;
	setp.gt.s32 	%p12, %r2, %r17;
	setp.le.s32 	%p13, %r2, %r7;
	and.pred  	%p14, %p13, %p12;
	and.pred  	%p16, %p7, %p14;
	and.pred  	%p17, %p9, %p16;
	selp.u16 	%rs4, 1, 0, %p17;

$L__BB6_3:
	add.s32 	%r18, %r8, %r4;
	setp.lt.s32 	%p18, %r1, %r18;
	and.pred  	%p20, %p7, %p18;
	and.pred  	%p22, %p4, %p20;
	setp.le.s32 	%p23, %r2, %r7;
	and.pred  	%p24, %p23, %p22;
	@%p24 bra 	$L__BB6_5;

	setp.gt.s32 	%p25, %r2, %r7;
	sub.s32 	%r19, %r6, %r8;
	setp.le.s32 	%p26, %r1, %r19;
	setp.gt.s32 	%p27, %r1, %r6;
	or.pred  	%p28, %p27, %p26;
	setp.lt.s32 	%p29, %r2, %r5;
	or.pred  	%p30, %p29, %p28;
	or.pred  	%p31, %p25, %p30;
	setp.eq.s16 	%p32, %rs4, 0;
	and.pred  	%p33, %p31, %p32;
	@%p33 bra 	$L__BB6_6;

$L__BB6_5:
	mad.lo.s32 	%r20, %r2, %r3, %r1;
	mul.lo.s32 	%r21, %r20, 3;
	cvta.to.global.u64 	%rd2, %rd1;
	mul.wide.s32 	%rd3, %r21, 4;
	add.s64 	%rd4, %rd2, %rd3;
	st.global.f32 	[%rd4], %f1;
	st.global.f32 	[%rd4+4], %f2;
	st.global.f32 	[%rd4+8], %f3;

$L__BB6_6:
	ret;

}
	// .globl	_Z16drawNumberKernelPfiiiiifff
.visible .entry _Z16drawNumberKernelPfiiiiifff(
	.param .u64 _Z16drawNumberKernelPfiiiiifff_param_0,
	.param .u32 _Z16drawNumberKernelPfiiiiifff_param_1,
	.param .u32 _Z16drawNumberKernelPfiiiiifff_param_2,
	.param .u32 _Z16drawNumberKernelPfiiiiifff_param_3,
	.param .u32 _Z16drawNumberKernelPfiiiiifff_param_4,
	.param .u32 _Z16drawNumberKernelPfiiiiifff_param_5,
	.param .f32 _Z16drawNumberKernelPfiiiiifff_param_6,
	.param .f32 _Z16drawNumberKernelPfiiiiifff_param_7,
	.param .f32 _Z16drawNumberKernelPfiiiiifff_param_8
)
{
	.local .align 2 .b8 	__local_depot7[350];
	.reg .b64 	%SP;
	.reg .b64 	%SPL;
	.reg .pred 	%p<10>;
	.reg .b16 	%rs<6>;
	.reg .f32 	%f<4>;
	.reg .b32 	%r<19>;
	.reg .b64 	%rd<11>;


	mov.u64 	%SPL, __local_depot7;
	ld.param.u64 	%rd2, [_Z16drawNumberKernelPfiiiiifff_param_0];
	ld.param.u32 	%r5, [_Z16drawNumberKernelPfiiiiifff_param_1];
	ld.param.u32 	%r9, [_Z16drawNumberKernelPfiiiiifff_param_2];
	ld.param.u32 	%r6, [_Z16drawNumberKernelPfiiiiifff_param_3];
	ld.param.u32 	%r7, [_Z16drawNumberKernelPfiiiiifff_param_4];
	ld.param.u32 	%r8, [_Z16drawNumberKernelPfiiiiifff_param_5];
	ld.param.f32 	%f1, [_Z16drawNumberKernelPfiiiiifff_param_6];
	ld.param.f32 	%f2, [_Z16drawNumberKernelPfiiiiifff_param_7];
	ld.param.f32 	%f3, [_Z16drawNumberKernelPfiiiiifff_param_8];
	add.u64 	%rd1, %SPL, 0;
	mov.u32 	%r10, %ntid.x;
	mov.u32 	%r11, %ctaid.x;
	mov.u32 	%r12, %tid.x;
	mad.lo.s32 	%r1, %r11, %r10, %r12;
	mov.u32 	%r13, %ntid.y;
	mov.u32 	%r14, %ctaid.y;
	mov.u32 	%r15, %tid.y;
	mad.lo.s32 	%r2, %r14, %r13, %r15;
	setp.ge.s32 	%p1, %r1, %r5;
	setp.ge.s32 	%p2, %r2, %r9;
	or.pred  	%p3, %p1, %p2;
	@%p3 bra 	$L__BB7_4;

	mov.u16 	%rs1, 1;
	mov.u16 	%rs2, 257;
	st.local.u16 	[%rd1], %rs2;
	st.local.u16 	[%rd1+2], %rs2;
	st.local.u16 	[%rd1+4], %rs2;
	mov.u16 	%rs3, 0;
	st.local.u16 	[%rd1+6], %rs3;
	mov.u16 	%rs4, 256;
	st.local.u16 	[%rd1+8], %rs4;
	st.local.u16 	[%rd1+10], %rs1;
	st.local.u16 	[%rd1+12], %rs3;
	st.local.u16 	[%rd1+14], %rs2;
	st.local.u16 	[%rd1+16], %rs3;
	st.local.u16 	[%rd1+18], %rs4;
	st.local.u16 	[%rd1+20], %rs1;
	st.local.u16 	[%rd1+22], %rs3;
	st.local.u16 	[%rd1+24], %rs2;
	st.local.u16 	[%rd1+26], %rs3;
	st.local.u16 	[%rd1+28], %rs4;
	st.local.u16 	[%rd1+30], %rs2;
	st.local.u8 	[%rd1+32], %rs1;
	st.local.u8 	[%rd1+33], %rs1;
	st.local.u16 	[%rd1+34], %rs1;
	st.local.u16 	[%rd1+36], %rs4;
	st.local.u16 	[%rd1+38], %rs3;
	st.local.u16 	[%rd1+40], %rs4;
	st.local.u16 	[%rd1+42], %rs1;
	st.local.u16 	[%rd1+44], %rs3;
	st.local.u16 	[%rd1+46], %rs4;
	st.local.u16 	[%rd1+48], %rs3;
	st.local.u16 	[%rd1+50], %rs3;
	st.local.u16 	[%rd1+52], %rs1;
	st.local.u16 	[%rd1+54], %rs3;
	st.local.u16 	[%rd1+56], %rs4;
	st.local.u16 	[%rd1+58], %rs3;
	st.local.u16 	[%rd1+60], %rs3;
	st.local.u16 	[%rd1+62], %rs1;
	st.local.v2.u8 	[%rd1+64], {%rs3, %rs3};
	st.local.v2.u8 	[%rd1+66], {%rs1, %rs1};
	st.local.v2.u8 	[%rd1+68], {%rs1, %rs3};
	st.local.u16 	[%rd1+70], %rs2;
	st.local.u16 	[%rd1+72], %rs2;
	st.local.u16 	[%rd1+74], %rs1;
	st.local.u16 	[%rd1+76], %rs3;
	st.local.u16 	[%rd1+78], %rs4;
	st.local.u16 	[%rd1+80], %rs3;
	st.local.u16 	[%rd1+82], %rs3;
	st.local.u16 	[%rd1+84], %rs2;
	st.local.u16 	[%rd1+86], %rs2;
	st.local.u16 	[%rd1+88], %rs2;
	st.local.u16 	[%rd1+90], %rs1;
	st.local.u16 	[%rd1+92], %rs3;
	st.local.u16 	[%rd1+94], %rs4;
	st.local.u16 	[%rd1+96], %rs3;
	st.local.u16 	[%rd1+98], %rs3;
	st.local.u16 	[%rd1+100], %rs2;
	st.local.u16 	[%rd1+102], %rs2;
	st.local.u16 	[%rd1+104], %rs2;
	st.local.u16 	[%rd1+106], %rs2;
	st.local.u16 	[%rd1+108], %rs2;
	st.local.u16 	[%rd1+110], %rs3;
	st.local.u16 	[%rd1+112], %rs3;
	st.local.u16 	[%rd1+114], %rs1;
	st.local.u16 	[%rd1+116], %rs3;
	st.local.v2.u8 	[%rd1+118], {%rs3, %rs1};
	st.local.v2.u8 	[%rd1+120], {%rs1, %rs1};
	st.local.v2.u8 	[%rd1+122], {%rs1, %rs1};
	st.local.v2.u8 	[%rd1+124], {%rs1, %rs3};
	st.local.v2.u8 	[%rd1+126], {%rs3, %rs3};
	st.local.v2.u8 	[%rd1+128], {%rs3, %rs1};
	st.local.v2.u8 	[%rd1+130], {%rs3, %rs3};
	st.local.v2.u8 	[%rd1+132], {%rs3, %rs3};
	st.local.v2.u8 	[%rd1+134], {%rs1, %rs1};
	st.local.v2.u8 	[%rd1+136], {%rs1, %rs1};
	st.local.v2.u8 	[%rd1+138], {%rs1, %rs1};
	st.local.u16 	[%rd1+140], %rs1;
	st.local.u16 	[%rd1+142], %rs3;
	st.local.u16 	[%rd1+144], %rs2;
	st.local.u16 	[%rd1+146], %rs3;
	st.local.u16 	[%rd1+148], %rs4;
	st.local.u16 	[%rd1+150], %rs1;
	st.local.u16 	[%rd1+152], %rs3;
	st.local.u16 	[%rd1+154], %rs2;
	st.local.u16 	[%rd1+156], %rs2;
	st.local.u16 	[%rd1+158], %rs2;
	st.local.u16 	[%rd1+160], %rs3;
	st.local.u16 	[%rd1+162], %rs3;
	st.local.u16 	[%rd1+164], %rs1;
	st.local.u16 	[%rd1+166], %rs3;
	st.local.u16 	[%rd1+168], %rs4;
	st.local.u16 	[%rd1+170], %rs3;
	st.local.u16 	[%rd1+172], %rs3;
	st.local.u16 	[%rd1+174], %rs2;
	st.local.u16 	[%rd1+176], %rs2;
	st.local.u16 	[%rd1+178], %rs2;
	st.local.u16 	[%rd1+180], %rs1;
	st.local.u16 	[%rd1+182], %rs3;
	st.local.u16 	[%rd1+184], %rs4;
	st.local.u16 	[%rd1+186], %rs3;
	st.local.v2.u8 	[%rd1+188], {%rs3, %rs3};
	st.local.v2.u8 	[%rd1+190], {%rs1, %rs1};
	st.local.u16 	[%rd1+192], %rs2;
	st.local.u8 	[%rd1+194], %rs1;
	st.local.u8 	[%rd1+195], %rs3;
	st.local.u8 	[%rd1+196], %rs3;
	st.local.u8 	[%rd1+197], %rs3;
	st.local.u8 	[%rd1+198], %rs3;
	st.local.u8 	[%rd1+199], %rs1;
	st.local.u8 	[%rd1+200], %rs3;
	st.local.u8 	[%rd1+201], %rs3;
	st.local.u8 	[%rd1+202], %rs3;
	st.local.u8 	[%rd1+203], %rs3;
	st.local.u8 	[%rd1+204], %rs1;
	st.local.u8 	[%rd1+205], %rs1;
	st.local.u8 	[%rd1+206], %rs1;
	st.local.u8 	[%rd1+207], %rs1;
	st.local.v2.u8 	[%rd1+208], {%rs1, %rs1};
	st.local.u16 	[%rd1+210], %rs2;
	st.local.u16 	[%rd1+212], %rs2;
	st.local.u16 	[%rd1+214], %rs2;
	st.local.u16 	[%rd1+216], %rs3;
	st.local.u16 	[%rd1+218], %rs3;
	st.local.u16 	[%rd1+220], %rs1;
	st.local.u16 	[%rd1+222], %rs3;
	st.local.u16 	[%rd1+224], %rs4;
	st.local.u16 	[%rd1+226], %rs2;
	st.local.u16 	[%rd1+228], %rs2;
	st.local.u16 	[%rd1+230], %rs1;
	st.local.u16 	[%rd1+232], %rs3;
	st.local.u16 	[%rd1+234], %rs2;
	st.local.u16 	[%rd1+236], %rs3;
	st.local.u16 	[%rd1+238], %rs4;
	st.local.u16 	[%rd1+240], %rs2;
	st.local.v2.u8 	[%rd1+242], {%rs1, %rs1};
	st.local.v2.u8 	[%rd1+244], {%rs1, %rs1};
	st.local.v2.u8 	[%rd1+246], {%rs1, %rs1};
	st.local.v2.u8 	[%rd1+248], {%rs1, %rs1};
	st.local.v2.u8 	[%rd1+250], {%rs3, %rs3};
	st.local.v2.u8 	[%rd1+252], {%rs3, %rs3};
	st.local.v2.u8 	[%rd1+254], {%rs1, %rs3};
	st.local.u16 	[%rd1+256], %rs3;
	st.local.u8 	[%rd1+258], %rs3;
	st.local.u8 	[%rd1+259], %rs1;
	st.local.u8 	[%rd1+260], %rs3;
	st.local.u8 	[%rd1+261], %rs3;
	st.local.u8 	[%rd1+262], %rs3;
	st.local.u8 	[%rd1+263], %rs3;
	st.local.u8 	[%rd1+264], %rs1;
	st.local.u8 	[%rd1+265], %rs3;
	st.local.u8 	[%rd1+266], %rs3;
	st.local.u8 	[%rd1+267], %rs3;
	st.local.u8 	[%rd1+268], %rs3;
	st.local.u8 	[%rd1+269], %rs1;
	st.local.u8 	[%rd1+270], %rs3;
	st.local.u8 	[%rd1+271], %rs3;
	st.local.v2.u8 	[%rd1+272], {%rs3, %rs3};
	st.local.v2.u8 	[%rd1+274], {%rs1, %rs3};
	st.local.v2.u8 	[%rd1+276], {%rs3, %rs3};
	st.local.v2.u8 	[%rd1+278], {%rs3, %rs1};
	st.local.u16 	[%rd1+280], %rs2;
	st.local.u16 	[%rd1+282], %rs2;
	st.local.u16 	[%rd1+284], %rs2;
	st.local.u16 	[%rd1+286], %rs3;
	st.local.u16 	[%rd1+288], %rs4;
	st.local.u16 	[%rd1+290], %rs1;
	st.local.u16 	[%rd1+292], %rs3;
	st.local.u16 	[%rd1+294], %rs2;
	st.local.u16 	[%rd1+296], %rs2;
	st.local.u16 	[%rd1+298], %rs2;
	st.local.u16 	[%rd1+300], %rs1;
	st.local.u16 	[%rd1+302], %rs3;
	st.local.u16 	[%rd1+304], %rs2;
	st.local.u16 	[%rd1+306], %rs3;
	st.local.u16 	[%rd1+308], %rs4;
	st.local.u16 	[%rd1+310], %rs2;
	st.local.v2.u8 	[%rd1+312], {%rs1, %rs1};
	st.local.v2.u8 	[%rd1+314], {%rs1, %rs1};
	st.local.v2.u8 	[%rd1+316], {%rs1, %rs1};
	st.local.v2.u8 	[%rd1+318], {%rs1, %rs1};
	st.local.u16 	[%rd1+320], %rs1;
	st.local.u8 	[%rd1+322], %rs3;
	st.local.u8 	[%rd1+323], %rs3;
	st.local.u8 	[%rd1+324], %rs1;
	st.local.u8 	[%rd1+325], %rs1;
	st.local.u8 	[%rd1+326], %rs3;
	st.local.u8 	[%rd1+327], %rs3;
	st.local.u8 	[%rd1+328], %rs3;
	st.local.u8 	[%rd1+329], %rs1;
	st.local.u8 	[%rd1+330], %rs1;
	st.local.u8 	[%rd1+331], %rs1;
	st.local.u8 	[%rd1+332], %rs1;
	st.local.u8 	[%rd1+333], %rs1;
	st.local.u8 	[%rd1+334], %rs1;
	st.local.u8 	[%rd1+335], %rs3;
	st.local.v2.u8 	[%rd1+336], {%rs3, %rs3};
	st.local.v2.u8 	[%rd1+338], {%rs3, %rs1};
	st.local.v2.u8 	[%rd1+340], {%rs3, %rs3};
	st.local.v2.u8 	[%rd1+342], {%rs3, %rs3};
	st.local.v2.u8 	[%rd1+344], {%rs1, %rs1};
	st.local.v2.u8 	[%rd1+346], {%rs1, %rs1};
	st.local.v2.u8 	[%rd1+348], {%rs1, %rs1};
	sub.s32 	%r3, %r1, %r6;
	setp.gt.u32 	%p4, %r3, 4;
	sub.s32 	%r4, %r2, %r7;
	setp.gt.u32 	%p5, %r4, 6;
	or.pred  	%p6, %p4, %p5;
	setp.gt.u32 	%p7, %r8, 9;
	or.pred  	%p8, %p7, %p6;
	@%p8 bra 	$L__BB7_4;

	mad.lo.s32 	%r16, %r4, 5, %r3;
	cvt.s64.s32 	%rd4, %r16;
	mul.wide.s32 	%rd5, %r8, 35;
	add.s64 	%rd6, %rd1, %rd5;
	add.s64 	%rd7, %rd6, %rd4;
	ld.local.u8 	%rs5, [%rd7];
	setp.eq.s16 	%p9, %rs5, 0;
	@%p9 bra 	$L__BB7_4;

	mad.lo.s32 	%r17, %r2, %r5, %r1;
	mul.lo.s32 	%r18, %r17, 3;
	cvta.to.global.u64 	%rd8, %rd2;
	mul.wide.s32 	%rd9, %r18, 4;
	add.s64 	%rd10, %rd8, %rd9;
	st.global.f32 	[%rd10], %f1;
	st.global.f32 	[%rd10+4], %f2;
	st.global.f32 	[%rd10+8], %f3;

$L__BB7_4:
	ret;

}
	// .globl	DetectUncertainRegionsKernel
.visible .entry DetectUncertainRegionsKernel(
	.param .u64 DetectUncertainRegionsKernel_param_0,
	.param .u64 DetectUncertainRegionsKernel_param_1,
	.param .u32 DetectUncertainRegionsKernel_param_2,
	.param .u32 DetectUncertainRegionsKernel_param_3,
	.param .u32 DetectUncertainRegionsKernel_param_4
)
{
	.reg .pred 	%p<7>;
	.reg .b16 	%rs<2>;
	.reg .f32 	%f<2>;
	.reg .b32 	%r<19>;
	.reg .b64 	%rd<9>;


	ld.param.u64 	%rd1, [DetectUncertainRegionsKernel_param_0];
	ld.param.u64 	%rd2, [DetectUncertainRegionsKernel_param_1];
	ld.param.u32 	%r3, [DetectUncertainRegionsKernel_param_2];
	ld.param.u32 	%r5, [DetectUncertainRegionsKernel_param_3];
	ld.param.u32 	%r4, [DetectUncertainRegionsKernel_param_4];
	mov.u32 	%r6, %ntid.x;
	mov.u32 	%r7, %ctaid.x;
	mov.u32 	%r8, %tid.x;
	mad.lo.s32 	%r1, %r7, %r6, %r8;
	mov.u32 	%r9, %ntid.y;
	mov.u32 	%r10, %ctaid.y;
	mov.u32 	%r11, %tid.y;
	mad.lo.s32 	%r2, %r10, %r9, %r11;
	setp.ge.s32 	%p1, %r1, %r3;
	setp.ge.s32 	%p2, %r2, %r5;
	or.pred  	%p3, %p1, %p2;
	@%p3 bra 	$L__BB8_3;

	cvta.to.global.u64 	%rd3, %rd1;
	mad.lo.s32 	%r12, %r2, %r3, %r1;
	mul.wide.s32 	%rd4, %r12, 4;
	add.s64 	%rd5, %rd3, %rd4;
	ld.global.f32 	%f1, [%rd5];
	setp.leu.f32 	%p4, %f1, 0f3DCCCCCD;
	setp.geu.f32 	%p5, %f1, 0f3F666666;
	or.pred  	%p6, %p4, %p5;
	@%p6 bra 	$L__BB8_3;

	div.s32 	%r13, %r1, %r4;
	add.s32 	%r14, %r3, %r4;
	add.s32 	%r15, %r14, -1;
	div.s32 	%r16, %r15, %r4;
	div.s32 	%r17, %r2, %r4;
	mad.lo.s32 	%r18, %r16, %r17, %r13;
	cvt.s64.s32 	%rd6, %r18;
	cvta.to.global.u64 	%rd7, %rd2;
	add.s64 	%rd8, %rd7, %rd6;
	mov.u16 	%rs1, 1;
	st.global.u8 	[%rd8], %rs1;

$L__BB8_3:
	ret;

}
	// .globl	generateTrimapKernel
.visible .entry generateTrimapKernel(
	.param .u64 generateTrimapKernel_param_0,
	.param .u64 generateTrimapKernel_param_1,
	.param .u32 generateTrimapKernel_param_2,
	.param .u32 generateTrimapKernel_param_3
)
{
	.reg .pred 	%p<33>;
	.reg .f32 	%f<29>;
	.reg .b32 	%r<19>;
	.reg .b64 	%rd<14>;


	ld.param.u64 	%rd6, [generateTrimapKernel_param_0];
	ld.param.u64 	%rd7, [generateTrimapKernel_param_1];
	ld.param.u32 	%r5, [generateTrimapKernel_param_2];
	ld.param.u32 	%r6, [generateTrimapKernel_param_3];
	cvta.to.global.u64 	%rd1, %rd7;
	mov.u32 	%r7, %ntid.x;
	mov.u32 	%r8, %ctaid.x;
	mov.u32 	%r9, %tid.x;
	mad.lo.s32 	%r1, %r8, %r7, %r9;
	mov.u32 	%r10, %ntid.y;
	mov.u32 	%r11, %ctaid.y;
	mov.u32 	%r12, %tid.y;
	mad.lo.s32 	%r2, %r11, %r10, %r12;
	setp.ge.s32 	%p2, %r1, %r5;
	setp.ge.s32 	%p3, %r2, %r6;
	or.pred  	%p4, %p2, %p3;
	@%p4 bra 	$L__BB9_12;

	mul.lo.s32 	%r3, %r2, %r5;
	add.s32 	%r13, %r3, %r1;
	cvt.s64.s32 	%rd2, %r13;
	mul.wide.s32 	%rd8, %r13, 4;
	add.s64 	%rd3, %rd1, %rd8;
	ld.global.f32 	%f1, [%rd3];
	add.s32 	%r14, %r5, -1;
	setp.ge.s32 	%p6, %r1, %r14;
	setp.lt.s32 	%p7, %r1, 1;
	or.pred  	%p8, %p7, %p6;
	setp.lt.s32 	%p9, %r2, 1;
	or.pred  	%p10, %p9, %p8;
	add.s32 	%r15, %r6, -1;
	setp.ge.s32 	%p11, %r2, %r15;
	mov.pred 	%p5, -1;
	or.pred  	%p12, %p11, %p10;
	mov.pred 	%p32, %p5;
	@%p12 bra 	$L__BB9_11;

	sub.s32 	%r16, %r3, %r5;
	add.s32 	%r4, %r16, %r1;
	mul.wide.s32 	%rd9, %r4, 4;
	add.s64 	%rd4, %rd1, %rd9;
	ld.global.f32 	%f2, [%rd4+-4];
	sub.f32 	%f3, %f1, %f2;
	abs.f32 	%f4, %f3;
	setp.gt.f32 	%p14, %f4, 0f3E99999A;
	mov.pred 	%p32, 0;
	@%p14 bra 	$L__BB9_11;

	ld.global.f32 	%f5, [%rd4];
	sub.f32 	%f6, %f1, %f5;
	abs.f32 	%f7, %f6;
	setp.gt.f32 	%p16, %f7, 0f3E99999A;
	@%p16 bra 	$L__BB9_11;

	ld.global.f32 	%f8, [%rd4+4];
	sub.f32 	%f9, %f1, %f8;
	abs.f32 	%f10, %f9;
	setp.gt.f32 	%p18, %f10, 0f3E99999A;
	@%p18 bra 	$L__BB9_11;

	ld.global.f32 	%f11, [%rd3+-4];
	sub.f32 	%f12, %f1, %f11;
	abs.f32 	%f13, %f12;
	setp.gt.f32 	%p20, %f13, 0f3E99999A;
	@%p20 bra 	$L__BB9_11;

	ld.global.f32 	%f14, [%rd3+4];
	sub.f32 	%f15, %f1, %f14;
	abs.f32 	%f16, %f15;
	setp.gt.f32 	%p22, %f16, 0f3E99999A;
	@%p22 bra 	$L__BB9_11;

	shl.b32 	%r17, %r5, 1;
	add.s32 	%r18, %r4, %r17;
	mul.wide.s32 	%rd10, %r18, 4;
	add.s64 	%rd5, %rd1, %rd10;
	ld.global.f32 	%f17, [%rd5+-4];
	sub.f32 	%f18, %f1, %f17;
	abs.f32 	%f19, %f18;
	setp.gt.f32 	%p24, %f19, 0f3E99999A;
	@%p24 bra 	$L__BB9_11;

	ld.global.f32 	%f20, [%rd5];
	sub.f32 	%f21, %f1, %f20;
	abs.f32 	%f22, %f21;
	setp.gt.f32 	%p26, %f22, 0f3E99999A;
	@%p26 bra 	$L__BB9_11;

	ld.global.f32 	%f23, [%rd5+4];
	sub.f32 	%f24, %f1, %f23;
	abs.f32 	%f25, %f24;
	setp.gt.f32 	%p28, %f25, 0f3E99999A;
	@%p28 bra 	$L__BB9_11;

	mov.pred 	%p32, %p5;

$L__BB9_11:
	setp.ge.f32 	%p30, %f1, 0f3F666666;
	selp.f32 	%f26, 0f3F800000, 0f3F008081, %p30;
	setp.le.f32 	%p31, %f1, 0f3DCCCCCD;
	selp.f32 	%f27, 0f00000000, %f26, %p31;
	selp.f32 	%f28, %f27, 0f3F008081, %p32;
	cvta.to.global.u64 	%rd11, %rd6;
	shl.b64 	%rd12, %rd2, 2;
	add.s64 	%rd13, %rd11, %rd12;
	st.global.f32 	[%rd13], %f28;

$L__BB9_12:
	ret;

}
	// .globl	prepareIndexNetInputKernel
.visible .entry prepareIndexNetInputKernel(
	.param .u64 prepareIndexNetInputKernel_param_0,
	.param .u64 prepareIndexNetInputKernel_param_1,
	.param .u64 prepareIndexNetInputKernel_param_2,
	.param .u32 prepareIndexNetInputKernel_param_3,
	.param .u32 prepareIndexNetInputKernel_param_4,
	.param .u32 prepareIndexNetInputKernel_param_5,
	.param .u32 prepareIndexNetInputKernel_param_6,
	.param .u32 prepareIndexNetInputKernel_param_7,
	.param .u32 prepareIndexNetInputKernel_param_8,
	.param .u32 prepareIndexNetInputKernel_param_9
)
{
	.reg .pred 	%p<4>;
	.reg .f32 	%f<5>;
	.reg .b32 	%r<30>;
	.reg .b64 	%rd<19>;


	ld.param.u64 	%rd1, [prepareIndexNetInputKernel_param_0];
	ld.param.u64 	%rd2, [prepareIndexNetInputKernel_param_1];
	ld.param.u64 	%rd3, [prepareIndexNetInputKernel_param_2];
	ld.param.u32 	%r3, [prepareIndexNetInputKernel_param_3];
	ld.param.u32 	%r4, [prepareIndexNetInputKernel_param_4];
	ld.param.u32 	%r5, [prepareIndexNetInputKernel_param_5];
	ld.param.u32 	%r6, [prepareIndexNetInputKernel_param_6];
	ld.param.u32 	%r7, [prepareIndexNetInputKernel_param_7];
	ld.param.u32 	%r8, [prepareIndexNetInputKernel_param_8];
	ld.param.u32 	%r9, [prepareIndexNetInputKernel_param_9];
	mov.u32 	%r10, %ntid.x;
	mov.u32 	%r11, %ctaid.x;
	mov.u32 	%r12, %tid.x;
	mad.lo.s32 	%r1, %r11, %r10, %r12;
	mov.u32 	%r13, %ntid.y;
	mov.u32 	%r14, %ctaid.y;
	mov.u32 	%r15, %tid.y;
	mad.lo.s32 	%r2, %r14, %r13, %r15;
	setp.ge.s32 	%p1, %r1, %r7;
	setp.ge.s32 	%p2, %r2, %r8;
	or.pred  	%p3, %p1, %p2;
	@%p3 bra 	$L__BB10_2;

	cvta.to.global.u64 	%rd4, %rd3;
	add.s32 	%r16, %r1, %r3;
	sub.s32 	%r17, %r16, %r9;
	add.s32 	%r18, %r2, %r4;
	sub.s32 	%r19, %r18, %r9;
	add.s32 	%r20, %r5, -1;
	min.s32 	%r21, %r20, %r17;
	max.s32 	%r22, %r21, 0;
	add.s32 	%r23, %r6, -1;
	min.s32 	%r24, %r23, %r19;
	max.s32 	%r25, %r24, 0;
	mad.lo.s32 	%r26, %r25, %r5, %r22;
	mad.lo.s32 	%r27, %r2, %r7, %r1;
	cvta.to.global.u64 	%rd5, %rd2;
	mul.wide.s32 	%rd6, %r26, 4;
	add.s64 	%rd7, %rd5, %rd6;
	ld.global.f32 	%f1, [%rd7];
	cvta.to.global.u64 	%rd8, %rd1;
	mul.wide.s32 	%rd9, %r27, 4;
	add.s64 	%rd10, %rd8, %rd9;
	st.global.f32 	[%rd10], %f1;
	mul.lo.s32 	%r28, %r6, %r5;
	mul.wide.s32 	%rd11, %r28, 4;
	add.s64 	%rd12, %rd7, %rd11;
	ld.global.f32 	%f2, [%rd12];
	mul.lo.s32 	%r29, %r8, %r7;
	mul.wide.s32 	%rd13, %r29, 4;
	add.s64 	%rd14, %rd10, %rd13;
	st.global.f32 	[%rd14], %f2;
	add.s64 	%rd15, %rd12, %rd11;
	ld.global.f32 	%f3, [%rd15];
	add.s64 	%rd16, %rd14, %rd13;
	st.global.f32 	[%rd16], %f3;
	add.s64 	%rd17, %rd4, %rd6;
	ld.global.f32 	%f4, [%rd17];
	add.s64 	%rd18, %rd16, %rd13;
	st.global.f32 	[%rd18], %f4;

$L__BB10_2:
	ret;

}
	// .globl	extractRegionKernel
.visible .entry extractRegionKernel(
	.param .u64 extractRegionKernel_param_0,
	.param .u64 extractRegionKernel_param_1,
	.param .u32 extractRegionKernel_param_2,
	.param .u32 extractRegionKernel_param_3,
	.param .u32 extractRegionKernel_param_4,
	.param .u32 extractRegionKernel_param_5,
	.param .u32 extractRegionKernel_param_6,
	.param .u32 extractRegionKernel_param_7,
	.param .u32 extractRegionKernel_param_8
)
{
	.reg .pred 	%p<13>;
	.reg .f32 	%f<6>;
	.reg .b32 	%r<66>;
	.reg .b64 	%rd<21>;


	ld.param.u64 	%rd5, [extractRegionKernel_param_0];
	ld.param.u64 	%rd6, [extractRegionKernel_param_1];
	ld.param.u32 	%r30, [extractRegionKernel_param_2];
	ld.param.u32 	%r31, [extractRegionKernel_param_3];
	ld.param.u32 	%r32, [extractRegionKernel_param_4];
	ld.param.u32 	%r33, [extractRegionKernel_param_5];
	ld.param.u32 	%r34, [extractRegionKernel_param_6];
	ld.param.u32 	%r35, [extractRegionKernel_param_7];
	ld.param.u32 	%r36, [extractRegionKernel_param_8];
	cvta.to.global.u64 	%rd1, %rd6;
	cvta.to.global.u64 	%rd2, %rd5;
	mov.u32 	%r37, %ntid.x;
	mov.u32 	%r38, %ctaid.x;
	mul.lo.s32 	%r1, %r38, %r37;
	mov.u32 	%r2, %tid.x;
	add.s32 	%r3, %r1, %r2;
	mov.u32 	%r39, %ntid.y;
	mov.u32 	%r40, %ctaid.y;
	mul.lo.s32 	%r4, %r40, %r39;
	mov.u32 	%r5, %tid.y;
	add.s32 	%r6, %r4, %r5;
	setp.ge.s32 	%p1, %r3, %r34;
	setp.ge.s32 	%p2, %r6, %r35;
	or.pred  	%p3, %p1, %p2;
	@%p3 bra 	$L__BB11_8;

	add.s32 	%r41, %r3, %r32;
	setp.ge.s32 	%p4, %r41, %r30;
	add.s32 	%r42, %r6, %r33;
	setp.ge.s32 	%p5, %r42, %r31;
	or.pred  	%p6, %p5, %p4;
	setp.lt.s32 	%p7, %r36, 1;
	or.pred  	%p8, %p6, %p7;
	@%p8 bra 	$L__BB11_8;

	add.s32 	%r44, %r36, -1;
	and.b32  	%r65, %r36, 3;
	setp.lt.u32 	%p9, %r44, 3;
	mov.u32 	%r62, 0;
	@%p9 bra 	$L__BB11_5;

	add.s32 	%r46, %r2, %r32;
	add.s32 	%r47, %r46, %r1;
	mul.lo.s32 	%r48, %r31, %r30;
	shl.b32 	%r8, %r48, 2;
	add.s32 	%r49, %r5, %r33;
	add.s32 	%r50, %r49, %r4;
	mad.lo.s32 	%r60, %r30, %r50, %r47;
	mul.lo.s32 	%r51, %r35, %r34;
	shl.b32 	%r10, %r51, 2;
	mad.lo.s32 	%r59, %r34, %r6, %r3;
	sub.s32 	%r12, %r65, %r36;
	mul.wide.s32 	%rd3, %r48, 4;
	mul.wide.s32 	%rd4, %r51, 4;
	mov.u32 	%r62, 0;

$L__BB11_4:
	mul.wide.s32 	%rd7, %r60, 4;
	add.s64 	%rd8, %rd2, %rd7;
	ld.global.f32 	%f1, [%rd8];
	mul.wide.s32 	%rd9, %r59, 4;
	add.s64 	%rd10, %rd1, %rd9;
	st.global.f32 	[%rd10], %f1;
	add.s64 	%rd11, %rd8, %rd3;
	ld.global.f32 	%f2, [%rd11];
	add.s64 	%rd12, %rd10, %rd4;
	st.global.f32 	[%rd12], %f2;
	add.s64 	%rd13, %rd11, %rd3;
	ld.global.f32 	%f3, [%rd13];
	add.s64 	%rd14, %rd12, %rd4;
	st.global.f32 	[%rd14], %f3;
	add.s64 	%rd15, %rd13, %rd3;
	ld.global.f32 	%f4, [%rd15];
	add.s64 	%rd16, %rd14, %rd4;
	st.global.f32 	[%rd16], %f4;
	add.s32 	%r60, %r60, %r8;
	add.s32 	%r59, %r59, %r10;
	add.s32 	%r62, %r62, 4;
	add.s32 	%r52, %r12, %r62;
	setp.ne.s32 	%p10, %r52, 0;
	@%p10 bra 	$L__BB11_4;

$L__BB11_5:
	setp.eq.s32 	%p11, %r65, 0;
	@%p11 bra 	$L__BB11_8;

	mad.lo.s32 	%r53, %r62, %r35, %r6;
	mad.lo.s32 	%r64, %r34, %r53, %r3;
	mul.lo.s32 	%r21, %r35, %r34;
	add.s32 	%r54, %r2, %r32;
	add.s32 	%r55, %r54, %r1;
	add.s32 	%r56, %r5, %r33;
	add.s32 	%r57, %r56, %r4;
	mad.lo.s32 	%r58, %r62, %r31, %r57;
	mad.lo.s32 	%r63, %r30, %r58, %r55;
	mul.lo.s32 	%r23, %r31, %r30;

$L__BB11_7:
	.pragma "nounroll";
	mul.wide.s32 	%rd17, %r63, 4;
	add.s64 	%rd18, %rd2, %rd17;
	ld.global.f32 	%f5, [%rd18];
	mul.wide.s32 	%rd19, %r64, 4;
	add.s64 	%rd20, %rd1, %rd19;
	st.global.f32 	[%rd20], %f5;
	add.s32 	%r64, %r64, %r21;
	add.s32 	%r63, %r63, %r23;
	add.s32 	%r65, %r65, -1;
	setp.ne.s32 	%p12, %r65, 0;
	@%p12 bra 	$L__BB11_7;

$L__BB11_8:
	ret;

}
	// .globl	blendAlphaRegionKernel
.visible .entry blendAlphaRegionKernel(
	.param .u64 blendAlphaRegionKernel_param_0,
	.param .u64 blendAlphaRegionKernel_param_1,
	.param .u32 blendAlphaRegionKernel_param_2,
	.param .u32 blendAlphaRegionKernel_param_3,
	.param .u32 blendAlphaRegionKernel_param_4,
	.param .u32 blendAlphaRegionKernel_param_5,
	.param .u32 blendAlphaRegionKernel_param_6,
	.param .u32 blendAlphaRegionKernel_param_7
)
{
	.reg .pred 	%p<7>;
	.reg .f32 	%f<2>;
	.reg .b32 	%r<19>;
	.reg .b64 	%rd<9>;


	ld.param.u64 	%rd1, [blendAlphaRegionKernel_param_0];
	ld.param.u64 	%rd2, [blendAlphaRegionKernel_param_1];
	ld.param.u32 	%r5, [blendAlphaRegionKernel_param_2];
	ld.param.u32 	%r6, [blendAlphaRegionKernel_param_3];
	ld.param.u32 	%r7, [blendAlphaRegionKernel_param_4];
	ld.param.u32 	%r8, [blendAlphaRegionKernel_param_5];
	ld.param.u32 	%r9, [blendAlphaRegionKernel_param_6];
	ld.param.u32 	%r10, [blendAlphaRegionKernel_param_7];
	mov.u32 	%r11, %ntid.x;
	mov.u32 	%r12, %ctaid.x;
	mov.u32 	%r13, %tid.x;
	mad.lo.s32 	%r1, %r12, %r11, %r13;
	mov.u32 	%r14, %ntid.y;
	mov.u32 	%r15, %ctaid.y;
	mov.u32 	%r16, %tid.y;
	mad.lo.s32 	%r2, %r15, %r14, %r16;
	setp.ge.s32 	%p1, %r1, %r9;
	setp.ge.s32 	%p2, %r2, %r10;
	or.pred  	%p3, %p1, %p2;
	@%p3 bra 	$L__BB12_3;

	add.s32 	%r3, %r1, %r7;
	setp.ge.s32 	%p4, %r3, %r5;
	add.s32 	%r4, %r2, %r8;
	setp.ge.s32 	%p5, %r4, %r6;
	or.pred  	%p6, %p4, %p5;
	@%p6 bra 	$L__BB12_3;

	mad.lo.s32 	%r17, %r4, %r5, %r3;
	mad.lo.s32 	%r18, %r2, %r9, %r1;
	cvta.to.global.u64 	%rd3, %rd2;
	mul.wide.s32 	%rd4, %r18, 4;
	add.s64 	%rd5, %rd3, %rd4;
	ld.global.f32 	%f1, [%rd5];
	cvta.to.global.u64 	%rd6, %rd1;
	mul.wide.s32 	%rd7, %r17, 4;
	add.s64 	%rd8, %rd6, %rd7;
	st.global.f32 	[%rd8], %f1;

$L__BB12_3:
	ret;

}
	// .globl	_Z36updateAlphaBufferRegionKernelTexturePfyPKfS1_iiiiii
.visible .entry _Z36updateAlphaBufferRegionKernelTexturePfyPKfS1_iiiiii(
	.param .u64 _Z36updateAlphaBufferRegionKernelTexturePfyPKfS1_iiiiii_param_0,
	.param .u64 _Z36updateAlphaBufferRegionKernelTexturePfyPKfS1_iiiiii_param_1,
	.param .u64 _Z36updateAlphaBufferRegionKernelTexturePfyPKfS1_iiiiii_param_2,
	.param .u64 _Z36updateAlphaBufferRegionKernelTexturePfyPKfS1_iiiiii_param_3,
	.param .u32 _Z36updateAlphaBufferRegionKernelTexturePfyPKfS1_iiiiii_param_4,
	.param .u32 _Z36updateAlphaBufferRegionKernelTexturePfyPKfS1_iiiiii_param_5,
	.param .u32 _Z36updateAlphaBufferRegionKernelTexturePfyPKfS1_iiiiii_param_6,
	.param .u32 _Z36updateAlphaBufferRegionKernelTexturePfyPKfS1_iiiiii_param_7,
	.param .u32 _Z36updateAlphaBufferRegionKernelTexturePfyPKfS1_iiiiii_param_8,
	.param .u32 _Z36updateAlphaBufferRegionKernelTexturePfyPKfS1_iiiiii_param_9
)
{
	.reg .pred 	%p<10>;
	.reg .f32 	%f<11>;
	.reg .b32 	%r<23>;
	.reg .b64 	%rd<13>;


	ld.param.u64 	%rd2, [_Z36updateAlphaBufferRegionKernelTexturePfyPKfS1_iiiiii_param_0];
	ld.param.u64 	%rd3, [_Z36updateAlphaBufferRegionKernelTexturePfyPKfS1_iiiiii_param_1];
	ld.param.u64 	%rd4, [_Z36updateAlphaBufferRegionKernelTexturePfyPKfS1_iiiiii_param_2];
	ld.param.u64 	%rd5, [_Z36updateAlphaBufferRegionKernelTexturePfyPKfS1_iiiiii_param_3];
	ld.param.u32 	%r5, [_Z36updateAlphaBufferRegionKernelTexturePfyPKfS1_iiiiii_param_4];
	ld.param.u32 	%r6, [_Z36updateAlphaBufferRegionKernelTexturePfyPKfS1_iiiiii_param_5];
	ld.param.u32 	%r7, [_Z36updateAlphaBufferRegionKernelTexturePfyPKfS1_iiiiii_param_6];
	ld.param.u32 	%r8, [_Z36updateAlphaBufferRegionKernelTexturePfyPKfS1_iiiiii_param_7];
	ld.param.u32 	%r9, [_Z36updateAlphaBufferRegionKernelTexturePfyPKfS1_iiiiii_param_8];
	ld.param.u32 	%r10, [_Z36updateAlphaBufferRegionKernelTexturePfyPKfS1_iiiiii_param_9];
	mov.u32 	%r11, %ntid.x;
	mov.u32 	%r12, %ctaid.x;
	mov.u32 	%r13, %tid.x;
	mad.lo.s32 	%r1, %r12, %r11, %r13;
	mov.u32 	%r14, %ntid.y;
	mov.u32 	%r15, %ctaid.y;
	mov.u32 	%r16, %tid.y;
	mad.lo.s32 	%r2, %r15, %r14, %r16;
	setp.ge.s32 	%p1, %r1, %r9;
	setp.ge.s32 	%p2, %r2, %r9;
	or.pred  	%p3, %p2, %p1;
	@%p3 bra 	$L__BB13_5;

	add.s32 	%r3, %r1, %r5;
	setp.ge.s32 	%p4, %r3, %r7;
	add.s32 	%r4, %r2, %r6;
	setp.ge.s32 	%p5, %r4, %r8;
	or.pred  	%p6, %p4, %p5;
	@%p6 bra 	$L__BB13_5;

	mad.lo.s32 	%r17, %r4, %r7, %r3;
	cvta.to.global.u64 	%rd6, %rd4;
	mul.wide.s32 	%rd7, %r17, 4;
	add.s64 	%rd8, %rd6, %rd7;
	ld.global.f32 	%f1, [%rd8];
	setp.gt.f32 	%p7, %f1, 0f3ECCCCCD;
	setp.lt.f32 	%p8, %f1, 0f3F19999A;
	and.pred  	%p9, %p7, %p8;
	cvta.to.global.u64 	%rd9, %rd2;
	add.s64 	%rd1, %rd9, %rd7;
	@%p9 bra 	$L__BB13_4;
	bra.uni 	$L__BB13_3;

$L__BB13_4:
	add.s32 	%r18, %r2, %r10;
	shl.b32 	%r19, %r10, 1;
	add.s32 	%r20, %r19, %r9;
	add.s32 	%r21, %r1, %r10;
	mad.lo.s32 	%r22, %r20, %r18, %r21;
	cvta.to.global.u64 	%rd10, %rd5;
	mul.wide.s32 	%rd11, %r22, 4;
	add.s64 	%rd12, %rd10, %rd11;
	ld.global.f32 	%f10, [%rd12];
	st.global.f32 	[%rd1], %f10;
	bra.uni 	$L__BB13_5;

$L__BB13_3:
	cvt.rn.f32.s32 	%f2, %r3;
	add.f32 	%f3, %f2, 0f3F000000;
	cvt.rn.f32.s32 	%f4, %r4;
	add.f32 	%f5, %f4, 0f3F000000;
	tex.2d.v4.f32.f32 	{%f6, %f7, %f8, %f9}, [%rd3, {%f3, %f5}];
	st.global.f32 	[%rd1], %f6;

$L__BB13_5:
	ret;

}
	// .globl	PreprocessBufferKernel
.visible .entry PreprocessBufferKernel(
	.param .u64 PreprocessBufferKernel_param_0,
	.param .u64 PreprocessBufferKernel_param_1,
	.param .u32 PreprocessBufferKernel_param_2,
	.param .u32 PreprocessBufferKernel_param_3,
	.param .u8 PreprocessBufferKernel_param_4,
	.param .align 4 .b8 PreprocessBufferKernel_param_5[24]
)
{
	.reg .pred 	%p<6>;
	.reg .b16 	%rs<2>;
	.reg .f32 	%f<20>;
	.reg .b32 	%r<21>;
	.reg .b64 	%rd<26>;


	ld.param.s8 	%rs1, [PreprocessBufferKernel_param_4];
	ld.param.u64 	%rd4, [PreprocessBufferKernel_param_0];
	ld.param.u64 	%rd5, [PreprocessBufferKernel_param_1];
	ld.param.u32 	%r3, [PreprocessBufferKernel_param_2];
	ld.param.u32 	%r4, [PreprocessBufferKernel_param_3];
	ld.param.f32 	%f11, [PreprocessBufferKernel_param_5+20];
	ld.param.f32 	%f10, [PreprocessBufferKernel_param_5+16];
	ld.param.f32 	%f9, [PreprocessBufferKernel_param_5+12];
	ld.param.f32 	%f8, [PreprocessBufferKernel_param_5+8];
	ld.param.f32 	%f7, [PreprocessBufferKernel_param_5+4];
	ld.param.f32 	%f6, [PreprocessBufferKernel_param_5];
	mov.u32 	%r5, %ntid.x;
	mov.u32 	%r6, %ctaid.x;
	mov.u32 	%r7, %tid.x;
	mad.lo.s32 	%r1, %r6, %r5, %r7;
	mov.u32 	%r8, %ntid.y;
	mov.u32 	%r9, %ctaid.y;
	mov.u32 	%r10, %tid.y;
	mad.lo.s32 	%r2, %r9, %r8, %r10;
	setp.ge.s32 	%p1, %r1, %r3;
	setp.ge.s32 	%p2, %r2, %r4;
	or.pred  	%p3, %p1, %p2;
	@%p3 bra 	$L__BB14_5;

	cvta.to.global.u64 	%rd6, %rd5;
	mad.lo.s32 	%r11, %r2, %r3, %r1;
	cvt.s64.s32 	%rd1, %r11;
	mul.wide.s32 	%rd7, %r11, 4;
	add.s64 	%rd8, %rd6, %rd7;
	ld.global.f32 	%f1, [%rd8];
	mul.lo.s32 	%r12, %r4, %r3;
	add.s32 	%r13, %r11, %r12;
	cvt.s64.s32 	%rd2, %r12;
	mul.wide.s32 	%rd9, %r12, 4;
	add.s64 	%rd10, %rd8, %rd9;
	ld.global.f32 	%f2, [%rd10];
	add.s32 	%r14, %r13, %r12;
	cvt.s64.s32 	%rd3, %r14;
	mul.wide.s32 	%rd11, %r14, 4;
	add.s64 	%rd12, %rd6, %rd11;
	ld.global.f32 	%f3, [%rd12];
	setp.eq.s16 	%p4, %rs1, 0;
	mov.f32 	%f19, 0f00000000;
	@%p4 bra 	$L__BB14_3;

	cvt.u32.u64 	%r15, %rd2;
	cvt.u32.u64 	%r16, %rd3;
	add.s32 	%r17, %r16, %r15;
	mul.wide.s32 	%rd14, %r17, 4;
	add.s64 	%rd15, %rd6, %rd14;
	ld.global.f32 	%f19, [%rd15];

$L__BB14_3:
	sub.f32 	%f13, %f1, %f6;
	div.rn.f32 	%f14, %f13, %f9;
	cvta.to.global.u64 	%rd16, %rd4;
	shl.b64 	%rd17, %rd1, 2;
	add.s64 	%rd18, %rd16, %rd17;
	st.global.f32 	[%rd18], %f14;
	sub.f32 	%f15, %f2, %f7;
	div.rn.f32 	%f16, %f15, %f10;
	shl.b64 	%rd19, %rd2, 2;
	add.s64 	%rd20, %rd18, %rd19;
	st.global.f32 	[%rd20], %f16;
	sub.f32 	%f17, %f3, %f8;
	div.rn.f32 	%f18, %f17, %f11;
	shl.b64 	%rd21, %rd3, 2;
	add.s64 	%rd22, %rd16, %rd21;
	st.global.f32 	[%rd22], %f18;
	@%p4 bra 	$L__BB14_5;

	cvt.u32.u64 	%r18, %rd3;
	cvt.u32.u64 	%r19, %rd2;
	add.s32 	%r20, %r18, %r19;
	mul.wide.s32 	%rd24, %r20, 4;
	add.s64 	%rd25, %rd16, %rd24;
	st.global.f32 	[%rd25], %f19;

$L__BB14_5:
	ret;

}
	// .globl	_Z20BicubicUpscaleKernelPfiiPKfiii
.visible .entry _Z20BicubicUpscaleKernelPfiiPKfiii(
	.param .u64 _Z20BicubicUpscaleKernelPfiiPKfiii_param_0,
	.param .u32 _Z20BicubicUpscaleKernelPfiiPKfiii_param_1,
	.param .u32 _Z20BicubicUpscaleKernelPfiiPKfiii_param_2,
	.param .u64 _Z20BicubicUpscaleKernelPfiiPKfiii_param_3,
	.param .u32 _Z20BicubicUpscaleKernelPfiiPKfiii_param_4,
	.param .u32 _Z20BicubicUpscaleKernelPfiiPKfiii_param_5,
	.param .u32 _Z20BicubicUpscaleKernelPfiiPKfiii_param_6
)
{
	.reg .pred 	%p<40>;
	.reg .f32 	%f<258>;
	.reg .b32 	%r<62>;
	.reg .b64 	%rd<14>;


	ld.param.u64 	%rd7, [_Z20BicubicUpscaleKernelPfiiPKfiii_param_0];
	ld.param.u32 	%r25, [_Z20BicubicUpscaleKernelPfiiPKfiii_param_1];
	ld.param.u32 	%r26, [_Z20BicubicUpscaleKernelPfiiPKfiii_param_2];
	ld.param.u64 	%rd8, [_Z20BicubicUpscaleKernelPfiiPKfiii_param_3];
	ld.param.u32 	%r27, [_Z20BicubicUpscaleKernelPfiiPKfiii_param_4];
	ld.param.u32 	%r28, [_Z20BicubicUpscaleKernelPfiiPKfiii_param_5];
	ld.param.u32 	%r29, [_Z20BicubicUpscaleKernelPfiiPKfiii_param_6];
	cvta.to.global.u64 	%rd1, %rd8;
	mov.u32 	%r30, %ntid.x;
	mov.u32 	%r31, %ctaid.x;
	mov.u32 	%r32, %tid.x;
	mad.lo.s32 	%r1, %r31, %r30, %r32;
	mov.u32 	%r33, %ntid.y;
	mov.u32 	%r34, %ctaid.y;
	mov.u32 	%r35, %tid.y;
	mad.lo.s32 	%r2, %r34, %r33, %r35;
	setp.ge.s32 	%p1, %r1, %r25;
	setp.ge.s32 	%p2, %r2, %r26;
	or.pred  	%p3, %p1, %p2;
	@%p3 bra 	$L__BB15_58;

	cvt.rn.f32.s32 	%f86, %r27;
	cvt.rn.f32.s32 	%f87, %r25;
	div.rn.f32 	%f88, %f86, %f87;
	cvt.rn.f32.s32 	%f89, %r26;
	cvt.rn.f32.s32 	%f90, %r28;
	div.rn.f32 	%f91, %f90, %f89;
	cvt.rn.f32.s32 	%f92, %r1;
	add.f32 	%f93, %f92, 0f3F000000;
	fma.rn.f32 	%f1, %f93, %f88, 0fBF000000;
	cvt.rn.f32.s32 	%f94, %r2;
	add.f32 	%f95, %f94, 0f3F000000;
	fma.rn.f32 	%f2, %f95, %f91, 0fBF000000;
	cvt.rmi.f32.f32 	%f96, %f1;
	cvt.rzi.s32.f32 	%r36, %f96;
	add.s32 	%r37, %r36, -1;
	max.s32 	%r3, %r37, 0;
	cvt.rpi.f32.f32 	%f97, %f1;
	cvt.rzi.s32.f32 	%r4, %f97;
	add.s32 	%r38, %r4, 2;
	add.s32 	%r39, %r27, -1;
	min.s32 	%r5, %r39, %r38;
	cvt.rmi.f32.f32 	%f98, %f2;
	cvt.rzi.s32.f32 	%r40, %f98;
	add.s32 	%r41, %r40, -1;
	max.s32 	%r6, %r41, 0;
	cvt.rpi.f32.f32 	%f99, %f2;
	cvt.rzi.s32.f32 	%r42, %f99;
	add.s32 	%r43, %r42, 2;
	add.s32 	%r44, %r28, -1;
	min.s32 	%r7, %r44, %r43;
	setp.lt.s32 	%p4, %r29, 1;
	@%p4 bra 	$L__BB15_58;

	neg.s32 	%r46, %r3;
	mov.u32 	%r58, 0;
	mov.u32 	%r47, -3;
	sub.s32 	%r48, %r47, %r4;
	neg.s32 	%r49, %r27;
	max.s32 	%r50, %r48, %r49;
	sub.s32 	%r51, %r46, %r50;
	not.b32 	%r52, %r3;
	sub.s32 	%r8, %r52, %r50;
	and.b32  	%r9, %r51, 3;
	cvt.rn.f32.s32 	%f100, %r3;
	sub.f32 	%f3, %f1, %f100;
	add.s32 	%r10, %r3, 1;
	cvt.rn.f32.s32 	%f101, %r10;
	sub.f32 	%f4, %f1, %f101;
	add.s32 	%r11, %r3, 2;
	cvt.rn.f32.s32 	%f102, %r11;
	sub.f32 	%f5, %f1, %f102;
	add.s32 	%r12, %r3, 3;
	cvta.to.global.u64 	%rd2, %rd7;
	abs.f32 	%f11, %f3;
	abs.f32 	%f21, %f4;
	abs.f32 	%f30, %f5;

$L__BB15_3:
	setp.gt.s32 	%p5, %r6, %r7;
	mov.f32 	%f257, 0f00000000;
	mov.f32 	%f242, %f257;
	mov.f32 	%f243, %f257;
	@%p5 bra 	$L__BB15_55;

	mul.lo.s32 	%r14, %r58, %r28;
	mov.f32 	%f243, 0f00000000;
	mov.u32 	%r59, %r6;
	mov.f32 	%f242, %f243;

$L__BB15_5:
	mov.u32 	%r15, %r59;
	setp.gt.s32 	%p6, %r3, %r5;
	@%p6 bra 	$L__BB15_54;

	setp.eq.s32 	%p7, %r9, 0;
	cvt.rn.f32.s32 	%f108, %r15;
	sub.f32 	%f109, %f2, %f108;
	abs.f32 	%f8, %f109;
	mul.f32 	%f110, %f8, 0fBF000000;
	mul.f32 	%f111, %f8, %f110;
	mul.f32 	%f112, %f8, 0f40200000;
	mul.f32 	%f113, %f8, %f112;
	fma.rn.f32 	%f114, %f8, %f111, %f113;
	fma.rn.f32 	%f115, %f8, 0fC0800000, %f114;
	add.f32 	%f9, %f115, 0f40000000;
	mul.f32 	%f116, %f8, 0f3FC00000;
	mul.f32 	%f117, %f8, %f116;
	mul.f32 	%f118, %f8, %f117;
	sub.f32 	%f119, %f118, %f113;
	add.f32 	%f10, %f119, 0f3F800000;
	add.s32 	%r53, %r15, %r14;
	mul.lo.s32 	%r16, %r53, %r27;
	mov.u32 	%r60, %r3;
	@%p7 bra 	$L__BB15_27;

	setp.ge.f32 	%p8, %f11, 0f40000000;
	mov.f32 	%f228, 0f00000000;
	@%p8 bra 	$L__BB15_11;

	setp.gtu.f32 	%p9, %f11, 0f3F800000;
	@%p9 bra 	$L__BB15_10;
	bra.uni 	$L__BB15_9;

$L__BB15_10:
	mul.f32 	%f126, %f11, 0fBF000000;
	mul.f32 	%f127, %f11, %f126;
	mul.f32 	%f128, %f11, %f127;
	mul.f32 	%f129, %f11, 0f40200000;
	fma.rn.f32 	%f130, %f11, %f129, %f128;
	fma.rn.f32 	%f131, %f11, 0fC0800000, %f130;
	add.f32 	%f228, %f131, 0f40000000;
	bra.uni 	$L__BB15_11;

$L__BB15_9:
	mul.f32 	%f121, %f11, 0f3FC00000;
	mul.f32 	%f122, %f11, %f121;
	mul.f32 	%f123, %f11, 0fC0200000;
	mul.f32 	%f124, %f11, %f123;
	fma.rn.f32 	%f125, %f11, %f122, %f124;
	add.f32 	%f228, %f125, 0f3F800000;

$L__BB15_11:
	setp.gtu.f32 	%p10, %f8, 0f3F800000;
	selp.f32 	%f132, %f9, %f10, %p10;
	setp.ltu.f32 	%p11, %f8, 0f40000000;
	selp.f32 	%f15, %f132, 0f00000000, %p11;
	mul.f32 	%f16, %f228, %f15;
	setp.eq.f32 	%p12, %f16, 0f00000000;
	add.s32 	%r54, %r3, %r16;
	mul.wide.s32 	%rd9, %r54, 4;
	add.s64 	%rd3, %rd1, %rd9;
	@%p12 bra 	$L__BB15_13;

	ld.global.f32 	%f133, [%rd3];
	fma.rn.f32 	%f242, %f16, %f133, %f242;
	add.f32 	%f243, %f243, %f16;

$L__BB15_13:
	setp.eq.s32 	%p13, %r9, 1;
	mov.u32 	%r60, %r10;
	@%p13 bra 	$L__BB15_27;

	setp.ge.f32 	%p14, %f21, 0f40000000;
	mov.f32 	%f231, 0f00000000;
	@%p14 bra 	$L__BB15_18;

	setp.gtu.f32 	%p15, %f21, 0f3F800000;
	@%p15 bra 	$L__BB15_17;
	bra.uni 	$L__BB15_16;

$L__BB15_17:
	mul.f32 	%f140, %f21, 0fBF000000;
	mul.f32 	%f141, %f21, %f140;
	mul.f32 	%f142, %f21, %f141;
	mul.f32 	%f143, %f21, 0f40200000;
	fma.rn.f32 	%f144, %f21, %f143, %f142;
	fma.rn.f32 	%f145, %f21, 0fC0800000, %f144;
	add.f32 	%f231, %f145, 0f40000000;
	bra.uni 	$L__BB15_18;

$L__BB15_16:
	mul.f32 	%f135, %f21, 0f3FC00000;
	mul.f32 	%f136, %f21, %f135;
	mul.f32 	%f137, %f21, 0fC0200000;
	mul.f32 	%f138, %f21, %f137;
	fma.rn.f32 	%f139, %f21, %f136, %f138;
	add.f32 	%f231, %f139, 0f3F800000;

$L__BB15_18:
	mul.f32 	%f25, %f231, %f15;
	setp.eq.f32 	%p16, %f25, 0f00000000;
	@%p16 bra 	$L__BB15_20;

	ld.global.f32 	%f146, [%rd3+4];
	fma.rn.f32 	%f242, %f25, %f146, %f242;
	add.f32 	%f243, %f243, %f25;

$L__BB15_20:
	setp.eq.s32 	%p17, %r9, 2;
	mov.u32 	%r60, %r11;
	@%p17 bra 	$L__BB15_27;

	setp.ge.f32 	%p18, %f30, 0f40000000;
	mov.f32 	%f234, 0f00000000;
	@%p18 bra 	$L__BB15_25;

	setp.gtu.f32 	%p19, %f30, 0f3F800000;
	@%p19 bra 	$L__BB15_24;
	bra.uni 	$L__BB15_23;

$L__BB15_24:
	mul.f32 	%f153, %f30, 0fBF000000;
	mul.f32 	%f154, %f30, %f153;
	mul.f32 	%f155, %f30, %f154;
	mul.f32 	%f156, %f30, 0f40200000;
	fma.rn.f32 	%f157, %f30, %f156, %f155;
	fma.rn.f32 	%f158, %f30, 0fC0800000, %f157;
	add.f32 	%f234, %f158, 0f40000000;
	bra.uni 	$L__BB15_25;

$L__BB15_23:
	mul.f32 	%f148, %f30, 0f3FC00000;
	mul.f32 	%f149, %f30, %f148;
	mul.f32 	%f150, %f30, 0fC0200000;
	mul.f32 	%f151, %f30, %f150;
	fma.rn.f32 	%f152, %f30, %f149, %f151;
	add.f32 	%f234, %f152, 0f3F800000;

$L__BB15_25:
	mul.f32 	%f34, %f234, %f15;
	setp.eq.f32 	%p20, %f34, 0f00000000;
	mov.u32 	%r60, %r12;
	@%p20 bra 	$L__BB15_27;

	ld.global.f32 	%f159, [%rd3+8];
	fma.rn.f32 	%f242, %f34, %f159, %f242;
	add.f32 	%f243, %f243, %f34;
	mov.u32 	%r60, %r12;

$L__BB15_27:
	setp.lt.u32 	%p21, %r8, 3;
	@%p21 bra 	$L__BB15_54;

	setp.gtu.f32 	%p22, %f8, 0f3F800000;
	selp.f32 	%f160, %f9, %f10, %p22;
	setp.ltu.f32 	%p23, %f8, 0f40000000;
	selp.f32 	%f41, %f160, 0f00000000, %p23;
	add.s32 	%r55, %r60, %r16;
	mul.wide.s32 	%rd10, %r55, 4;
	add.s64 	%rd13, %rd1, %rd10;

$L__BB15_29:
	cvt.rn.f32.s32 	%f162, %r60;
	sub.f32 	%f163, %f1, %f162;
	abs.f32 	%f44, %f163;
	setp.ge.f32 	%p24, %f44, 0f40000000;
	mov.f32 	%f241, 0f00000000;
	@%p24 bra 	$L__BB15_33;

	setp.gtu.f32 	%p25, %f44, 0f3F800000;
	@%p25 bra 	$L__BB15_32;
	bra.uni 	$L__BB15_31;

$L__BB15_32:
	mul.f32 	%f169, %f44, 0fBF000000;
	mul.f32 	%f170, %f44, %f169;
	mul.f32 	%f171, %f44, %f170;
	mul.f32 	%f172, %f44, 0f40200000;
	fma.rn.f32 	%f173, %f44, %f172, %f171;
	fma.rn.f32 	%f174, %f44, 0fC0800000, %f173;
	add.f32 	%f241, %f174, 0f40000000;
	bra.uni 	$L__BB15_33;

$L__BB15_31:
	mul.f32 	%f164, %f44, 0f3FC00000;
	mul.f32 	%f165, %f44, %f164;
	mul.f32 	%f166, %f44, 0fC0200000;
	mul.f32 	%f167, %f44, %f166;
	fma.rn.f32 	%f168, %f44, %f165, %f167;
	add.f32 	%f241, %f168, 0f3F800000;

$L__BB15_33:
	mul.f32 	%f48, %f241, %f41;
	setp.eq.f32 	%p26, %f48, 0f00000000;
	@%p26 bra 	$L__BB15_35;

	ld.global.f32 	%f175, [%rd13];
	fma.rn.f32 	%f242, %f48, %f175, %f242;
	add.f32 	%f243, %f243, %f48;

$L__BB15_35:
	add.s32 	%r19, %r60, 1;
	cvt.rn.f32.s32 	%f177, %r19;
	sub.f32 	%f178, %f1, %f177;
	abs.f32 	%f53, %f178;
	setp.ge.f32 	%p27, %f53, 0f40000000;
	mov.f32 	%f244, 0f00000000;
	@%p27 bra 	$L__BB15_39;

	setp.gtu.f32 	%p28, %f53, 0f3F800000;
	@%p28 bra 	$L__BB15_38;
	bra.uni 	$L__BB15_37;

$L__BB15_38:
	mul.f32 	%f184, %f53, 0fBF000000;
	mul.f32 	%f185, %f53, %f184;
	mul.f32 	%f186, %f53, %f185;
	mul.f32 	%f187, %f53, 0f40200000;
	fma.rn.f32 	%f188, %f53, %f187, %f186;
	fma.rn.f32 	%f189, %f53, 0fC0800000, %f188;
	add.f32 	%f244, %f189, 0f40000000;
	bra.uni 	$L__BB15_39;

$L__BB15_37:
	mul.f32 	%f179, %f53, 0f3FC00000;
	mul.f32 	%f180, %f53, %f179;
	mul.f32 	%f181, %f53, 0fC0200000;
	mul.f32 	%f182, %f53, %f181;
	fma.rn.f32 	%f183, %f53, %f180, %f182;
	add.f32 	%f244, %f183, 0f3F800000;

$L__BB15_39:
	mul.f32 	%f57, %f244, %f41;
	setp.eq.f32 	%p29, %f57, 0f00000000;
	@%p29 bra 	$L__BB15_41;

	ld.global.f32 	%f190, [%rd13+4];
	fma.rn.f32 	%f242, %f57, %f190, %f242;
	add.f32 	%f243, %f243, %f57;

$L__BB15_41:
	add.s32 	%r20, %r19, 1;
	cvt.rn.f32.s32 	%f192, %r20;
	sub.f32 	%f193, %f1, %f192;
	abs.f32 	%f62, %f193;
	setp.ge.f32 	%p30, %f62, 0f40000000;
	mov.f32 	%f247, 0f00000000;
	@%p30 bra 	$L__BB15_45;

	setp.gtu.f32 	%p31, %f62, 0f3F800000;
	@%p31 bra 	$L__BB15_44;
	bra.uni 	$L__BB15_43;

$L__BB15_44:
	mul.f32 	%f199, %f62, 0fBF000000;
	mul.f32 	%f200, %f62, %f199;
	mul.f32 	%f201, %f62, %f200;
	mul.f32 	%f202, %f62, 0f40200000;
	fma.rn.f32 	%f203, %f62, %f202, %f201;
	fma.rn.f32 	%f204, %f62, 0fC0800000, %f203;
	add.f32 	%f247, %f204, 0f40000000;
	bra.uni 	$L__BB15_45;

$L__BB15_43:
	mul.f32 	%f194, %f62, 0f3FC00000;
	mul.f32 	%f195, %f62, %f194;
	mul.f32 	%f196, %f62, 0fC0200000;
	mul.f32 	%f197, %f62, %f196;
	fma.rn.f32 	%f198, %f62, %f195, %f197;
	add.f32 	%f247, %f198, 0f3F800000;

$L__BB15_45:
	mul.f32 	%f66, %f247, %f41;
	setp.eq.f32 	%p32, %f66, 0f00000000;
	@%p32 bra 	$L__BB15_47;

	ld.global.f32 	%f205, [%rd13+8];
	fma.rn.f32 	%f242, %f66, %f205, %f242;
	add.f32 	%f243, %f243, %f66;

$L__BB15_47:
	add.s32 	%r21, %r20, 1;
	cvt.rn.f32.s32 	%f207, %r21;
	sub.f32 	%f208, %f1, %f207;
	abs.f32 	%f71, %f208;
	setp.ge.f32 	%p33, %f71, 0f40000000;
	mov.f32 	%f250, 0f00000000;
	@%p33 bra 	$L__BB15_51;

	setp.gtu.f32 	%p34, %f71, 0f3F800000;
	@%p34 bra 	$L__BB15_50;
	bra.uni 	$L__BB15_49;

$L__BB15_50:
	mul.f32 	%f214, %f71, 0fBF000000;
	mul.f32 	%f215, %f71, %f214;
	mul.f32 	%f216, %f71, %f215;
	mul.f32 	%f217, %f71, 0f40200000;
	fma.rn.f32 	%f218, %f71, %f217, %f216;
	fma.rn.f32 	%f219, %f71, 0fC0800000, %f218;
	add.f32 	%f250, %f219, 0f40000000;
	bra.uni 	$L__BB15_51;

$L__BB15_49:
	mul.f32 	%f209, %f71, 0f3FC00000;
	mul.f32 	%f210, %f71, %f209;
	mul.f32 	%f211, %f71, 0fC0200000;
	mul.f32 	%f212, %f71, %f211;
	fma.rn.f32 	%f213, %f71, %f210, %f212;
	add.f32 	%f250, %f213, 0f3F800000;

$L__BB15_51:
	mul.f32 	%f75, %f250, %f41;
	setp.eq.f32 	%p35, %f75, 0f00000000;
	@%p35 bra 	$L__BB15_53;

	ld.global.f32 	%f220, [%rd13+12];
	fma.rn.f32 	%f242, %f75, %f220, %f242;
	add.f32 	%f243, %f243, %f75;

$L__BB15_53:
	add.s64 	%rd13, %rd13, 16;
	add.s32 	%r60, %r21, 1;
	setp.lt.s32 	%p36, %r21, %r5;
	@%p36 bra 	$L__BB15_29;

$L__BB15_54:
	add.s32 	%r59, %r15, 1;
	setp.lt.s32 	%p37, %r15, %r7;
	@%p37 bra 	$L__BB15_5;

$L__BB15_55:
	setp.leu.f32 	%p38, %f243, 0f00000000;
	@%p38 bra 	$L__BB15_57;

	div.rn.f32 	%f257, %f242, %f243;

$L__BB15_57:
	mov.f32 	%f222, 0f3F800000;
	min.f32 	%f223, %f222, %f257;
	mov.f32 	%f224, 0f00000000;
	max.f32 	%f225, %f224, %f223;
	mad.lo.s32 	%r56, %r58, %r26, %r2;
	mad.lo.s32 	%r57, %r56, %r25, %r1;
	mul.wide.s32 	%rd11, %r57, 4;
	add.s64 	%rd12, %rd2, %rd11;
	st.global.f32 	[%rd12], %f225;
	add.s32 	%r58, %r58, 1;
	setp.lt.s32 	%p39, %r58, %r29;
	@%p39 bra 	$L__BB15_3;

$L__BB15_58:
	ret;

}
	// .globl	_Z25EdgeDirectedUpscaleKernelPfiiPKfiii
.visible .entry _Z25EdgeDirectedUpscaleKernelPfiiPKfiii(
	.param .u64 _Z25EdgeDirectedUpscaleKernelPfiiPKfiii_param_0,
	.param .u32 _Z25EdgeDirectedUpscaleKernelPfiiPKfiii_param_1,
	.param .u32 _Z25EdgeDirectedUpscaleKernelPfiiPKfiii_param_2,
	.param .u64 _Z25EdgeDirectedUpscaleKernelPfiiPKfiii_param_3,
	.param .u32 _Z25EdgeDirectedUpscaleKernelPfiiPKfiii_param_4,
	.param .u32 _Z25EdgeDirectedUpscaleKernelPfiiPKfiii_param_5,
	.param .u32 _Z25EdgeDirectedUpscaleKernelPfiiPKfiii_param_6
)
{
	.reg .pred 	%p<32>;
	.reg .f32 	%f<160>;
	.reg .b32 	%r<107>;
	.reg .b64 	%rd<35>;


	ld.param.u64 	%rd3, [_Z25EdgeDirectedUpscaleKernelPfiiPKfiii_param_0];
	ld.param.u32 	%r52, [_Z25EdgeDirectedUpscaleKernelPfiiPKfiii_param_1];
	ld.param.u32 	%r53, [_Z25EdgeDirectedUpscaleKernelPfiiPKfiii_param_2];
	ld.param.u64 	%rd4, [_Z25EdgeDirectedUpscaleKernelPfiiPKfiii_param_3];
	ld.param.u32 	%r54, [_Z25EdgeDirectedUpscaleKernelPfiiPKfiii_param_4];
	ld.param.u32 	%r55, [_Z25EdgeDirectedUpscaleKernelPfiiPKfiii_param_5];
	ld.param.u32 	%r56, [_Z25EdgeDirectedUpscaleKernelPfiiPKfiii_param_6];
	cvta.to.global.u64 	%rd1, %rd3;
	cvta.to.global.u64 	%rd2, %rd4;
	mov.u32 	%r57, %ntid.x;
	mov.u32 	%r58, %ctaid.x;
	mov.u32 	%r59, %tid.x;
	mad.lo.s32 	%r1, %r58, %r57, %r59;
	mov.u32 	%r60, %ntid.y;
	mov.u32 	%r61, %ctaid.y;
	mul.lo.s32 	%r2, %r61, %r60;
	mov.u32 	%r3, %tid.y;
	add.s32 	%r4, %r2, %r3;
	setp.ge.s32 	%p1, %r1, %r52;
	setp.ge.s32 	%p2, %r4, %r53;
	or.pred  	%p3, %p1, %p2;
	@%p3 bra 	$L__BB16_13;

	cvt.rn.f32.s32 	%f48, %r54;
	cvt.rn.f32.s32 	%f49, %r52;
	div.rn.f32 	%f50, %f48, %f49;
	cvt.rn.f32.s32 	%f51, %r53;
	cvt.rn.f32.s32 	%f52, %r55;
	div.rn.f32 	%f53, %f52, %f51;
	cvt.rn.f32.s32 	%f54, %r1;
	add.f32 	%f55, %f54, 0f3F000000;
	fma.rn.f32 	%f56, %f55, %f50, 0fBF000000;
	cvt.rn.f32.s32 	%f57, %r4;
	add.f32 	%f58, %f57, 0f3F000000;
	fma.rn.f32 	%f59, %f58, %f53, 0fBF000000;
	cvt.rmi.f32.f32 	%f60, %f56;
	cvt.rzi.s32.f32 	%r5, %f60;
	cvt.rmi.f32.f32 	%f61, %f59;
	cvt.rzi.s32.f32 	%r6, %f61;
	add.s32 	%r7, %r55, -1;
	max.s32 	%r8, %r5, 0;
	max.s32 	%r9, %r6, 0;
	cvt.rn.f32.s32 	%f62, %r8;
	sub.f32 	%f157, %f56, %f62;
	cvt.rn.f32.s32 	%f63, %r9;
	sub.f32 	%f156, %f59, %f63;
	setp.lt.s32 	%p4, %r56, 1;
	@%p4 bra 	$L__BB16_13;

	add.s32 	%r63, %r6, 1;
	mul.lo.s32 	%r10, %r55, %r54;
	mul.lo.s32 	%r11, %r9, %r54;
	add.s32 	%r12, %r11, %r8;
	min.s32 	%r64, %r63, %r7;
	mul.lo.s32 	%r13, %r64, %r54;
	add.s32 	%r14, %r13, %r8;
	and.b32  	%r15, %r56, 1;
	setp.eq.s32 	%p5, %r56, 1;
	mov.u32 	%r106, 0;
	@%p5 bra 	$L__BB16_9;

	add.s32 	%r66, %r55, %r9;
	mul.lo.s32 	%r67, %r54, %r66;
	add.s32 	%r104, %r8, %r67;
	shl.b32 	%r17, %r10, 1;
	add.s32 	%r68, %r67, -1;
	mov.u32 	%r69, -2;
	sub.s32 	%r70, %r69, %r5;
	neg.s32 	%r71, %r54;
	mov.u32 	%r106, 0;
	max.s32 	%r72, %r71, %r70;
	sub.s32 	%r103, %r68, %r72;
	sub.s32 	%r73, %r69, %r6;
	neg.s32 	%r74, %r55;
	max.s32 	%r75, %r74, %r73;
	sub.s32 	%r76, %r7, %r75;
	mul.lo.s32 	%r77, %r54, %r76;
	add.s32 	%r102, %r8, %r77;
	add.s32 	%r78, %r77, -1;
	sub.s32 	%r101, %r78, %r72;
	add.s32 	%r79, %r13, -1;
	sub.s32 	%r100, %r79, %r72;
	add.s32 	%r80, %r11, -1;
	sub.s32 	%r98, %r80, %r72;
	add.s32 	%r81, %r3, %r53;
	add.s32 	%r82, %r81, %r2;
	mad.lo.s32 	%r96, %r52, %r82, %r1;
	mul.lo.s32 	%r83, %r53, %r52;
	shl.b32 	%r24, %r83, 1;
	mad.lo.s32 	%r95, %r52, %r4, %r1;
	sub.s32 	%r26, %r15, %r56;
	mov.u32 	%r97, %r12;
	mov.u32 	%r99, %r14;

$L__BB16_4:
	mul.wide.s32 	%rd5, %r97, 4;
	add.s64 	%rd6, %rd2, %rd5;
	mul.wide.s32 	%rd7, %r98, 4;
	add.s64 	%rd8, %rd2, %rd7;
	mul.wide.s32 	%rd9, %r99, 4;
	add.s64 	%rd10, %rd2, %rd9;
	mul.wide.s32 	%rd11, %r100, 4;
	add.s64 	%rd12, %rd2, %rd11;
	ld.global.f32 	%f3, [%rd8];
	ld.global.f32 	%f4, [%rd6];
	sub.f32 	%f64, %f3, %f4;
	abs.f32 	%f65, %f64;
	ld.global.f32 	%f5, [%rd12];
	ld.global.f32 	%f6, [%rd10];
	sub.f32 	%f66, %f5, %f6;
	abs.f32 	%f67, %f66;
	add.f32 	%f68, %f65, %f67;
	sub.f32 	%f69, %f6, %f4;
	abs.f32 	%f70, %f69;
	sub.f32 	%f71, %f5, %f3;
	abs.f32 	%f72, %f71;
	add.f32 	%f7, %f70, %f72;
	sub.f32 	%f73, %f5, %f4;
	abs.f32 	%f8, %f73;
	sub.f32 	%f74, %f6, %f3;
	abs.f32 	%f9, %f74;
	setp.lt.f32 	%p6, %f68, %f7;
	setp.lt.f32 	%p7, %f68, %f8;
	and.pred  	%p8, %p6, %p7;
	setp.lt.f32 	%p9, %f68, %f9;
	and.pred  	%p10, %p8, %p9;
	mov.f32 	%f148, %f156;
	mov.f32 	%f149, %f157;
	mov.f32 	%f150, %f6;
	mov.f32 	%f151, %f3;
	@%p10 bra 	$L__BB16_6;

	setp.lt.f32 	%p11, %f7, %f8;
	setp.lt.f32 	%p12, %f7, %f9;
	and.pred  	%p13, %p11, %p12;
	selp.f32 	%f148, %f157, %f156, %p13;
	selp.f32 	%f149, %f156, %f157, %p13;
	selp.f32 	%f150, %f3, %f6, %p13;
	selp.f32 	%f151, %f6, %f3, %p13;

$L__BB16_6:
	mov.f32 	%f75, 0f3F800000;
	sub.f32 	%f76, %f75, %f149;
	mul.f32 	%f77, %f149, %f151;
	fma.rn.f32 	%f78, %f4, %f76, %f77;
	mul.f32 	%f79, %f150, %f76;
	fma.rn.f32 	%f80, %f5, %f149, %f79;
	sub.f32 	%f81, %f75, %f148;
	mul.f32 	%f82, %f81, %f78;
	fma.rn.f32 	%f83, %f148, %f80, %f82;
	add.f32 	%f84, %f4, %f3;
	add.f32 	%f85, %f84, %f6;
	add.f32 	%f86, %f85, %f5;
	fma.rn.f32 	%f87, %f86, 0fBE800000, %f83;
	fma.rn.f32 	%f88, %f87, 0f3DCCCCCD, %f83;
	min.f32 	%f89, %f75, %f88;
	mov.f32 	%f90, 0f00000000;
	max.f32 	%f91, %f90, %f89;
	mul.wide.s32 	%rd13, %r95, 4;
	add.s64 	%rd14, %rd1, %rd13;
	st.global.f32 	[%rd14], %f91;
	mul.wide.s32 	%rd15, %r104, 4;
	add.s64 	%rd16, %rd2, %rd15;
	mul.wide.s32 	%rd17, %r103, 4;
	add.s64 	%rd18, %rd2, %rd17;
	mul.wide.s32 	%rd19, %r102, 4;
	add.s64 	%rd20, %rd2, %rd19;
	mul.wide.s32 	%rd21, %r101, 4;
	add.s64 	%rd22, %rd2, %rd21;
	ld.global.f32 	%f18, [%rd18];
	ld.global.f32 	%f19, [%rd16];
	sub.f32 	%f92, %f18, %f19;
	abs.f32 	%f93, %f92;
	ld.global.f32 	%f20, [%rd22];
	ld.global.f32 	%f21, [%rd20];
	sub.f32 	%f94, %f20, %f21;
	abs.f32 	%f95, %f94;
	add.f32 	%f96, %f93, %f95;
	sub.f32 	%f97, %f21, %f19;
	abs.f32 	%f98, %f97;
	sub.f32 	%f99, %f20, %f18;
	abs.f32 	%f100, %f99;
	add.f32 	%f22, %f98, %f100;
	sub.f32 	%f101, %f20, %f19;
	abs.f32 	%f23, %f101;
	sub.f32 	%f102, %f21, %f18;
	abs.f32 	%f24, %f102;
	setp.lt.f32 	%p14, %f96, %f22;
	setp.lt.f32 	%p15, %f96, %f23;
	and.pred  	%p16, %p14, %p15;
	setp.lt.f32 	%p17, %f96, %f24;
	and.pred  	%p18, %p16, %p17;
	mov.f32 	%f152, %f156;
	mov.f32 	%f153, %f157;
	mov.f32 	%f154, %f21;
	mov.f32 	%f155, %f18;
	@%p18 bra 	$L__BB16_8;

	setp.lt.f32 	%p19, %f22, %f23;
	setp.lt.f32 	%p20, %f22, %f24;
	and.pred  	%p21, %p19, %p20;
	selp.f32 	%f152, %f157, %f156, %p21;
	selp.f32 	%f153, %f156, %f157, %p21;
	selp.f32 	%f154, %f18, %f21, %p21;
	selp.f32 	%f155, %f21, %f18, %p21;

$L__BB16_8:
	sub.f32 	%f104, %f75, %f153;
	mul.f32 	%f105, %f153, %f155;
	fma.rn.f32 	%f106, %f19, %f104, %f105;
	mul.f32 	%f107, %f154, %f104;
	fma.rn.f32 	%f108, %f20, %f153, %f107;
	sub.f32 	%f109, %f75, %f152;
	mul.f32 	%f110, %f109, %f106;
	fma.rn.f32 	%f111, %f152, %f108, %f110;
	add.f32 	%f112, %f19, %f18;
	add.f32 	%f113, %f112, %f21;
	add.f32 	%f114, %f113, %f20;
	fma.rn.f32 	%f115, %f114, 0fBE800000, %f111;
	fma.rn.f32 	%f116, %f115, 0f3DCCCCCD, %f111;
	min.f32 	%f117, %f75, %f116;
	max.f32 	%f119, %f90, %f117;
	mul.wide.s32 	%rd23, %r96, 4;
	add.s64 	%rd24, %rd1, %rd23;
	st.global.f32 	[%rd24], %f119;
	add.s32 	%r104, %r104, %r17;
	add.s32 	%r103, %r103, %r17;
	add.s32 	%r102, %r102, %r17;
	add.s32 	%r101, %r101, %r17;
	add.s32 	%r100, %r100, %r17;
	add.s32 	%r99, %r99, %r17;
	add.s32 	%r98, %r98, %r17;
	add.s32 	%r97, %r97, %r17;
	add.s32 	%r96, %r96, %r24;
	add.s32 	%r95, %r95, %r24;
	add.s32 	%r106, %r106, 2;
	add.s32 	%r84, %r26, %r106;
	setp.ne.s32 	%p22, %r84, 0;
	@%p22 bra 	$L__BB16_4;

$L__BB16_9:
	add.s32 	%r85, %r5, 1;
	add.s32 	%r86, %r54, -1;
	min.s32 	%r87, %r85, %r86;
	setp.eq.s32 	%p23, %r15, 0;
	add.s32 	%r50, %r13, %r87;
	add.s32 	%r51, %r11, %r87;
	@%p23 bra 	$L__BB16_13;

	mul.lo.s32 	%r88, %r10, %r106;
	add.s32 	%r89, %r12, %r88;
	mul.wide.s32 	%rd25, %r89, 4;
	add.s64 	%rd26, %rd2, %rd25;
	add.s32 	%r90, %r51, %r88;
	mul.wide.s32 	%rd27, %r90, 4;
	add.s64 	%rd28, %rd2, %rd27;
	add.s32 	%r91, %r14, %r88;
	mul.wide.s32 	%rd29, %r91, 4;
	add.s64 	%rd30, %rd2, %rd29;
	add.s32 	%r92, %r50, %r88;
	mul.wide.s32 	%rd31, %r92, 4;
	add.s64 	%rd32, %rd2, %rd31;
	ld.global.f32 	%f33, [%rd28];
	ld.global.f32 	%f34, [%rd26];
	sub.f32 	%f120, %f33, %f34;
	abs.f32 	%f121, %f120;
	ld.global.f32 	%f35, [%rd32];
	ld.global.f32 	%f36, [%rd30];
	sub.f32 	%f122, %f35, %f36;
	abs.f32 	%f123, %f122;
	add.f32 	%f124, %f121, %f123;
	sub.f32 	%f125, %f36, %f34;
	abs.f32 	%f126, %f125;
	sub.f32 	%f127, %f35, %f33;
	abs.f32 	%f128, %f127;
	add.f32 	%f37, %f126, %f128;
	sub.f32 	%f129, %f35, %f34;
	abs.f32 	%f38, %f129;
	sub.f32 	%f130, %f36, %f33;
	abs.f32 	%f39, %f130;
	setp.lt.f32 	%p24, %f124, %f37;
	setp.lt.f32 	%p25, %f124, %f38;
	and.pred  	%p26, %p24, %p25;
	setp.lt.f32 	%p27, %f124, %f39;
	and.pred  	%p28, %p26, %p27;
	mov.f32 	%f158, %f36;
	mov.f32 	%f159, %f33;
	@%p28 bra 	$L__BB16_12;

	setp.lt.f32 	%p29, %f37, %f38;
	setp.lt.f32 	%p30, %f37, %f39;
	and.pred  	%p31, %p29, %p30;
	selp.f32 	%f40, %f157, %f156, %p31;
	selp.f32 	%f157, %f156, %f157, %p31;
	selp.f32 	%f158, %f33, %f36, %p31;
	selp.f32 	%f159, %f36, %f33, %p31;
	mov.f32 	%f156, %f40;

$L__BB16_12:
	mov.f32 	%f131, 0f3F800000;
	sub.f32 	%f132, %f131, %f157;
	mul.f32 	%f133, %f157, %f159;
	fma.rn.f32 	%f134, %f34, %f132, %f133;
	mul.f32 	%f135, %f158, %f132;
	fma.rn.f32 	%f136, %f35, %f157, %f135;
	sub.f32 	%f137, %f131, %f156;
	mul.f32 	%f138, %f137, %f134;
	fma.rn.f32 	%f139, %f156, %f136, %f138;
	add.f32 	%f140, %f34, %f33;
	add.f32 	%f141, %f140, %f36;
	add.f32 	%f142, %f141, %f35;
	fma.rn.f32 	%f143, %f142, 0fBE800000, %f139;
	fma.rn.f32 	%f144, %f143, 0f3DCCCCCD, %f139;
	min.f32 	%f145, %f131, %f144;
	mov.f32 	%f146, 0f00000000;
	max.f32 	%f147, %f146, %f145;
	mad.lo.s32 	%r93, %r106, %r53, %r4;
	mad.lo.s32 	%r94, %r93, %r52, %r1;
	mul.wide.s32 	%rd33, %r94, 4;
	add.s64 	%rd34, %rd1, %rd33;
	st.global.f32 	[%rd34], %f147;

$L__BB16_13:
	ret;

}
	// .globl	_Z20LanczosUpscaleKernelPfiiPKfiii
.visible .entry _Z20LanczosUpscaleKernelPfiiPKfiii(
	.param .u64 _Z20LanczosUpscaleKernelPfiiPKfiii_param_0,
	.param .u32 _Z20LanczosUpscaleKernelPfiiPKfiii_param_1,
	.param .u32 _Z20LanczosUpscaleKernelPfiiPKfiii_param_2,
	.param .u64 _Z20LanczosUpscaleKernelPfiiPKfiii_param_3,
	.param .u32 _Z20LanczosUpscaleKernelPfiiPKfiii_param_4,
	.param .u32 _Z20LanczosUpscaleKernelPfiiPKfiii_param_5,
	.param .u32 _Z20LanczosUpscaleKernelPfiiPKfiii_param_6
)
{
	.local .align 4 .b8 	__local_depot17[28];
	.reg .b64 	%SP;
	.reg .b64 	%SPL;
	.reg .pred 	%p<56>;
	.reg .f32 	%f<224>;
	.reg .b32 	%r<253>;
	.reg .f64 	%fd<9>;
	.reg .b64 	%rd<91>;


	mov.u64 	%SPL, __local_depot17;
	ld.param.u64 	%rd27, [_Z20LanczosUpscaleKernelPfiiPKfiii_param_0];
	ld.param.u32 	%r79, [_Z20LanczosUpscaleKernelPfiiPKfiii_param_1];
	ld.param.u32 	%r80, [_Z20LanczosUpscaleKernelPfiiPKfiii_param_2];
	ld.param.u64 	%rd28, [_Z20LanczosUpscaleKernelPfiiPKfiii_param_3];
	ld.param.u32 	%r81, [_Z20LanczosUpscaleKernelPfiiPKfiii_param_4];
	ld.param.u32 	%r82, [_Z20LanczosUpscaleKernelPfiiPKfiii_param_5];
	ld.param.u32 	%r83, [_Z20LanczosUpscaleKernelPfiiPKfiii_param_6];
	add.u64 	%rd1, %SPL, 0;
	mov.u32 	%r84, %ntid.x;
	mov.u32 	%r85, %ctaid.x;
	mov.u32 	%r86, %tid.x;
	mad.lo.s32 	%r1, %r85, %r84, %r86;
	mov.u32 	%r87, %ntid.y;
	mov.u32 	%r88, %ctaid.y;
	mov.u32 	%r89, %tid.y;
	mad.lo.s32 	%r2, %r88, %r87, %r89;
	setp.ge.s32 	%p1, %r1, %r79;
	setp.ge.s32 	%p2, %r2, %r80;
	or.pred  	%p3, %p1, %p2;
	@%p3 bra 	$L__BB17_68;

	cvt.rn.f32.s32 	%f79, %r81;
	cvt.rn.f32.s32 	%f80, %r79;
	div.rn.f32 	%f81, %f79, %f80;
	cvt.rn.f32.s32 	%f82, %r80;
	cvt.rn.f32.s32 	%f83, %r82;
	div.rn.f32 	%f84, %f83, %f82;
	cvt.rn.f32.s32 	%f85, %r1;
	add.f32 	%f86, %f85, 0f3F000000;
	fma.rn.f32 	%f1, %f86, %f81, 0fBF000000;
	cvt.rn.f32.s32 	%f87, %r2;
	add.f32 	%f88, %f87, 0f3F000000;
	fma.rn.f32 	%f2, %f88, %f84, 0fBF000000;
	cvt.rmi.f32.f32 	%f89, %f1;
	cvt.rzi.s32.f32 	%r90, %f89;
	add.s32 	%r91, %r90, -2;
	max.s32 	%r3, %r91, 0;
	cvt.rpi.f32.f32 	%f90, %f1;
	cvt.rzi.s32.f32 	%r92, %f90;
	add.s32 	%r93, %r92, 2;
	add.s32 	%r94, %r81, -1;
	min.s32 	%r4, %r94, %r93;
	cvt.rmi.f32.f32 	%f91, %f2;
	cvt.rzi.s32.f32 	%r95, %f91;
	add.s32 	%r96, %r95, -2;
	max.s32 	%r5, %r96, 0;
	cvt.rpi.f32.f32 	%f92, %f2;
	cvt.rzi.s32.f32 	%r97, %f92;
	add.s32 	%r98, %r97, 2;
	add.s32 	%r99, %r82, -1;
	min.s32 	%r6, %r99, %r98;
	setp.lt.s32 	%p4, %r83, 1;
	@%p4 bra 	$L__BB17_68;

	cvta.to.global.u64 	%rd2, %rd27;
	cvta.to.global.u64 	%rd3, %rd28;
	mov.u32 	%r237, 0;

$L__BB17_3:
	setp.gt.s32 	%p5, %r5, %r6;
	mov.f32 	%f223, 0f00000000;
	mov.f32 	%f217, %f223;
	mov.f32 	%f218, %f223;
	@%p5 bra 	$L__BB17_65;

	ld.param.u32 	%r233, [_Z20LanczosUpscaleKernelPfiiPKfiii_param_5];
	mov.f32 	%f218, 0f00000000;
	mov.u32 	%r238, %r5;
	mov.f32 	%f217, %f218;

$L__BB17_5:
	setp.gt.s32 	%p6, %r3, %r4;
	@%p6 bra 	$L__BB17_64;

	ld.param.u32 	%r236, [_Z20LanczosUpscaleKernelPfiiPKfiii_param_5];
	mul.lo.s32 	%r235, %r237, %r233;
	ld.param.u32 	%r234, [_Z20LanczosUpscaleKernelPfiiPKfiii_param_4];
	cvt.rn.f32.s32 	%f97, %r238;
	sub.f32 	%f5, %f2, %f97;
	add.s32 	%r101, %r238, %r235;
	mul.lo.s32 	%r10, %r101, %r234;
	mul.f32 	%f6, %f5, 0f40490FDB;
	div.rn.f32 	%f7, %f6, 0f40400000;
	mul.f32 	%f8, %f6, 0f3F22F983;
	mov.b32 	%r102, %f6;
	and.b32  	%r11, %r102, -2147483648;
	bfe.u32 	%r103, %r102, 23, 8;
	add.s32 	%r104, %r103, -128;
	shl.b32 	%r105, %r102, 8;
	or.b32  	%r12, %r105, -2147483648;
	shr.u32 	%r106, %r104, 5;
	mov.u32 	%r107, 4;
	sub.s32 	%r108, %r107, %r106;
	and.b32  	%r13, %r104, 31;
	mov.u32 	%r109, 6;
	sub.s32 	%r110, %r109, %r106;
	mul.wide.s32 	%rd30, %r110, 4;
	add.s64 	%rd4, %rd1, %rd30;
	mov.b32 	%r111, %f7;
	and.b32  	%r14, %r111, -2147483648;
	bfe.u32 	%r112, %r111, 23, 8;
	add.s32 	%r113, %r112, -128;
	shr.u32 	%r115, %r113, 5;
	sub.s32 	%r116, %r107, %r115;
	xor.b32  	%r16, %r11, -2147483648;
	mov.u32 	%r117, 32;
	sub.s32 	%r17, %r117, %r13;
	mul.wide.s32 	%rd31, %r108, 4;
	add.s64 	%rd5, %rd1, %rd31;
	mul.f32 	%f10, %f6, %f6;
	and.b32  	%r18, %r113, 31;
	sub.s32 	%r118, %r109, %r115;
	mul.wide.s32 	%rd32, %r118, 4;
	add.s64 	%rd6, %rd1, %rd32;
	xor.b32  	%r19, %r14, -2147483648;
	sub.s32 	%r20, %r117, %r18;
	mul.wide.s32 	%rd33, %r116, 4;
	add.s64 	%rd7, %rd1, %rd33;
	mov.u32 	%r239, %r3;

$L__BB17_7:
	cvt.rn.f32.s32 	%f99, %r239;
	sub.f32 	%f13, %f1, %f99;
	setp.eq.f32 	%p7, %f13, 0f00000000;
	add.s64 	%rd8, %rd1, 24;
	mov.f32 	%f216, 0f3F800000;
	mov.f32 	%f209, %f216;
	@%p7 bra 	$L__BB17_34;

	abs.f32 	%f101, %f13;
	setp.ge.f32 	%p8, %f101, 0f40400000;
	mov.f32 	%f209, 0f00000000;
	@%p8 bra 	$L__BB17_34;

	mul.f32 	%f14, %f13, 0f40490FDB;
	div.rn.f32 	%f15, %f14, 0f40400000;
	mul.f32 	%f102, %f14, 0f3F22F983;
	cvt.rni.s32.f32 	%r243, %f102;
	cvt.rn.f32.s32 	%f103, %r243;
	mov.f32 	%f104, 0fBFC90FDA;
	fma.rn.f32 	%f105, %f103, %f104, %f14;
	mov.f32 	%f106, 0fB3A22168;
	fma.rn.f32 	%f107, %f103, %f106, %f105;
	mov.f32 	%f108, 0fA7C234C5;
	fma.rn.f32 	%f203, %f103, %f108, %f107;
	abs.f32 	%f17, %f14;
	setp.ltu.f32 	%p9, %f17, 0f47CE4780;
	@%p9 bra 	$L__BB17_17;

	setp.eq.f32 	%p10, %f17, 0f7F800000;
	@%p10 bra 	$L__BB17_16;
	bra.uni 	$L__BB17_11;

$L__BB17_16:
	mov.f32 	%f111, 0f00000000;
	mul.rn.f32 	%f203, %f14, %f111;
	mov.u32 	%r243, 0;
	bra.uni 	$L__BB17_17;

$L__BB17_11:
	mov.b32 	%r23, %f14;
	bfe.u32 	%r120, %r23, 23, 8;
	add.s32 	%r24, %r120, -128;
	shl.b32 	%r121, %r23, 8;
	or.b32  	%r25, %r121, -2147483648;
	shr.u32 	%r26, %r24, 5;
	mov.u64 	%rd84, 0;
	mov.u32 	%r240, 0;
	mov.u64 	%rd83, __cudart_i2opi_f;
	mov.u64 	%rd82, %rd1;

$L__BB17_12:
	.pragma "nounroll";
	ld.global.nc.u32 	%r122, [%rd83];
	mad.wide.u32 	%rd36, %r122, %r25, %rd84;
	shr.u64 	%rd84, %rd36, 32;
	st.local.u32 	[%rd82], %rd36;
	add.s64 	%rd83, %rd83, 4;
	add.s64 	%rd82, %rd82, 4;
	add.s32 	%r240, %r240, 1;
	setp.ne.s32 	%p11, %r240, 6;
	@%p11 bra 	$L__BB17_12;

	st.local.u32 	[%rd8], %rd84;
	mov.u32 	%r123, 4;
	sub.s32 	%r29, %r123, %r26;
	mov.u32 	%r124, 6;
	sub.s32 	%r125, %r124, %r26;
	mul.wide.s32 	%rd37, %r125, 4;
	add.s64 	%rd38, %rd1, %rd37;
	ld.local.u32 	%r241, [%rd38];
	ld.local.u32 	%r242, [%rd38+-4];
	and.b32  	%r32, %r24, 31;
	setp.eq.s32 	%p12, %r32, 0;
	@%p12 bra 	$L__BB17_15;

	mov.u32 	%r126, 32;
	sub.s32 	%r127, %r126, %r32;
	shr.u32 	%r128, %r242, %r127;
	shl.b32 	%r129, %r241, %r32;
	add.s32 	%r241, %r128, %r129;
	mul.wide.s32 	%rd39, %r29, 4;
	add.s64 	%rd40, %rd1, %rd39;
	ld.local.u32 	%r130, [%rd40];
	shr.u32 	%r131, %r130, %r127;
	shl.b32 	%r132, %r242, %r32;
	add.s32 	%r242, %r131, %r132;

$L__BB17_15:
	and.b32  	%r133, %r23, -2147483648;
	shr.u32 	%r134, %r242, 30;
	shl.b32 	%r135, %r241, 2;
	or.b32  	%r136, %r134, %r135;
	shr.u32 	%r137, %r136, 31;
	shr.u32 	%r138, %r241, 30;
	add.s32 	%r139, %r137, %r138;
	neg.s32 	%r140, %r139;
	setp.eq.s32 	%p13, %r133, 0;
	selp.b32 	%r243, %r139, %r140, %p13;
	setp.ne.s32 	%p14, %r137, 0;
	xor.b32  	%r141, %r133, -2147483648;
	selp.b32 	%r142, %r141, %r133, %p14;
	selp.b32 	%r143, -1, 0, %p14;
	xor.b32  	%r144, %r136, %r143;
	shl.b32 	%r145, %r242, 2;
	xor.b32  	%r146, %r145, %r143;
	cvt.u64.u32 	%rd41, %r144;
	cvt.u64.u32 	%rd42, %r146;
	bfi.b64 	%rd43, %rd41, %rd42, 32, 32;
	cvt.rn.f64.s64 	%fd1, %rd43;
	mul.f64 	%fd2, %fd1, 0d3BF921FB54442D19;
	cvt.rn.f32.f64 	%f109, %fd2;
	setp.eq.s32 	%p15, %r142, 0;
	neg.f32 	%f110, %f109;
	selp.f32 	%f203, %f109, %f110, %p15;

$L__BB17_17:
	and.b32  	%r39, %r243, 1;
	setp.eq.s32 	%p16, %r39, 0;
	selp.f32 	%f21, %f203, 0f3F800000, %p16;
	mul.rn.f32 	%f22, %f203, %f203;
	mov.f32 	%f204, 0fB94D4153;
	@%p16 bra 	$L__BB17_19;

	mov.f32 	%f113, 0fBAB607ED;
	mov.f32 	%f114, 0f37CBAC00;
	fma.rn.f32 	%f204, %f114, %f22, %f113;

$L__BB17_19:
	selp.f32 	%f115, 0f3C0885E4, 0f3D2AAABB, %p16;
	fma.rn.f32 	%f116, %f204, %f22, %f115;
	selp.f32 	%f117, 0fBE2AAAA8, 0fBEFFFFFF, %p16;
	fma.rn.f32 	%f118, %f116, %f22, %f117;
	mov.f32 	%f119, 0f00000000;
	fma.rn.f32 	%f120, %f22, %f21, %f119;
	fma.rn.f32 	%f205, %f118, %f120, %f21;
	and.b32  	%r148, %r243, 2;
	setp.eq.s32 	%p18, %r148, 0;
	@%p18 bra 	$L__BB17_21;

	mov.f32 	%f122, 0fBF800000;
	fma.rn.f32 	%f205, %f205, %f122, %f119;

$L__BB17_21:
	mul.f32 	%f123, %f15, 0f3F22F983;
	cvt.rni.s32.f32 	%r246, %f123;
	cvt.rn.f32.s32 	%f124, %r246;
	mov.f32 	%f125, 0fBFC90FDA;
	fma.rn.f32 	%f126, %f124, %f125, %f15;
	mov.f32 	%f127, 0fB3A22168;
	fma.rn.f32 	%f128, %f124, %f127, %f126;
	mov.f32 	%f129, 0fA7C234C5;
	fma.rn.f32 	%f206, %f124, %f129, %f128;
	abs.f32 	%f29, %f15;
	setp.ltu.f32 	%p19, %f29, 0f47CE4780;
	@%p19 bra 	$L__BB17_29;

	setp.eq.f32 	%p20, %f29, 0f7F800000;
	@%p20 bra 	$L__BB17_28;
	bra.uni 	$L__BB17_23;

$L__BB17_28:
	mov.f32 	%f132, 0f00000000;
	mul.rn.f32 	%f206, %f15, %f132;
	mov.u32 	%r246, 0;
	bra.uni 	$L__BB17_29;

$L__BB17_23:
	mov.b32 	%r41, %f15;
	bfe.u32 	%r149, %r41, 23, 8;
	add.s32 	%r42, %r149, -128;
	shl.b32 	%r150, %r41, 8;
	or.b32  	%r43, %r150, -2147483648;
	shr.u32 	%r44, %r42, 5;
	mov.u64 	%rd85, 0;
	mov.u64 	%rd86, %rd85;

$L__BB17_24:
	.pragma "nounroll";
	shl.b64 	%rd46, %rd85, 2;
	mov.u64 	%rd47, __cudart_i2opi_f;
	add.s64 	%rd48, %rd47, %rd46;
	ld.global.nc.u32 	%r151, [%rd48];
	mad.wide.u32 	%rd49, %r151, %r43, %rd86;
	shr.u64 	%rd86, %rd49, 32;
	add.s64 	%rd50, %rd1, %rd46;
	st.local.u32 	[%rd50], %rd49;
	cvt.u32.u64 	%r152, %rd85;
	add.s32 	%r153, %r152, 1;
	cvt.s64.s32 	%rd85, %r153;
	setp.ne.s32 	%p21, %r153, 6;
	@%p21 bra 	$L__BB17_24;

	st.local.u32 	[%rd8], %rd86;
	mov.u32 	%r154, 4;
	sub.s32 	%r45, %r154, %r44;
	mov.u32 	%r155, 6;
	sub.s32 	%r156, %r155, %r44;
	mul.wide.s32 	%rd51, %r156, 4;
	add.s64 	%rd52, %rd1, %rd51;
	ld.local.u32 	%r244, [%rd52];
	ld.local.u32 	%r245, [%rd52+-4];
	and.b32  	%r48, %r42, 31;
	setp.eq.s32 	%p22, %r48, 0;
	@%p22 bra 	$L__BB17_27;

	mov.u32 	%r157, 32;
	sub.s32 	%r158, %r157, %r48;
	shr.u32 	%r159, %r245, %r158;
	shl.b32 	%r160, %r244, %r48;
	add.s32 	%r244, %r159, %r160;
	mul.wide.s32 	%rd53, %r45, 4;
	add.s64 	%rd54, %rd1, %rd53;
	ld.local.u32 	%r161, [%rd54];
	shr.u32 	%r162, %r161, %r158;
	shl.b32 	%r163, %r245, %r48;
	add.s32 	%r245, %r162, %r163;

$L__BB17_27:
	and.b32  	%r164, %r41, -2147483648;
	shr.u32 	%r165, %r245, 30;
	shl.b32 	%r166, %r244, 2;
	or.b32  	%r167, %r165, %r166;
	shr.u32 	%r168, %r167, 31;
	shr.u32 	%r169, %r244, 30;
	add.s32 	%r170, %r168, %r169;
	neg.s32 	%r171, %r170;
	setp.eq.s32 	%p23, %r164, 0;
	selp.b32 	%r246, %r170, %r171, %p23;
	setp.ne.s32 	%p24, %r168, 0;
	xor.b32  	%r172, %r164, -2147483648;
	selp.b32 	%r173, %r172, %r164, %p24;
	selp.b32 	%r174, -1, 0, %p24;
	xor.b32  	%r175, %r167, %r174;
	shl.b32 	%r176, %r245, 2;
	xor.b32  	%r177, %r176, %r174;
	cvt.u64.u32 	%rd55, %r175;
	cvt.u64.u32 	%rd56, %r177;
	bfi.b64 	%rd57, %rd55, %rd56, 32, 32;
	cvt.rn.f64.s64 	%fd3, %rd57;
	mul.f64 	%fd4, %fd3, 0d3BF921FB54442D19;
	cvt.rn.f32.f64 	%f130, %fd4;
	setp.eq.s32 	%p25, %r173, 0;
	neg.f32 	%f131, %f130;
	selp.f32 	%f206, %f130, %f131, %p25;

$L__BB17_29:
	and.b32  	%r55, %r246, 1;
	setp.eq.s32 	%p26, %r55, 0;
	selp.f32 	%f33, %f206, 0f3F800000, %p26;
	mul.rn.f32 	%f34, %f206, %f206;
	mov.f32 	%f207, 0fB94D4153;
	@%p26 bra 	$L__BB17_31;

	mov.f32 	%f134, 0fBAB607ED;
	mov.f32 	%f135, 0f37CBAC00;
	fma.rn.f32 	%f207, %f135, %f34, %f134;

$L__BB17_31:
	selp.f32 	%f136, 0f3C0885E4, 0f3D2AAABB, %p26;
	fma.rn.f32 	%f137, %f207, %f34, %f136;
	selp.f32 	%f138, 0fBE2AAAA8, 0fBEFFFFFF, %p26;
	fma.rn.f32 	%f139, %f137, %f34, %f138;
	mov.f32 	%f140, 0f00000000;
	fma.rn.f32 	%f141, %f34, %f33, %f140;
	fma.rn.f32 	%f208, %f139, %f141, %f33;
	and.b32  	%r179, %r246, 2;
	setp.eq.s32 	%p28, %r179, 0;
	@%p28 bra 	$L__BB17_33;

	mov.f32 	%f143, 0fBF800000;
	fma.rn.f32 	%f208, %f208, %f143, %f140;

$L__BB17_33:
	mul.f32 	%f144, %f14, %f14;
	mul.f32 	%f145, %f205, 0f40400000;
	mul.f32 	%f146, %f145, %f208;
	div.rn.f32 	%f209, %f146, %f144;

$L__BB17_34:
	setp.eq.f32 	%p29, %f5, 0f00000000;
	@%p29 bra 	$L__BB17_61;

	abs.f32 	%f149, %f5;
	setp.ge.f32 	%p30, %f149, 0f40400000;
	mov.f32 	%f216, 0f00000000;
	@%p30 bra 	$L__BB17_61;

	cvt.rni.s32.f32 	%r249, %f8;
	cvt.rn.f32.s32 	%f150, %r249;
	mov.f32 	%f151, 0fBFC90FDA;
	fma.rn.f32 	%f152, %f150, %f151, %f6;
	mov.f32 	%f153, 0fB3A22168;
	fma.rn.f32 	%f154, %f150, %f153, %f152;
	mov.f32 	%f155, 0fA7C234C5;
	fma.rn.f32 	%f210, %f150, %f155, %f154;
	abs.f32 	%f43, %f6;
	setp.ltu.f32 	%p31, %f43, 0f47CE4780;
	@%p31 bra 	$L__BB17_44;

	setp.eq.f32 	%p32, %f43, 0f7F800000;
	@%p32 bra 	$L__BB17_43;
	bra.uni 	$L__BB17_38;

$L__BB17_43:
	mov.f32 	%f158, 0f00000000;
	mul.rn.f32 	%f210, %f6, %f158;
	mov.u32 	%r249, 0;
	bra.uni 	$L__BB17_44;

$L__BB17_38:
	mov.u64 	%rd87, 0;
	mov.u64 	%rd88, %rd87;

$L__BB17_39:
	.pragma "nounroll";
	shl.b64 	%rd60, %rd87, 2;
	mov.u64 	%rd61, __cudart_i2opi_f;
	add.s64 	%rd62, %rd61, %rd60;
	ld.global.nc.u32 	%r180, [%rd62];
	mad.wide.u32 	%rd63, %r180, %r12, %rd88;
	shr.u64 	%rd88, %rd63, 32;
	add.s64 	%rd64, %rd1, %rd60;
	st.local.u32 	[%rd64], %rd63;
	cvt.u32.u64 	%r181, %rd87;
	add.s32 	%r182, %r181, 1;
	cvt.s64.s32 	%rd87, %r182;
	setp.ne.s32 	%p33, %r182, 6;
	@%p33 bra 	$L__BB17_39;

	setp.eq.s32 	%p34, %r13, 0;
	st.local.u32 	[%rd8], %rd88;
	ld.local.u32 	%r247, [%rd4];
	ld.local.u32 	%r248, [%rd4+-4];
	@%p34 bra 	$L__BB17_42;

	shl.b32 	%r183, %r247, %r13;
	shr.u32 	%r184, %r248, %r17;
	add.s32 	%r247, %r184, %r183;
	ld.local.u32 	%r185, [%rd5];
	shr.u32 	%r186, %r185, %r17;
	shl.b32 	%r187, %r248, %r13;
	add.s32 	%r248, %r186, %r187;

$L__BB17_42:
	shr.u32 	%r188, %r248, 30;
	shl.b32 	%r189, %r247, 2;
	or.b32  	%r190, %r188, %r189;
	shr.u32 	%r191, %r190, 31;
	shr.u32 	%r192, %r247, 30;
	add.s32 	%r193, %r191, %r192;
	neg.s32 	%r194, %r193;
	setp.eq.s32 	%p35, %r11, 0;
	selp.b32 	%r249, %r193, %r194, %p35;
	setp.ne.s32 	%p36, %r191, 0;
	selp.b32 	%r195, %r16, %r11, %p36;
	selp.b32 	%r196, -1, 0, %p36;
	xor.b32  	%r197, %r190, %r196;
	shl.b32 	%r198, %r248, 2;
	xor.b32  	%r199, %r198, %r196;
	cvt.u64.u32 	%rd65, %r197;
	cvt.u64.u32 	%rd66, %r199;
	bfi.b64 	%rd67, %rd65, %rd66, 32, 32;
	cvt.rn.f64.s64 	%fd5, %rd67;
	mul.f64 	%fd6, %fd5, 0d3BF921FB54442D19;
	cvt.rn.f32.f64 	%f156, %fd6;
	setp.eq.s32 	%p37, %r195, 0;
	neg.f32 	%f157, %f156;
	selp.f32 	%f210, %f156, %f157, %p37;

$L__BB17_44:
	and.b32  	%r65, %r249, 1;
	setp.eq.s32 	%p38, %r65, 0;
	selp.f32 	%f47, %f210, 0f3F800000, %p38;
	mul.rn.f32 	%f48, %f210, %f210;
	mov.f32 	%f211, 0fB94D4153;
	@%p38 bra 	$L__BB17_46;

	mov.f32 	%f160, 0fBAB607ED;
	mov.f32 	%f161, 0f37CBAC00;
	fma.rn.f32 	%f211, %f161, %f48, %f160;

$L__BB17_46:
	selp.f32 	%f162, 0f3C0885E4, 0f3D2AAABB, %p38;
	fma.rn.f32 	%f163, %f211, %f48, %f162;
	selp.f32 	%f164, 0fBE2AAAA8, 0fBEFFFFFF, %p38;
	fma.rn.f32 	%f165, %f163, %f48, %f164;
	mov.f32 	%f166, 0f00000000;
	fma.rn.f32 	%f167, %f48, %f47, %f166;
	fma.rn.f32 	%f212, %f165, %f167, %f47;
	and.b32  	%r201, %r249, 2;
	setp.eq.s32 	%p40, %r201, 0;
	@%p40 bra 	$L__BB17_48;

	mov.f32 	%f169, 0fBF800000;
	fma.rn.f32 	%f212, %f212, %f169, %f166;

$L__BB17_48:
	mul.f32 	%f198, %f7, 0f3F22F983;
	cvt.rni.s32.f32 	%r252, %f198;
	cvt.rn.f32.s32 	%f170, %r252;
	mov.f32 	%f171, 0fBFC90FDA;
	fma.rn.f32 	%f172, %f170, %f171, %f7;
	mov.f32 	%f173, 0fB3A22168;
	fma.rn.f32 	%f174, %f170, %f173, %f172;
	mov.f32 	%f175, 0fA7C234C5;
	fma.rn.f32 	%f213, %f170, %f175, %f174;
	abs.f32 	%f55, %f7;
	setp.ltu.f32 	%p41, %f55, 0f47CE4780;
	@%p41 bra 	$L__BB17_56;

	setp.eq.f32 	%p42, %f55, 0f7F800000;
	@%p42 bra 	$L__BB17_55;
	bra.uni 	$L__BB17_50;

$L__BB17_55:
	mov.f32 	%f178, 0f00000000;
	mul.rn.f32 	%f213, %f7, %f178;
	mov.u32 	%r252, 0;
	bra.uni 	$L__BB17_56;

$L__BB17_50:
	mov.u64 	%rd89, 0;
	mov.u64 	%rd90, %rd89;

$L__BB17_51:
	.pragma "nounroll";
	mov.b32 	%r229, %f7;
	shl.b32 	%r228, %r229, 8;
	or.b32  	%r227, %r228, -2147483648;
	shl.b64 	%rd70, %rd89, 2;
	mov.u64 	%rd71, __cudart_i2opi_f;
	add.s64 	%rd72, %rd71, %rd70;
	ld.global.nc.u32 	%r202, [%rd72];
	mad.wide.u32 	%rd73, %r202, %r227, %rd90;
	shr.u64 	%rd90, %rd73, 32;
	add.s64 	%rd74, %rd1, %rd70;
	st.local.u32 	[%rd74], %rd73;
	cvt.u32.u64 	%r203, %rd89;
	add.s32 	%r204, %r203, 1;
	cvt.s64.s32 	%rd89, %r204;
	setp.ne.s32 	%p43, %r204, 6;
	@%p43 bra 	$L__BB17_51;

	setp.eq.s32 	%p44, %r18, 0;
	st.local.u32 	[%rd8], %rd90;
	ld.local.u32 	%r250, [%rd6];
	ld.local.u32 	%r251, [%rd6+-4];
	@%p44 bra 	$L__BB17_54;

	shl.b32 	%r205, %r250, %r18;
	shr.u32 	%r206, %r251, %r20;
	add.s32 	%r250, %r206, %r205;
	ld.local.u32 	%r207, [%rd7];
	shr.u32 	%r208, %r207, %r20;
	shl.b32 	%r209, %r251, %r18;
	add.s32 	%r251, %r208, %r209;

$L__BB17_54:
	shr.u32 	%r210, %r251, 30;
	shl.b32 	%r211, %r250, 2;
	or.b32  	%r212, %r210, %r211;
	shr.u32 	%r213, %r212, 31;
	shr.u32 	%r214, %r250, 30;
	add.s32 	%r215, %r213, %r214;
	neg.s32 	%r216, %r215;
	setp.eq.s32 	%p45, %r14, 0;
	selp.b32 	%r252, %r215, %r216, %p45;
	setp.ne.s32 	%p46, %r213, 0;
	selp.b32 	%r217, %r19, %r14, %p46;
	selp.b32 	%r218, -1, 0, %p46;
	xor.b32  	%r219, %r212, %r218;
	shl.b32 	%r220, %r251, 2;
	xor.b32  	%r221, %r220, %r218;
	cvt.u64.u32 	%rd75, %r219;
	cvt.u64.u32 	%rd76, %r221;
	bfi.b64 	%rd77, %rd75, %rd76, 32, 32;
	cvt.rn.f64.s64 	%fd7, %rd77;
	mul.f64 	%fd8, %fd7, 0d3BF921FB54442D19;
	cvt.rn.f32.f64 	%f176, %fd8;
	setp.eq.s32 	%p47, %r217, 0;
	neg.f32 	%f177, %f176;
	selp.f32 	%f213, %f176, %f177, %p47;

$L__BB17_56:
	and.b32  	%r75, %r252, 1;
	setp.eq.s32 	%p48, %r75, 0;
	selp.f32 	%f59, %f213, 0f3F800000, %p48;
	mul.rn.f32 	%f60, %f213, %f213;
	mov.f32 	%f214, 0fB94D4153;
	@%p48 bra 	$L__BB17_58;

	mov.f32 	%f180, 0fBAB607ED;
	mov.f32 	%f181, 0f37CBAC00;
	fma.rn.f32 	%f214, %f181, %f60, %f180;

$L__BB17_58:
	selp.f32 	%f182, 0f3C0885E4, 0f3D2AAABB, %p48;
	fma.rn.f32 	%f183, %f214, %f60, %f182;
	selp.f32 	%f184, 0fBE2AAAA8, 0fBEFFFFFF, %p48;
	fma.rn.f32 	%f185, %f183, %f60, %f184;
	mov.f32 	%f186, 0f00000000;
	fma.rn.f32 	%f187, %f60, %f59, %f186;
	fma.rn.f32 	%f215, %f185, %f187, %f59;
	and.b32  	%r223, %r252, 2;
	setp.eq.s32 	%p50, %r223, 0;
	@%p50 bra 	$L__BB17_60;

	mov.f32 	%f189, 0fBF800000;
	fma.rn.f32 	%f215, %f215, %f189, %f186;

$L__BB17_60:
	mul.f32 	%f190, %f212, 0f40400000;
	mul.f32 	%f191, %f190, %f215;
	div.rn.f32 	%f216, %f191, %f10;

$L__BB17_61:
	mul.f32 	%f68, %f209, %f216;
	setp.eq.f32 	%p51, %f68, 0f00000000;
	@%p51 bra 	$L__BB17_63;

	add.s32 	%r224, %r239, %r10;
	mul.wide.s32 	%rd78, %r224, 4;
	add.s64 	%rd79, %rd3, %rd78;
	ld.global.f32 	%f192, [%rd79];
	fma.rn.f32 	%f217, %f68, %f192, %f217;
	add.f32 	%f218, %f218, %f68;

$L__BB17_63:
	add.s32 	%r76, %r239, 1;
	setp.lt.s32 	%p52, %r239, %r4;
	mov.u32 	%r239, %r76;
	@%p52 bra 	$L__BB17_7;

$L__BB17_64:
	add.s32 	%r77, %r238, 1;
	setp.lt.s32 	%p53, %r238, %r6;
	mov.u32 	%r238, %r77;
	@%p53 bra 	$L__BB17_5;

$L__BB17_65:
	setp.leu.f32 	%p54, %f218, 0f00000000;
	@%p54 bra 	$L__BB17_67;

	div.rn.f32 	%f223, %f217, %f218;

$L__BB17_67:
	ld.param.u32 	%r232, [_Z20LanczosUpscaleKernelPfiiPKfiii_param_6];
	ld.param.u32 	%r231, [_Z20LanczosUpscaleKernelPfiiPKfiii_param_2];
	ld.param.u32 	%r230, [_Z20LanczosUpscaleKernelPfiiPKfiii_param_1];
	mov.f32 	%f194, 0f3F800000;
	min.f32 	%f195, %f194, %f223;
	mov.f32 	%f196, 0f00000000;
	max.f32 	%f197, %f196, %f195;
	mad.lo.s32 	%r225, %r237, %r231, %r2;
	mad.lo.s32 	%r226, %r225, %r230, %r1;
	mul.wide.s32 	%rd80, %r226, 4;
	add.s64 	%rd81, %rd2, %rd80;
	st.global.f32 	[%rd81], %f197;
	add.s32 	%r237, %r237, 1;
	setp.lt.s32 	%p55, %r237, %r232;
	@%p55 bra 	$L__BB17_3;

$L__BB17_68:
	ret;

}
	// .globl	ByteNv12ToPlanarFloatRgbKernel
.visible .entry ByteNv12ToPlanarFloatRgbKernel(
	.param .u64 ByteNv12ToPlanarFloatRgbKernel_param_0,
	.param .u64 ByteNv12ToPlanarFloatRgbKernel_param_1,
	.param .u32 ByteNv12ToPlanarFloatRgbKernel_param_2,
	.param .u32 ByteNv12ToPlanarFloatRgbKernel_param_3,
	.param .u64 ByteNv12ToPlanarFloatRgbKernel_param_4
)
{
	.reg .pred 	%p<4>;
	.reg .b16 	%rs<4>;
	.reg .f32 	%f<22>;
	.reg .b32 	%r<22>;
	.reg .b64 	%rd<19>;


	ld.param.u64 	%rd1, [ByteNv12ToPlanarFloatRgbKernel_param_0];
	ld.param.u64 	%rd2, [ByteNv12ToPlanarFloatRgbKernel_param_1];
	ld.param.u32 	%r3, [ByteNv12ToPlanarFloatRgbKernel_param_2];
	ld.param.u32 	%r4, [ByteNv12ToPlanarFloatRgbKernel_param_3];
	ld.param.u64 	%rd3, [ByteNv12ToPlanarFloatRgbKernel_param_4];
	mov.u32 	%r5, %ntid.x;
	mov.u32 	%r6, %ctaid.x;
	mov.u32 	%r7, %tid.x;
	mad.lo.s32 	%r1, %r6, %r5, %r7;
	mov.u32 	%r8, %ntid.y;
	mov.u32 	%r9, %ctaid.y;
	mov.u32 	%r10, %tid.y;
	mad.lo.s32 	%r2, %r9, %r8, %r10;
	setp.ge.s32 	%p1, %r1, %r3;
	setp.ge.s32 	%p2, %r2, %r4;
	or.pred  	%p3, %p1, %p2;
	@%p3 bra 	$L__BB18_2;

	cvta.to.global.u64 	%rd4, %rd2;
	cvta.to.global.u64 	%rd5, %rd1;
	cvt.u32.u64 	%r11, %rd3;
	mad.lo.s32 	%r12, %r2, %r11, %r1;
	shr.u32 	%r13, %r2, 31;
	add.s32 	%r14, %r2, %r13;
	shr.s32 	%r15, %r14, 1;
	and.b32  	%r16, %r1, -2;
	mad.lo.s32 	%r17, %r15, %r11, %r16;
	cvt.s64.s32 	%rd6, %r4;
	mul.lo.s64 	%rd7, %rd6, %rd3;
	cvt.s64.s32 	%rd8, %r12;
	add.s64 	%rd9, %rd5, %rd8;
	ld.global.u8 	%rs1, [%rd9];
	cvt.rn.f32.u16 	%f1, %rs1;
	cvt.s64.s32 	%rd10, %r17;
	add.s64 	%rd11, %rd7, %rd10;
	add.s64 	%rd12, %rd5, %rd11;
	ld.global.u8 	%rs2, [%rd12];
	cvt.rn.f32.u16 	%f2, %rs2;
	ld.global.u8 	%rs3, [%rd12+1];
	cvt.rn.f32.u16 	%f3, %rs3;
	add.f32 	%f4, %f1, 0fC1800000;
	div.rn.f32 	%f5, %f4, 0f435B0000;
	add.f32 	%f6, %f2, 0fC3000000;
	div.rn.f32 	%f7, %f6, 0f43600000;
	add.f32 	%f8, %f3, 0fC3000000;
	div.rn.f32 	%f9, %f8, 0f43600000;
	fma.rn.f32 	%f10, %f9, 0f3FB374BC, %f5;
	fma.rn.f32 	%f11, %f7, 0fBEB020C5, %f5;
	fma.rn.f32 	%f12, %f9, 0fBF36C8B4, %f11;
	fma.rn.f32 	%f13, %f7, 0f3FE2D0E5, %f5;
	mov.f32 	%f14, 0f3F800000;
	min.f32 	%f15, %f14, %f10;
	mov.f32 	%f16, 0f00000000;
	max.f32 	%f17, %f16, %f15;
	min.f32 	%f18, %f14, %f12;
	max.f32 	%f19, %f16, %f18;
	min.f32 	%f20, %f14, %f13;
	max.f32 	%f21, %f16, %f20;
	mad.lo.s32 	%r18, %r2, %r3, %r1;
	mul.wide.s32 	%rd13, %r18, 4;
	add.s64 	%rd14, %rd4, %rd13;
	st.global.f32 	[%rd14], %f17;
	mul.lo.s32 	%r19, %r4, %r3;
	add.s32 	%r20, %r18, %r19;
	mul.wide.s32 	%rd15, %r19, 4;
	add.s64 	%rd16, %rd14, %rd15;
	st.global.f32 	[%rd16], %f19;
	add.s32 	%r21, %r20, %r19;
	mul.wide.s32 	%rd17, %r21, 4;
	add.s64 	%rd18, %rd4, %rd17;
	st.global.f32 	[%rd18], %f21;

$L__BB18_2:
	ret;

}
	// .globl	Yuv420pToPlanarFloatRgbKernel
.visible .entry Yuv420pToPlanarFloatRgbKernel(
	.param .u64 Yuv420pToPlanarFloatRgbKernel_param_0,
	.param .u64 Yuv420pToPlanarFloatRgbKernel_param_1,
	.param .u64 Yuv420pToPlanarFloatRgbKernel_param_2,
	.param .u64 Yuv420pToPlanarFloatRgbKernel_param_3,
	.param .u64 Yuv420pToPlanarFloatRgbKernel_param_4,
	.param .u64 Yuv420pToPlanarFloatRgbKernel_param_5,
	.param .u32 Yuv420pToPlanarFloatRgbKernel_param_6,
	.param .u32 Yuv420pToPlanarFloatRgbKernel_param_7,
	.param .u32 Yuv420pToPlanarFloatRgbKernel_param_8,
	.param .u32 Yuv420pToPlanarFloatRgbKernel_param_9,
	.param .u32 Yuv420pToPlanarFloatRgbKernel_param_10
)
{
	.reg .pred 	%p<4>;
	.reg .b16 	%rs<4>;
	.reg .f32 	%f<21>;
	.reg .b32 	%r<24>;
	.reg .b64 	%rd<23>;


	ld.param.u64 	%rd1, [Yuv420pToPlanarFloatRgbKernel_param_0];
	ld.param.u64 	%rd2, [Yuv420pToPlanarFloatRgbKernel_param_1];
	ld.param.u64 	%rd3, [Yuv420pToPlanarFloatRgbKernel_param_2];
	ld.param.u64 	%rd4, [Yuv420pToPlanarFloatRgbKernel_param_3];
	ld.param.u64 	%rd5, [Yuv420pToPlanarFloatRgbKernel_param_4];
	ld.param.u64 	%rd6, [Yuv420pToPlanarFloatRgbKernel_param_5];
	ld.param.u32 	%r3, [Yuv420pToPlanarFloatRgbKernel_param_6];
	ld.param.u32 	%r7, [Yuv420pToPlanarFloatRgbKernel_param_7];
	ld.param.u32 	%r4, [Yuv420pToPlanarFloatRgbKernel_param_8];
	ld.param.u32 	%r5, [Yuv420pToPlanarFloatRgbKernel_param_9];
	ld.param.u32 	%r6, [Yuv420pToPlanarFloatRgbKernel_param_10];
	mov.u32 	%r8, %ctaid.x;
	mov.u32 	%r9, %ntid.x;
	mov.u32 	%r10, %tid.x;
	mad.lo.s32 	%r1, %r8, %r9, %r10;
	mov.u32 	%r11, %ntid.y;
	mov.u32 	%r12, %ctaid.y;
	mov.u32 	%r13, %tid.y;
	mad.lo.s32 	%r2, %r12, %r11, %r13;
	setp.ge.s32 	%p1, %r1, %r3;
	setp.ge.s32 	%p2, %r2, %r7;
	or.pred  	%p3, %p1, %p2;
	@%p3 bra 	$L__BB19_2;

	cvta.to.global.u64 	%rd7, %rd1;
	mad.lo.s32 	%r14, %r2, %r4, %r1;
	cvt.s64.s32 	%rd8, %r14;
	add.s64 	%rd9, %rd7, %rd8;
	shr.u32 	%r15, %r2, 31;
	add.s32 	%r16, %r2, %r15;
	shr.s32 	%r17, %r16, 1;
	shr.u32 	%r18, %r1, 31;
	add.s32 	%r19, %r1, %r18;
	shr.s32 	%r20, %r19, 1;
	mad.lo.s32 	%r21, %r17, %r5, %r20;
	cvt.s64.s32 	%rd10, %r21;
	cvta.to.global.u64 	%rd11, %rd2;
	add.s64 	%rd12, %rd11, %rd10;
	mad.lo.s32 	%r22, %r17, %r6, %r20;
	cvt.s64.s32 	%rd13, %r22;
	cvta.to.global.u64 	%rd14, %rd3;
	add.s64 	%rd15, %rd14, %rd13;
	ld.global.u8 	%rs1, [%rd9];
	cvt.rn.f32.u16 	%f1, %rs1;
	ld.global.u8 	%rs2, [%rd12];
	cvt.rn.f32.u16 	%f2, %rs2;
	add.f32 	%f3, %f2, 0fC3000000;
	ld.global.u8 	%rs3, [%rd15];
	cvt.rn.f32.u16 	%f4, %rs3;
	add.f32 	%f5, %f4, 0fC3000000;
	fma.rn.f32 	%f6, %f5, 0f3FB374BC, %f1;
	fma.rn.f32 	%f7, %f3, 0fBEB03298, %f1;
	fma.rn.f32 	%f8, %f5, 0fBF36D19E, %f7;
	fma.rn.f32 	%f9, %f3, 0f3FE2D0E5, %f1;
	mov.f32 	%f10, 0f00000000;
	max.f32 	%f11, %f6, %f10;
	mov.f32 	%f12, 0f437F0000;
	min.f32 	%f13, %f11, %f12;
	div.rn.f32 	%f14, %f13, 0f437F0000;
	mad.lo.s32 	%r23, %r2, %r3, %r1;
	cvta.to.global.u64 	%rd16, %rd4;
	mul.wide.s32 	%rd17, %r23, 4;
	add.s64 	%rd18, %rd16, %rd17;
	st.global.f32 	[%rd18], %f14;
	max.f32 	%f15, %f8, %f10;
	min.f32 	%f16, %f15, %f12;
	div.rn.f32 	%f17, %f16, 0f437F0000;
	cvta.to.global.u64 	%rd19, %rd5;
	add.s64 	%rd20, %rd19, %rd17;
	st.global.f32 	[%rd20], %f17;
	max.f32 	%f18, %f9, %f10;
	min.f32 	%f19, %f18, %f12;
	div.rn.f32 	%f20, %f19, 0f437F0000;
	cvta.to.global.u64 	%rd21, %rd6;
	add.s64 	%rd22, %rd21, %rd17;
	st.global.f32 	[%rd22], %f20;

$L__BB19_2:
	ret;

}
	// .globl	convertFloatAlphaToYuv420Kernel
.visible .entry convertFloatAlphaToYuv420Kernel(
	.param .u64 convertFloatAlphaToYuv420Kernel_param_0,
	.param .u64 convertFloatAlphaToYuv420Kernel_param_1,
	.param .u32 convertFloatAlphaToYuv420Kernel_param_2,
	.param .u32 convertFloatAlphaToYuv420Kernel_param_3,
	.param .u32 convertFloatAlphaToYuv420Kernel_param_4,
	.param .u32 convertFloatAlphaToYuv420Kernel_param_5
)
{
	.reg .pred 	%p<9>;
	.reg .b16 	%rs<2>;
	.reg .f32 	%f<7>;
	.reg .b32 	%r<30>;
	.reg .b64 	%rd<13>;


	ld.param.u64 	%rd2, [convertFloatAlphaToYuv420Kernel_param_0];
	ld.param.u64 	%rd3, [convertFloatAlphaToYuv420Kernel_param_1];
	ld.param.u32 	%r3, [convertFloatAlphaToYuv420Kernel_param_2];
	ld.param.u32 	%r4, [convertFloatAlphaToYuv420Kernel_param_3];
	ld.param.u32 	%r5, [convertFloatAlphaToYuv420Kernel_param_4];
	ld.param.u32 	%r6, [convertFloatAlphaToYuv420Kernel_param_5];
	cvta.to.global.u64 	%rd1, %rd3;
	mov.u32 	%r7, %ntid.x;
	mov.u32 	%r8, %ctaid.x;
	mov.u32 	%r9, %tid.x;
	mad.lo.s32 	%r1, %r8, %r7, %r9;
	mov.u32 	%r10, %ntid.y;
	mov.u32 	%r11, %ctaid.y;
	mov.u32 	%r12, %tid.y;
	mad.lo.s32 	%r2, %r11, %r10, %r12;
	setp.ge.s32 	%p1, %r1, %r3;
	setp.ge.s32 	%p2, %r2, %r4;
	or.pred  	%p3, %p1, %p2;
	@%p3 bra 	$L__BB20_3;

	cvta.to.global.u64 	%rd4, %rd2;
	mad.lo.s32 	%r13, %r2, %r3, %r1;
	mul.wide.s32 	%rd5, %r13, 4;
	add.s64 	%rd6, %rd4, %rd5;
	ld.global.f32 	%f1, [%rd6];
	mov.f32 	%f2, 0f3F800000;
	min.f32 	%f3, %f2, %f1;
	mov.f32 	%f4, 0f00000000;
	max.f32 	%f5, %f4, %f3;
	mul.f32 	%f6, %f5, 0f437F0000;
	cvt.rzi.u32.f32 	%r14, %f6;
	mad.lo.s32 	%r15, %r2, %r5, %r1;
	cvt.s64.s32 	%rd7, %r15;
	add.s64 	%rd8, %rd1, %rd7;
	st.global.u8 	[%rd8], %r14;
	and.b32  	%r16, %r2, 1;
	setp.eq.b32 	%p4, %r16, 1;
	and.b32  	%r17, %r1, 1;
	setp.eq.b32 	%p5, %r17, 1;
	or.pred  	%p6, %p5, %p4;
	mov.pred 	%p7, 0;
	xor.pred  	%p8, %p6, %p7;
	@%p8 bra 	$L__BB20_3;

	shr.u32 	%r18, %r4, 31;
	add.s32 	%r19, %r4, %r18;
	shr.s32 	%r20, %r19, 1;
	shr.u32 	%r21, %r2, 31;
	add.s32 	%r22, %r2, %r21;
	shr.s32 	%r23, %r22, 1;
	shr.u32 	%r24, %r1, 31;
	add.s32 	%r25, %r1, %r24;
	shr.s32 	%r26, %r25, 1;
	mad.lo.s32 	%r27, %r23, %r6, %r26;
	mad.lo.s32 	%r28, %r5, %r4, %r27;
	cvt.s64.s32 	%rd9, %r28;
	add.s64 	%rd10, %rd1, %rd9;
	mov.u16 	%rs1, 128;
	st.global.u8 	[%rd10], %rs1;
	mad.lo.s32 	%r29, %r20, %r6, %r28;
	cvt.s64.s32 	%rd11, %r29;
	add.s64 	%rd12, %rd1, %rd11;
	st.global.u8 	[%rd12], %rs1;

$L__BB20_3:
	ret;

}
	// .globl	convertYuv420ToFloatAlphaKernel
.visible .entry convertYuv420ToFloatAlphaKernel(
	.param .u64 convertYuv420ToFloatAlphaKernel_param_0,
	.param .u64 convertYuv420ToFloatAlphaKernel_param_1,
	.param .u32 convertYuv420ToFloatAlphaKernel_param_2,
	.param .u32 convertYuv420ToFloatAlphaKernel_param_3,
	.param .u32 convertYuv420ToFloatAlphaKernel_param_4
)
{
	.reg .pred 	%p<4>;
	.reg .b16 	%rs<2>;
	.reg .f32 	%f<3>;
	.reg .b32 	%r<14>;
	.reg .b64 	%rd<9>;


	ld.param.u64 	%rd1, [convertYuv420ToFloatAlphaKernel_param_0];
	ld.param.u64 	%rd2, [convertYuv420ToFloatAlphaKernel_param_1];
	ld.param.u32 	%r3, [convertYuv420ToFloatAlphaKernel_param_2];
	ld.param.u32 	%r5, [convertYuv420ToFloatAlphaKernel_param_3];
	ld.param.u32 	%r4, [convertYuv420ToFloatAlphaKernel_param_4];
	mov.u32 	%r6, %ctaid.x;
	mov.u32 	%r7, %ntid.x;
	mov.u32 	%r8, %tid.x;
	mad.lo.s32 	%r1, %r6, %r7, %r8;
	mov.u32 	%r9, %ntid.y;
	mov.u32 	%r10, %ctaid.y;
	mov.u32 	%r11, %tid.y;
	mad.lo.s32 	%r2, %r10, %r9, %r11;
	setp.ge.s32 	%p1, %r1, %r3;
	setp.ge.s32 	%p2, %r2, %r5;
	or.pred  	%p3, %p1, %p2;
	@%p3 bra 	$L__BB21_2;

	cvta.to.global.u64 	%rd3, %rd1;
	mad.lo.s32 	%r12, %r2, %r4, %r1;
	mad.lo.s32 	%r13, %r2, %r3, %r1;
	cvt.s64.s32 	%rd4, %r12;
	add.s64 	%rd5, %rd3, %rd4;
	ld.global.u8 	%rs1, [%rd5];
	cvt.rn.f32.u16 	%f1, %rs1;
	div.rn.f32 	%f2, %f1, 0f437F0000;
	cvta.to.global.u64 	%rd6, %rd2;
	mul.wide.s32 	%rd7, %r13, 4;
	add.s64 	%rd8, %rd6, %rd7;
	st.global.f32 	[%rd8], %f2;

$L__BB21_2:
	ret;

}

