// DirectVideoWriter.cpp
#include "DirectVideoWriter.h"
#include <stdexcept>
#include <iostream>
#include <cmath>

#ifdef _WIN32
#include <windows.h>
#endif

// Helper function for CUDA error handling
static void CheckCudaError(CUresult error) {
	if (error != CUDA_SUCCESS) {
        const char* errorStr;
        cuGetErrorString(error, &errorStr);
		throw std::runtime_error(std::string("CUDA error: ") + errorStr);
	}
}

// FFmpeg includes
extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/hwcontext.h>
#include <libavutil/hwcontext_cuda.h>
#include <libavutil/opt.h>
#include <libavutil/rational.h>
#include <libavutil/error.h>
#include <libavutil/mem.h>
#include <libavutil/imgutils.h>
#include <libavutil/mathematics.h>
}



// Helper to convert wstring to string (Windows only)
#ifdef _WIN32
static std::string WStringToString(const std::wstring& wstr)
{
    if (wstr.empty()) return std::string();
    int size_needed = WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), NULL, 0, NULL, NULL);
    std::string strTo(size_needed, 0);
    WideCharToMultiByte(CP_UTF8, 0, &wstr[0], (int)wstr.size(), &strTo[0], size_needed, NULL, NULL);
    return strTo;
}
#endif

// Helper to normalize AVRational to reasonable values for encoding
static AVRational NormalizeFrameRate(const AVRational& frameRate) {
    // Convert to double and back to get a reasonable fraction
    double fps = static_cast<double>(frameRate.num) / frameRate.den;

    // For common frame rates, use exact values
    if (abs(fps - 23.976) < 0.001) return {24000, 1001};
    if (abs(fps - 24.0) < 0.001) return {24, 1};
    if (abs(fps - 25.0) < 0.001) return {25, 1};
    if (abs(fps - 29.97) < 0.001) return {30000, 1001};
    if (abs(fps - 30.0) < 0.001) return {30, 1};
    if (abs(fps - 50.0) < 0.001) return {50, 1};
    if (abs(fps - 59.94) < 0.001) return {60000, 1001};
    if (abs(fps - 60.0) < 0.001) return {60, 1};

    // For other frame rates, use a simpler approach to avoid large fractions
    // Round to nearest 0.001 and use a reasonable denominator
    int roundedFps = static_cast<int>(fps * 1000 + 0.5);

    // Use a smaller denominator to avoid huge time_base values
    // Try common denominators first
    int denominators[] = {1, 2, 4, 5, 8, 10, 25, 50, 100, 1000};

    for (int den : denominators) {
        int num = static_cast<int>(fps * den + 0.5);
        double testFps = static_cast<double>(num) / den;
        if (abs(testFps - fps) < 0.01) { // Within 0.01 fps tolerance
            return {num, den};
        }
    }

    // Fallback: use a reasonable fraction with limited precision
    return {static_cast<int>(fps * 100 + 0.5), 100};
}


// Constructor
DirectVideoWriter::DirectVideoWriter()
    : m_formatContext(nullptr)
    , m_codecContext(nullptr)
    , m_stream(nullptr)
    , m_packet(nullptr)
    , m_hwFrame(nullptr)
    , m_hwDeviceCtx(nullptr)
    , m_hwFramesCtx(nullptr)
    , m_cudaContext(nullptr)
    , m_cudaStream(0)
    , m_internalYuv420Buffer(0)
    , m_internalYuv420BufferSize(0)
    , m_internalYuv420YPitch(0)
    , m_internalYuv420UVPitch(0)
    , m_width(0)
    , m_height(0)
    , m_frameRate({0, 1})
    , m_codec()
    , m_outputPath()
    , m_isInitialized(false)
    , m_isFinalized(false)
    , m_frameIndex(0)
{
}

// Destructor
DirectVideoWriter::~DirectVideoWriter()
{
    Cleanup();
}

// Create a new video writer using CodecConfig
std::shared_ptr<DirectVideoWriter> DirectVideoWriter::Create(
    const std::string& outputPath,
    const CodecConfig& config,
    CUcontext cudaContext
)
{
    std::shared_ptr<DirectVideoWriter> writer(new DirectVideoWriter());
    if (writer->Initialize(outputPath, config, cudaContext)) {
        return writer;
    }
    return nullptr;
}

// Initialize the video writer using CodecConfig
bool DirectVideoWriter::Initialize(
    const std::string& outputPath,
    const CodecConfig& config,
    CUcontext cudaContext
)
{
    // Enable debug logging to get detailed error info from FFmpeg
    //av_log_set_level(AV_LOG_DEBUG);

    AVBufferRef* hw_device_ctx = nullptr;
    AVBufferRef* hw_frames_ref = nullptr;

    try {
        // Store parameters
        m_cudaContext = cudaContext;
        m_width = config.width;
        m_height = config.height;
        m_codec = config.GetCodecName();
        m_outputPath = outputPath;
        // Hardware acceleration is always enabled
        m_frameRate = NormalizeFrameRate({static_cast<int>(config.frameRate * 1000), 1000});

        if (!m_cudaContext) {
            throw std::runtime_error("A valid CUDA context must be provided.");
        }

        CheckCudaError(cuStreamCreate(&m_cudaStream, CU_STREAM_DEFAULT));

        if (avformat_alloc_output_context2(&m_formatContext, nullptr, nullptr, outputPath.c_str()) < 0) {
            throw std::runtime_error("Could not allocate output format context.");
        }

        const AVCodec* encoder = avcodec_find_encoder_by_name(m_codec.c_str());
        if (!encoder) {
            throw std::runtime_error("Could not find encoder: " + m_codec);
        }

        m_stream = avformat_new_stream(m_formatContext, encoder);
        if (!m_stream) {
            throw std::runtime_error("Could not create new video stream.");
        }

        m_codecContext = avcodec_alloc_context3(encoder);
        if (!m_codecContext) {
            throw std::runtime_error("Could not allocate codec context.");
        }

        // --- HARDWARE CONTEXT SETUP (always on) ---
        // 1. Create the HW Device Context referencing the user's CUcontext
        m_hwDeviceCtx = av_hwdevice_ctx_alloc(AV_HWDEVICE_TYPE_CUDA);
        if (!m_hwDeviceCtx) throw std::runtime_error("Failed to create CUDA hardware device context.");

        auto* device_ctx = (AVHWDeviceContext*)m_hwDeviceCtx->data;
        auto* cuda_hw_ctx = (AVCUDADeviceContext*)device_ctx->hwctx;
        cuda_hw_ctx->cuda_ctx = m_cudaContext;
        cuda_hw_ctx->stream = m_cudaStream;
        if (av_hwdevice_ctx_init(m_hwDeviceCtx) < 0) {
            throw std::runtime_error("Failed to initialize CUDA hardware device context.");
        }

        // 2. Create the HW Frames Context
        m_hwFramesCtx = av_hwframe_ctx_alloc(m_hwDeviceCtx);
        if (!m_hwFramesCtx) throw std::runtime_error("Failed to create CUDA hardware frames context.");

        auto* frames_ctx = (AVHWFramesContext*)m_hwFramesCtx->data;
        frames_ctx->format = AV_PIX_FMT_CUDA;
        // Always use YUV420P for alpha storage (Y carries alpha, U/V set to 128)
        frames_ctx->sw_format = AV_PIX_FMT_YUV420P;
        frames_ctx->width = m_width;
        frames_ctx->height = m_height;
        frames_ctx->initial_pool_size = 20; // A decent pool size

        if (av_hwframe_ctx_init(m_hwFramesCtx) < 0) {
            throw std::runtime_error("Failed to initialize CUDA hardware frames context.");
        }

        // 3. Set codec parameters and assign the contexts
        m_codecContext->hw_device_ctx = av_buffer_ref(m_hwDeviceCtx);
        m_codecContext->hw_frames_ctx = av_buffer_ref(m_hwFramesCtx);
        if (!m_codecContext->hw_device_ctx || !m_codecContext->hw_frames_ctx) {
            throw std::runtime_error("Failed to set hardware contexts on codec.");
        }

        std::cout << "DirectVideoWriter: Using frame rate: " << m_frameRate.num << "/" << m_frameRate.den
                  << " (" << (static_cast<double>(m_frameRate.num) / m_frameRate.den) << " fps)" << std::endl;

        m_codecContext->width = m_width;
        m_codecContext->height = m_height;

        // Use a standard time_base that works well with most containers
        AVRational timeBase = {1, 90000};  // 90kHz time base (common for H.264)
        m_codecContext->time_base = timeBase;
        m_codecContext->framerate = m_frameRate;
        m_codecContext->gop_size = config.gopSize;
        m_codecContext->max_b_frames = config.bFrames;
        m_codecContext->pix_fmt = AV_PIX_FMT_CUDA;

        std::cout << "DirectVideoWriter: Using time_base: " << timeBase.num << "/" << timeBase.den << std::endl;

        AVDictionary* options = nullptr;

        // Preset and profile
        av_dict_set(&options, "preset", config.GetPresetString().c_str(), 0);
        av_dict_set(&options, "profile", config.GetProfileName().c_str(), 0);

        // Rate control
        av_dict_set(&options, "rc", config.GetRateControlString().c_str(), 0);
        switch (config.rateControl) {
            case CodecConfig::RateControlMode::CONSTANT_QUALITY:
                av_dict_set_int(&options, "cq", config.quality, 0);
                m_codecContext->bit_rate = 0; // ignored in CQ
                break;
            case CodecConfig::RateControlMode::CONSTANT_QP:
                av_dict_set_int(&options, "qp", config.quality, 0);
                break;
            case CodecConfig::RateControlMode::VARIABLE_BITRATE:
                m_codecContext->bit_rate = static_cast<int64_t>(config.bitrateMbps) * 1000000;
                av_dict_set_int(&options, "maxrate", static_cast<int64_t>(config.maxBitrateMbps) * 1000000, 0);
                av_dict_set_int(&options, "bufsize", static_cast<int64_t>(config.maxBitrateMbps) * 2000000, 0); // ~2x maxrate
                break;
            case CodecConfig::RateControlMode::CONSTANT_BITRATE:
                m_codecContext->bit_rate = static_cast<int64_t>(config.bitrateMbps) * 1000000;
                av_dict_set_int(&options, "maxrate", static_cast<int64_t>(config.bitrateMbps) * 1000000, 0);
                av_dict_set_int(&options, "bufsize", static_cast<int64_t>(config.bitrateMbps) * 2000000, 0);
                break;
        }

        // Low latency tuning
        if (config.lowLatency) {
            av_dict_set(&options, "tune", "ll", 0); // low-latency
            av_dict_set_int(&options, "rc-lookahead", 0, 0);
        }

        // Lossless mode
        if (config.lossless) {
            av_dict_set_int(&options, "lossless", 1, 0);
            av_dict_set(&options, "rc", "constqp", 0);
            av_dict_set_int(&options, "qp", 0, 0);
            m_codecContext->bit_rate = 0;
        }

        // 4. Open the codec now that all contexts are set up
        if (avcodec_open2(m_codecContext, encoder, &options) < 0) {
            av_dict_free(&options);
            throw std::runtime_error("Could not open codec. Check logs for details.");
        }
        av_dict_free(&options);

        if (avcodec_parameters_from_context(m_stream->codecpar, m_codecContext) < 0) {
            throw std::runtime_error("Could not copy codec parameters to stream.");
        }

        // Set the stream's time_base to match the codec's time_base
        m_stream->time_base = m_codecContext->time_base;
        std::cout << "DirectVideoWriter: Stream time_base set to: " << m_stream->time_base.num << "/" << m_stream->time_base.den << std::endl;

        // Calculate and display expected PTS increment
        double fps = static_cast<double>(m_frameRate.num) / m_frameRate.den;
        int64_t ptsIncrement = static_cast<int64_t>(90000.0 / fps + 0.5);
        std::cout << "DirectVideoWriter: PTS increment per frame: " << ptsIncrement << " (for " << fps << " fps)" << std::endl;

        m_hwFrame = av_frame_alloc();
        m_packet = av_packet_alloc();
        if (!m_hwFrame || !m_packet) throw std::runtime_error("Could not allocate frame or packet.");

        // Allocate hardware frame
        if (av_hwframe_get_buffer(m_codecContext->hw_frames_ctx, m_hwFrame, 0) < 0) {
            throw std::runtime_error("Could not allocate hardware frame buffer.");
        }

        if (!(m_formatContext->oformat->flags & AVFMT_NOFILE)) {
            if (avio_open(&m_formatContext->pb, outputPath.c_str(), AVIO_FLAG_WRITE) < 0) {
                throw std::runtime_error("Could not open output file: " + outputPath);
            }
        }

        if (avformat_write_header(m_formatContext, nullptr) < 0) {
            throw std::runtime_error("Could not write video header.");
        }
        m_isInitialized = true;
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Error initializing DirectVideoWriter (config): " << e.what() << std::endl;
        Cleanup();
        return false;
    }
}

// Helper function to prepare frame with proper PTS
bool DirectVideoWriter::PrepareFrame() {
    // Calculate PTS increment based on time_base and frame rate
    // With 90kHz time_base, PTS increment = 90000 / fps
    double fps = static_cast<double>(m_frameRate.num) / m_frameRate.den;
    int64_t ptsIncrement = static_cast<int64_t>(90000.0 / fps + 0.5);

    m_hwFrame->pts = m_frameIndex * ptsIncrement;
    m_frameIndex++;

    return true;
}


// Write a grayscale frame (for alpha channels)
bool DirectVideoWriter::WriteGrayFrame(const float* d_grayData, CUstream stream) {
    if (!m_isInitialized || !d_grayData || m_isFinalized) return false;
    
    try {
        if (!PrepareFrame()) return false;
        
        // Convert grayscale float data to YUV420 (Y=alpha, U/V=128) and encode via hardware frame
        if (CreateHardwareFrame((const void*)d_grayData, true, stream)) {
            return EncodeFrame(m_hwFrame);
        }
        
        return false;
    }
    catch (const std::exception& e) {
        std::cerr << "Error writing grayscale frame: " << e.what() << std::endl;
        return false;
    }
}

// Write an RGB frame
bool DirectVideoWriter::WriteRgbFrame(const float* d_rgbData, CUstream stream) {
    if (!m_isInitialized || !d_rgbData || m_isFinalized) return false;
    
    try {
        if (!PrepareFrame()) return false;
        
        // Convert RGB float data to YUV format for hardware encoding
        if (CreateHardwareFrame((const void*)d_rgbData, false, stream)) {
            return EncodeFrame(m_hwFrame);
        }
        
        return false;
    }
    catch (const std::exception& e) {
        std::cerr << "Error writing RGB frame: " << e.what() << std::endl;
        return false;
    }
}

// Create hardware frame from CUDA data
AVFrame* DirectVideoWriter::CreateHardwareFrame(const void* d_data, bool isGray, CUstream stream) {
    if (!m_hwFrame || !d_data) return nullptr;

    if (isGray) {
        // Convert float alpha [0,1] to YUV420: Y=alpha(0..255), U=V=128
        int width = m_width;
        int height = m_height;

        // Ensure internal YUV420 buffer is allocated (contiguous layout: Y, then U, then V)
        int yPitch = (m_internalYuv420YPitch > 0) ? m_internalYuv420YPitch : width;
        int uvPitch = (m_internalYuv420UVPitch > 0) ? m_internalYuv420UVPitch : (width / 2);
        size_t ySize = static_cast<size_t>(yPitch) * height;
        size_t uvSize = static_cast<size_t>(uvPitch) * (height / 2);
        size_t requiredSize = ySize + 2 * uvSize;

        if (m_internalYuv420BufferSize < requiredSize || m_internalYuv420Buffer == 0) {
            if (m_internalYuv420Buffer) {
                cuMemFree(m_internalYuv420Buffer);
                m_internalYuv420Buffer = 0;
                m_internalYuv420BufferSize = 0;
            }
            CUresult allocRes = cuMemAlloc(&m_internalYuv420Buffer, requiredSize);
            if (allocRes != CUDA_SUCCESS) {
                const char* errStr = nullptr;
                cuGetErrorString(allocRes, &errStr);
                std::cerr << "CreateHardwareFrame: Failed to allocate YUV420 buffer: " << (errStr ? errStr : "unknown") << std::endl;
                return nullptr;
            }
            m_internalYuv420BufferSize = requiredSize;
            m_internalYuv420YPitch = yPitch;
            m_internalYuv420UVPitch = uvPitch;
        }

        // Launch kernel to write YUV420 data into the temporary buffer
        bool ok = LaunchFloatAlphaToYuv420(
            static_cast<const float*>(d_data),
            reinterpret_cast<uint8_t*>(m_internalYuv420Buffer),
            width,
            height,
            m_internalYuv420YPitch,
            m_internalYuv420UVPitch,
            stream
        );
        if (!ok) {
            std::cerr << "CreateHardwareFrame: LaunchFloatAlphaToYuv420 failed" << std::endl;
            return nullptr;
        }

        // Copy planes into the hardware frame
        CUDA_MEMCPY2D cpy{};
        // Y plane
        cpy.srcMemoryType = CU_MEMORYTYPE_DEVICE;
        cpy.dstMemoryType = CU_MEMORYTYPE_DEVICE;
        cpy.srcDevice = m_internalYuv420Buffer;
        cpy.srcPitch = m_internalYuv420YPitch;
        cpy.dstDevice = (CUdeviceptr)m_hwFrame->data[0];
        cpy.dstPitch = m_hwFrame->linesize[0];
        cpy.WidthInBytes = width;
        cpy.Height = height;
        CUresult res = cuMemcpy2DAsync(&cpy, stream);
        if (res != CUDA_SUCCESS) {
            const char* errStr = nullptr;
            cuGetErrorString(res, &errStr);
            std::cerr << "CreateHardwareFrame: Failed to copy Y plane: " << (errStr ? errStr : "unknown") << std::endl;
            return nullptr;
        }

        // U plane
        size_t uOffset = static_cast<size_t>(m_internalYuv420YPitch) * height;
        cpy.srcDevice = m_internalYuv420Buffer + uOffset;
        cpy.srcPitch = m_internalYuv420UVPitch;
        cpy.dstDevice = (CUdeviceptr)m_hwFrame->data[1];
        cpy.dstPitch = m_hwFrame->linesize[1];
        cpy.WidthInBytes = width / 2;
        cpy.Height = height / 2;
        res = cuMemcpy2DAsync(&cpy, stream);
        if (res != CUDA_SUCCESS) {
            const char* errStr = nullptr;
            cuGetErrorString(res, &errStr);
            std::cerr << "CreateHardwareFrame: Failed to copy U plane: " << (errStr ? errStr : "unknown") << std::endl;
            return nullptr;
        }

        // V plane
        size_t vOffset = uOffset + static_cast<size_t>(m_internalYuv420UVPitch) * (height / 2);
		cpy.srcDevice = m_internalYuv420Buffer + vOffset;
        cpy.srcPitch = m_internalYuv420UVPitch;
        cpy.dstDevice = (CUdeviceptr)m_hwFrame->data[2];
        cpy.dstPitch = m_hwFrame->linesize[2];
        cpy.WidthInBytes = width / 2;
        cpy.Height = height / 2;
        res = cuMemcpy2DAsync(&cpy, stream);
        if (res != CUDA_SUCCESS) {
            const char* errStr = nullptr;
            cuGetErrorString(res, &errStr);
            std::cerr << "CreateHardwareFrame: Failed to copy V plane: " << (errStr ? errStr : "unknown") << std::endl;
            return nullptr;
        }

        // Ensure all copies on the provided stream are complete before returning the frame to be encoded
        CUresult syncRes = cuStreamSynchronize(stream ? stream : m_cudaStream);
        if (syncRes != CUDA_SUCCESS) {
            const char* errStr = nullptr;
            cuGetErrorString(syncRes, &errStr);
            std::cerr << "CreateHardwareFrame: Stream sync failed: " << (errStr ? errStr : "unknown") << std::endl;
            return nullptr;
        }

        return m_hwFrame;
    }

    // RGB path: convert planar float RGB [0..1] to YUV420 in a temporary device buffer,
    // then copy planes into the hardware frame
    int width = m_width;
    int height = m_height;

    // Ensure internal YUV420 buffer is allocated (contiguous layout: Y, then U, then V)
    int yPitch = (m_internalYuv420YPitch > 0) ? m_internalYuv420YPitch : width;
    int uvPitch = (m_internalYuv420UVPitch > 0) ? m_internalYuv420UVPitch : (width / 2);
    size_t ySize = static_cast<size_t>(yPitch) * height;
    size_t uvSize = static_cast<size_t>(uvPitch) * (height / 2);
    size_t requiredSize = ySize + 2 * uvSize;

    if (m_internalYuv420BufferSize < requiredSize || m_internalYuv420Buffer == 0) {
        if (m_internalYuv420Buffer) {
            cuMemFree(m_internalYuv420Buffer);
            m_internalYuv420Buffer = 0;
            m_internalYuv420BufferSize = 0;
        }
        CUresult allocRes = cuMemAlloc(&m_internalYuv420Buffer, requiredSize);
        if (allocRes != CUDA_SUCCESS) {
            const char* errStr = nullptr;
            cuGetErrorString(allocRes, &errStr);
            std::cerr << "CreateHardwareFrame(RGB): Failed to allocate YUV420 buffer: " << (errStr ? errStr : "unknown") << std::endl;
            return nullptr;
        }
        m_internalYuv420BufferSize = requiredSize;
        m_internalYuv420YPitch = yPitch;
        m_internalYuv420UVPitch = uvPitch;
    }

    // Launch kernel to convert planar float RGB to YUV420 in the temporary buffer
    bool ok = LaunchPlanarFloatRgbToYuv420(
        static_cast<const float*>(d_data),
        reinterpret_cast<uint8_t*>(m_internalYuv420Buffer),
        width,
        height,
        m_internalYuv420YPitch,
        m_internalYuv420UVPitch,
        stream
    );
    if (!ok) {
        std::cerr << "CreateHardwareFrame: LaunchPlanarFloatRgbToYuv420 failed" << std::endl;
        return nullptr;
    }

    // Copy planes into the hardware frame
    CUDA_MEMCPY2D cpy{};
    // Y plane
    cpy.srcMemoryType = CU_MEMORYTYPE_DEVICE;
    cpy.dstMemoryType = CU_MEMORYTYPE_DEVICE;
    cpy.srcDevice = m_internalYuv420Buffer;
    cpy.srcPitch = m_internalYuv420YPitch;
    cpy.dstDevice = (CUdeviceptr)m_hwFrame->data[0];
    cpy.dstPitch = m_hwFrame->linesize[0];
    cpy.WidthInBytes = width;
    cpy.Height = height;
    {
        CUresult res = cuMemcpy2DAsync(&cpy, stream);
        if (res != CUDA_SUCCESS) {
            const char* errStr = nullptr;
            cuGetErrorString(res, &errStr);
            std::cerr << "CreateHardwareFrame(RGB): Failed to copy Y plane: " << (errStr ? errStr : "unknown") << std::endl;
            return nullptr;
        }
    }

    // U plane
    size_t uOffset = static_cast<size_t>(m_internalYuv420YPitch) * height;
    cpy.srcDevice = m_internalYuv420Buffer + uOffset;
    cpy.srcPitch = m_internalYuv420UVPitch;
    cpy.dstDevice = (CUdeviceptr)m_hwFrame->data[1];
    cpy.dstPitch = m_hwFrame->linesize[1];
    cpy.WidthInBytes = width / 2;
    cpy.Height = height / 2;
    {
        CUresult res = cuMemcpy2DAsync(&cpy, stream);
        if (res != CUDA_SUCCESS) {
            const char* errStr = nullptr;
            cuGetErrorString(res, &errStr);
            std::cerr << "CreateHardwareFrame(RGB): Failed to copy U plane: " << (errStr ? errStr : "unknown") << std::endl;
            return nullptr;
        }
    }

    // V plane
    size_t vOffset = uOffset + static_cast<size_t>(m_internalYuv420UVPitch) * (height / 2);
    cpy.srcDevice = m_internalYuv420Buffer + vOffset;
    cpy.srcPitch = m_internalYuv420UVPitch;
    cpy.dstDevice = (CUdeviceptr)m_hwFrame->data[2];
    cpy.dstPitch = m_hwFrame->linesize[2];
    cpy.WidthInBytes = width / 2;
    cpy.Height = height / 2;
    {
        CUresult res = cuMemcpy2DAsync(&cpy, stream);
        if (res != CUDA_SUCCESS) {
            const char* errStr = nullptr;
            cuGetErrorString(res, &errStr);
            std::cerr << "CreateHardwareFrame(RGB): Failed to copy V plane: " << (errStr ? errStr : "unknown") << std::endl;
            return nullptr;
        }
    }

    // Ensure all conversions and copies on the provided stream are complete before encoding
    {
        CUresult syncRes = cuStreamSynchronize(stream ? stream : m_cudaStream);
        if (syncRes != CUDA_SUCCESS) {
            const char* errStr = nullptr;
            cuGetErrorString(syncRes, &errStr);
            std::cerr << "CreateHardwareFrame(RGB): Stream sync failed: " << (errStr ? errStr : "unknown") << std::endl;
            return nullptr;
        }
    }

    return m_hwFrame;
}

// Internal encoding function
bool DirectVideoWriter::EncodeFrame(AVFrame* frame) {
    int ret = avcodec_send_frame(m_codecContext, frame);
    if (ret < 0) {
        char err_buf[AV_ERROR_MAX_STRING_SIZE];
        av_make_error_string(err_buf, AV_ERROR_MAX_STRING_SIZE, ret);
        std::cerr << "Error sending frame to encoder: " << err_buf << std::endl;
        return false;
    }

    while (ret >= 0) {
        ret = avcodec_receive_packet(m_codecContext, m_packet);
        if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF) break;
        if (ret < 0) return false;
        if (av_interleaved_write_frame(m_formatContext, m_packet) < 0) {
            throw std::runtime_error("Error writing packet to file.");
        }
        av_packet_unref(m_packet);
    }
    return true;
}

bool DirectVideoWriter::Finalize() {
    if (!m_isInitialized || m_isFinalized) return false;
    if (m_cudaStream) cuStreamSynchronize(m_cudaStream);
    int ret = avcodec_send_frame(m_codecContext, nullptr);
    if (ret < 0) return false;
    while (ret >= 0) {
        ret = avcodec_receive_packet(m_codecContext, m_packet);
        if (ret == AVERROR_EOF) break;
        if (ret < 0) return false;
        if (av_interleaved_write_frame(m_formatContext, m_packet) < 0) return false;
        av_packet_unref(m_packet);
    }
    if (av_write_trailer(m_formatContext) < 0) return false;
    m_isFinalized = true;
    return true;
}

void DirectVideoWriter::Cleanup() {
    if (m_isInitialized && !m_isFinalized) Finalize();
    m_isInitialized = false;

    if (m_codecContext) avcodec_free_context(&m_codecContext);
    if (m_hwFrame) av_frame_free(&m_hwFrame);
    if (m_packet) av_packet_free(&m_packet);

    if (m_hwFramesCtx) {
        av_buffer_unref(&m_hwFramesCtx);
        m_hwFramesCtx = nullptr;
    }
    
    if (m_hwDeviceCtx) {
        av_buffer_unref(&m_hwDeviceCtx);
        m_hwDeviceCtx = nullptr;
    }

    if (m_formatContext) {
        if (!(m_formatContext->oformat->flags & AVFMT_NOFILE) && m_formatContext->pb) {
            avio_closep(&m_formatContext->pb);
        }
        avformat_free_context(m_formatContext);
        m_formatContext = nullptr;
    }

    if (m_cudaStream) {
        cuStreamDestroy(m_cudaStream);
        m_cudaStream = 0;
    }
    
    // Free internal GPU buffers
    if (m_internalYuv420Buffer) {
        cuMemFree(m_internalYuv420Buffer);
        m_internalYuv420Buffer = 0;
        m_internalYuv420BufferSize = 0;
        m_internalYuv420YPitch = 0;
        m_internalYuv420UVPitch = 0;
    }
    
    m_cudaContext = nullptr;
    m_isFinalized = false;
}
