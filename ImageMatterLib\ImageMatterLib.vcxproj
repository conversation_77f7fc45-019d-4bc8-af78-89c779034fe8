<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{A1B2C3D4-E5F6-7890-1234-56789ABCDEF0}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>ImageMatterLib</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <PlatformToolset>v143</PlatformToolset>
    <TargetName>ImageMatterLib</TargetName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
    <Import Project="$(VCTargetsPath)\BuildCustomizations\CUDA 12.9.props" />
  </ImportGroup>
  <ImportGroup Label="Shared" />
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <IncludePath>include;include_internal;include_internal/FFmpegIO;$(IncludePath)</IncludePath>
    <LibraryPath>$(ProjectDir)..\x64\$(Configuration);$(LibraryPath)</LibraryPath>
    <TargetExt>.dll</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PreprocessorDefinitions>IMAGEMATTERLIB_EXPORTS;_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>include_internal\FFmpegIO;$(CUDA_PATH)\include;$(VccLibs)\ffmpeg\include;$(VccLibs)\onnxruntime\include;$(VccLibs)\TensorRT\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>$(CUDA_PATH)\lib\x64;$(VccLibs)\ffmpeg\bin;$(VccLibs)\onnxruntime\lib;$(VccLibs)\Tensorrt\lib</AdditionalLibraryDirectories>
      <AdditionalDependencies>nvinfer_10.lib;nvonnxparser_10.lib;nvinfer_plugin_10.lib;cudart_static.lib;cuda.lib;cublas.lib;curand.lib;cusparse.lib;cusolver.lib;cufft.lib;avcodec.lib;avformat.lib;avutil.lib;swresample.lib;avfilter.lib;swscale.lib;onnxruntime.lib;d3d11.lib;dxgi.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <IgnoreSpecificDefaultLibraries>LIBCMT</IgnoreSpecificDefaultLibraries>
      <AdditionalOptions>/verbose %(AdditionalOptions)</AdditionalOptions>
    </Link>
    <CudaCompile>
      <TargetMachinePlatform>64</TargetMachinePlatform>
      <AdditionalOptions>--expt-relaxed-constexpr %(AdditionalOptions)</AdditionalOptions>
      <GenerateRelocatableDeviceCode>false</GenerateRelocatableDeviceCode>
      <GPUDebugInfo>false</GPUDebugInfo>
      <CodeGeneration>compute_75,sm_75</CodeGeneration>
    </CudaCompile>
    <PostBuildEvent>
      <Command>call "$(ProjectDir)..\ImageMatterLib\src\Generate_ptx_updated.bat" "$(ProjectDir)..\ImageMatterLib" "$(ProjectDir)..\"</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <PreprocessorDefinitions>IMAGEMATTERLIB_EXPORTS;_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <AdditionalIncludeDirectories>include_internal\FFmpegIO;$(CUDA_PATH)\include;$(VccLibs)\ffmpeg\include;$(VccLibs)\onnxruntime\include;$(VccLibs)\TensorRT\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalLibraryDirectories>$(CUDA_PATH)\lib\x64;$(VccLibs)\ffmpeg\bin;$(VccLibs)\onnxruntime\lib;$(VccLibs)\Tensorrt\lib</AdditionalLibraryDirectories>
      <AdditionalDependencies>nvinfer_10.lib;nvonnxparser_10.lib;nvinfer_plugin_10.lib;cudart_static.lib;cuda.lib;cublas.lib;curand.lib;cusparse.lib;cusolver.lib;cufft.lib;avcodec.lib;avformat.lib;avutil.lib;swresample.lib;avfilter.lib;swscale.lib;onnxruntime.lib;d3d11.lib;dxgi.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <IgnoreSpecificDefaultLibraries>LIBCMT</IgnoreSpecificDefaultLibraries>
      <AdditionalOptions>
 %(AdditionalOptions)</AdditionalOptions>
    </Link>
    <CudaCompile>
      <TargetMachinePlatform>64</TargetMachinePlatform>
      <AdditionalOptions>--expt-relaxed-constexpr %(AdditionalOptions)</AdditionalOptions>
      <GenerateRelocatableDeviceCode>false</GenerateRelocatableDeviceCode>
      <GPUDebugInfo>false</GPUDebugInfo>
      <CodeGeneration>compute_75,sm_75</CodeGeneration>
    </CudaCompile>
    <PostBuildEvent>
      <Command>call "$(ProjectDir)..\ImageMatterLib\src\Generate_ptx_updated.bat" "$(ProjectDir)..\ImageMatterLib" "$(ProjectDir)..\"</Command>
    </PostBuildEvent>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="include_internal\FFmpegIO\CodecConfig.h" />
    <ClInclude Include="include_internal\FFmpegIO\DirectVideoReader.h" />
    <ClInclude Include="include_internal\FFmpegIO\MultiVideoReader.h" />
    <ClInclude Include="include_internal\FFmpegIO\D3D11VideoReader.h" />
    <ClInclude Include="include\D3D11VideoReader.h" />
    <ClInclude Include="include\ImageMatterLib.h" />
    <ClInclude Include="include_internal\BackgroundEstimationKernels.cuh" />
    <ClInclude Include="include_internal\DownscaleResizingKernels.cuh" />
    <ClInclude Include="include_internal\FFmpegIO\DirectVideoWriter.h" />
    <ClInclude Include="include_internal\FFmpegIO\FFmpegIOKernels.cuh" />
    <ClInclude Include="include_internal\framework.h" />
    <ClInclude Include="include_internal\HeadDetector.h" />
    <ClInclude Include="include_internal\HeadDetectorKernels.cuh" />
    <ClInclude Include="include_internal\Helpers.h" />
    <ClInclude Include="include_internal\Helpers_Heads.h" />
    <ClInclude Include="include_internal\Helpers_Kernels.cuh" />
    <ClInclude Include="include_internal\ImageMatting.h" />
    <ClInclude Include="include_internal\ImageMattingFactory.h" />
    <ClInclude Include="include_internal\ImageMattingOnnx.h" />
    <ClInclude Include="include_internal\ImageMattingTensorRt.h" />
    <ClInclude Include="include_internal\KernelManager.h" />
    <ClInclude Include="include_internal\lodepng.h" />
    <ClInclude Include="include_internal\main_Kernels.cuh" />
    <ClInclude Include="include_internal\Matting_Kernels.cuh" />
    <ClInclude Include="include_internal\ProcessBodyRegions.h" />
    <ClInclude Include="include_internal\RAII.h" />
    <ClInclude Include="include_internal\StringUtils.h" />
    <ClInclude Include="include_internal\UpscaleResizingKernels.cuh" />
    <ClInclude Include="include_internal\VideoBackgroundRemover.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\FFmpegIO\DirectVideoReader.cpp" />
    <ClCompile Include="src\FFmpegIO\DirectVideoWriter.cpp" />
    <ClCompile Include="src\FFmpegIO\MultiVideoReader.cpp" />
    <ClCompile Include="src\FFmpegIO\D3D11VideoReader.cpp" />
    <ClCompile Include="src\FFmpegIO\D3D11VideoReaderAPI.cpp" />
    <ClCompile Include="src\BackgroundRemover.cpp" />
    <ClCompile Include="src\dllmain.cpp" />
    <None Include="src\Generate_ptx.bat">
      <FileType>CppCode</FileType>
    </None>
    <ClCompile Include="src\ImageMattingFactory.cpp" />
    <ClCompile Include="src\ImageMatting.cpp" />
    <ClCompile Include="src\ImageMattingOnnx.cpp" />
    <ClCompile Include="src\ImageMattingTensorRt.cpp" />
    <ClCompile Include="src\HeadDetector.cpp" />
    <ClCompile Include="src\Helpers.cpp" />
    <ClCompile Include="src\Helpers_Heads.cpp" />
    <ClCompile Include="src\KernelManager.cpp" />
    <ClCompile Include="src\lodepng.cpp" />
    <ClCompile Include="src\ProcessBodyRegions.cpp" />
    <ClCompile Include="src\RAII.cpp" />
    <ClCompile Include="src\VideoBackgroundRemover.cpp" />
    <None Include="src\Generate_ptx_updated.bat" />
  </ItemGroup>
  <ItemGroup>
    <CudaCompile Include="src\BackgroundEstimationKernels.cu" />
    <CudaCompile Include="src\FFmpegIO\FFmpegIOKernels.cu" />
    <CudaCompile Include="src\HeadDetectorKernels.cu" />
    <CudaCompile Include="src\Helpers_Kernels.cu" />
    <CudaCompile Include="src\main_Kernels.cu" />
    <CudaCompile Include="src\Matting_Kernels.cu" />
    <CudaCompile Include="src\DownscaleResizingKernels.cu" />
    <CudaCompile Include="src\UpscaleResizingKernels.cu" />
  </ItemGroup>
  <ItemGroup>
    <Text Include="src\IMPORTANT - Principles.txt" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
    <Import Project="$(VCTargetsPath)\BuildCustomizations\CUDA 12.9.targets" />
  </ImportGroup>
</Project>