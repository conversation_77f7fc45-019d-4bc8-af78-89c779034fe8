@echo off
setlocal enabledelayedexpansion
echo Generating ImageMatterKernels.ptx...

set "PROJ=%~1"
set "OUT=%~2"
set "MASTER_FILE=%PROJ%\src\ImageMatterKernels_Master.cu"

cd /d "%PROJ%\src"

rem Set up Visual Studio environment
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvarsall.bat" (
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvarsall.bat" x64
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Auxiliary\Build\vcvarsall.bat" (
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Auxiliary\Build\vcvarsall.bat" x64
) else (
    echo Warning: Visual Studio environment not found, compilation might fail
)

rem Create master file that includes all .cu files
echo Creating master file...
echo // Auto-generated master file > "%MASTER_FILE%"

rem Include all .cu files from current directory (excluding master file)
for %%f in (*.cu) do (
    if not "%%f"=="ImageMatterKernels_Master.cu" (
        echo #include "%%f" >> "%MASTER_FILE%"
    )
)

rem Include all .cu files from subdirectories recursively
for /r . %%f in (*.cu) do (
    set "relativepath=%%f"
    set "relativepath=!relativepath:%CD%\=!"
    rem Skip the master file if found in subdirectories
    if not "!relativepath!"=="ImageMatterKernels_Master.cu" (
        rem Only include files that are not already in the current directory
        echo !relativepath! | findstr "\\" >nul
        if not errorlevel 1 (
            echo #include "!relativepath!" >> "%MASTER_FILE%"
        )
    )
)

echo Compiling master file...
nvcc -I"%PROJ%\include_internal" -I"%PROJ%\src" -I"%PROJ%\include_internal\FFmpegIO" -Wno-deprecated-gpu-targets -ptx "%MASTER_FILE%" -o "%OUT%ImageMatterKernels.ptx"

rem Clean up temporary file
del "%MASTER_FILE%" 2>nul

if exist "%OUT%ImageMatterKernels.ptx" (
    echo   - ImageMatterKernels.ptx generated successfully
    
    rem Copy PTX to executable directories
    if exist "%OUT%x64\Debug" (
        copy "%OUT%ImageMatterKernels.ptx" "%OUT%x64\Debug\ImageMatterKernels.ptx" >nul
        echo   - PTX copied to Debug directory
    )
    if exist "%OUT%x64\Release" (
        copy "%OUT%ImageMatterKernels.ptx" "%OUT%x64\Release\ImageMatterKernels.ptx" >nul
        echo   - PTX copied to Release directory
    )
    
) else (
    echo   - Error generating ImageMatterKernels.ptx
)

echo Done.
