// CUDA ProRes 4444 Encoding with AI Alpha Matting - One-Phase Processing
//
// This application processes video files to add AI-generated alpha channels
// and encodes them to ProRes 4444 format using GPU acceleration.
//

#include <iostream>
#include <string>
#include "ImageMatterLib.h"

// Progress callback for demonstration
bool __stdcall MyProgressCallback(int currentFrame, int totalFrames, void* userData) {
    if (currentFrame % 10 == 0) {
        if (totalFrames > 0) {
            std::cout << "Progress: " << currentFrame << "/" << totalFrames
                     << " (" << (currentFrame * 100 / totalFrames) << "%)" << std::endl;
        } else {
            std::cout << "Processed " << currentFrame << " frames" << std::endl;
        }
    }
    return true; // Continue processing
}

int main(int argc, char* argv[]) {

    std::cout << "=== CUDA ProRes 4444 Encoding with AI Alpha Matting (One-Phase) ===" << std::endl;
    std::cout << "Debug: argc = " << argc << std::endl;

    std::wstring inputPath;
    std::wstring outputPath;
    
    // Check command line arguments - use defaults if not provided
    if (argc == 3) {
        // Use command line arguments
        std::string inputStr(argv[1]);
        std::string outputStr(argv[2]);
        inputPath = std::wstring(inputStr.begin(), inputStr.end());
        outputPath = std::wstring(outputStr.begin(), outputStr.end());
        std::cout << "Using command line arguments:" << std::endl;
        std::cout << "  Input: " << inputStr << std::endl;
        std::cout << "  Output: " << outputStr << std::endl;
    } else if (argc == 1) {
        // Use default values for easier testing
        inputPath = L"Videos\\Input short.mp4";
        outputPath = L"Videos\\output_default.mp4";
    } else {
        std::cout << "Usage: " << argv[0] << " [<input_video> <output_video>]" << std::endl;
        std::cout << "If no arguments are provided, defaults will be used." << std::endl;
        return 1;
    }

        // Call the shared function with multi-pass processing
	int result = RemoveBackground(
		inputPath.c_str(),
		outputPath.c_str(),
		ENGINE_AUTO_EXPORT, // Use automatic engine selection
		MyProgressCallback,
		nullptr);
    if (result == 0) {
        std::cout << "Remove background processing completed successfully!" << std::endl;
        return 0;
    } else {
        std::cout << "Remove background processing failed!" << std::endl;
        return 1;
    }
}
