#pragma once

#include <string>
#include <vector>
#include <memory>
#include <set>
#include <cuda.h>
#include "HeadDetector.h"
#include "RAII.h"

// Forward declarations
class DirectVideoReader;
class DirectVideoWriter;
class IImageMatting;

// Progress callback type: returns true to continue, false to cancel
// currentFrame: 0-based index, totalFrames: total number of frames (if known, else 0)
typedef bool(__stdcall* ProgressCallback)(int currentFrame, int totalFrames, void* userData);

// Engine type enum
enum EngineType {
	NONE = -1, // Added for initialization safety
	ENGINE_ONNX = 0,
	ENGINE_TENSORRT = 1,
	ENGINE_AUTO = 2
};

// Structure to hold head information
struct HeadInfo {
	Box box = { 0.0f, 0.0f, 0.0f, 0.0f, 0.0f }; // Initialize box with zeros
	std::pair<int, int> modelSize = { 0, 0 };	// Initialize model size
	int headFileIndex = -1;						// Initialize to invalid index
	int frameIndexHeadFile = -1;				// Initialize to invalid index
};

// VideoBackgroundRemover class for multi-pass processing
class VideoBackgroundRemover {
public:
	VideoBackgroundRemover();
	~VideoBackgroundRemover();

	// Main processing function
	int Process(
		const std::wstring& inputPath,
		const std::wstring& outputPath,
		EngineType engine,
		ProgressCallback progressCb,
		void* userData);


private:
	// Processing passes
	int InitializeProcessing(const std::wstring& inputPath);
	int ProcessPass1_InitialAlpha();
	int ProcessPass2_HeadDetection();
	int ProcessPass3_RefineHeadRegions();
	int ProcessPass4_RefineBodyRegions();
	int ProcessPass5_ReplaceHeadAlphaWithRefined();
	int ProcessPass6_EstimateBackground();
	int RenameFinalFilesAndCleanup();
	void Cleanup();

	// Helper methods
	bool UpdateProgress(int currentFrame, int totalSteps);
	bool SafeResetReader();

	// Shared member variables
	std::wstring m_inputPath;
	std::wstring m_outputPath;
	EngineType m_engine;
	ProgressCallback m_progressCb;
	void* m_userData;

	// CUDA resources
	CUcontext m_context;
	CUstream m_processingStream;

	// Video properties
	std::shared_ptr<DirectVideoReader> m_reader;
	int m_videoWidth;
	int m_videoHeight;
	int m_totalFrames;

	// Processing data
	std::vector<std::vector<HeadInfo>> m_headInfos;
	std::set<std::pair<int, int>> m_modelSizesRequired;

	// Progress tracking
	int m_totalProgressSteps;
};