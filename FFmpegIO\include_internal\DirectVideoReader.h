// DirectVideoReader.h
#pragma once

#include <string>
#include <memory>
#include <cuda.h>
#include <vector>

#include "../../ImageMatterLib/include_internal/Helpers.h"

// Include AVRational definition
extern "C" {
#include <libavutil/rational.h>
}

// Forward declarations of FFmpeg structures
struct AVFormatContext;
struct AVCodecContext;
struct AVFrame;
struct AVPacket;
struct SwsContext;

class DirectVideoReader {
public:
    // Initialize and open a video file, using an existing CUDA context
    static std::shared_ptr<DirectVideoReader> Create(
        const std::string& filePath,
        CUcontext cudaContext);

    // Destructor - will handle cleanup. Called automatically by std::shared_ptr.
    ~DirectVideoReader();

    // Seek to a specific time position (in seconds)
    bool Seek(double timeInSeconds);

    // Read the next frame into CUDA memory, returns the frame timestamp
    double ReadFrame(float* cudaBuffer, size_t bufferSize);

    // Get video properties
    int GetWidth() const { return m_width; }
    int GetHeight() const { return m_height; }
    double GetDuration() const { return m_duration; }
    AVRational GetFrameRate() const { return m_frameRate; }
    double GetFrameRateDouble() const { return m_frameRate.num / static_cast<double>(m_frameRate.den); } // For backward compatibility
    Helpers::TextureFormat GetTextureFormat() const { return m_textureFormat; }
    AVRational GetTimeBase() const { return m_timeBase; }
    double GetTimeBaseDouble() const { return m_timeBase.num / static_cast<double>(m_timeBase.den); } // For backward compatibility

    // Get underlying video stream format (can be called before reading frames)
    Helpers::TextureFormat GetVideoStreamFormat() const;

    // Close the video and release resources
    void Close();

private:
    // Private constructor - use Open() to create instances
    DirectVideoReader();

    // Initialize FFmpeg and CUDA resources
    bool Initialize(const std::string& filePath, CUcontext cudaContext);

    // Decode a single packet
    bool DecodePacket();

    // Transfer frame from GPU to CUDA memory
    bool TransferFrameToCuda(void* cudaBuffer, size_t bufferSize, size_t bufferPitch);

    // Convert frame timestamp to seconds
    double ConvertTimestampToSeconds(int64_t timestamp) const;

private:
    // FFmpeg state
    AVFormatContext* m_formatContext;
    AVCodecContext* m_codecContext;
    AVFrame* m_hwFrame;
    AVPacket* m_packet;
    int m_videoStreamIndex;
    bool m_isHardwareAccelerated;

    // CUDA state
    CUcontext m_cudaContext;
    CUstream m_cudaStream;

    // Internal NV12 buffer for RGB conversion
    void* m_internalNv12Buffer;
    size_t m_internalNv12BufferSize;
    size_t m_internalNv12Pitch;

    // Video properties
    int m_width;
    int m_height;
    double m_duration;
    AVRational m_frameRate;
    AVRational m_timeBase;
    Helpers::TextureFormat m_textureFormat;
    Helpers::TextureFormat m_sourceFormat;

    // Current frame timestamp
    int64_t m_currentFrameTimestamp;

    // State tracking
    bool m_isInitialized;
    bool m_isEof;

};
