﻿#include "DownscaleResizingKernels.cuh"
#include "KernelManager.h"
#include <cuda.h>
#include <device_launch_parameters.h>
#define NOMINMAX // Disable the min/max macros from windows.h which interfere with std::min/max
#include <Windows.h>
#include <iostream>
#include <exception>
#include <math_constants.h>

// Define CUDA Driver API types if not already defined
#ifndef CUDA_DRIVER_API_TYPES
#define CUDA_DRIVER_API_TYPES
typedef CUstream_st* CUstream;
typedef unsigned long long CUdeviceptr;
#endif

#ifdef __CUDACC__


// CUDA kernel for Lanczos-3 resizing that preserves fine details like hair
__device__ inline float lanczos3(float x) {
    if (x == 0.0f) return 1.0f;
    if (fabsf(x) >= 3.0f) return 0.0f;

    float pi_x = 3.14159265359f * x;
    float pi_x_div3 = pi_x / 3.0f;
    return 3.0f * sinf(pi_x) * sinf(pi_x_div3) / (pi_x * pi_x);
}

extern "C" __global__ void LanczosResizeDownKernel(float* output, int outputWidth, int outputHeight,
    const float* input, int inputWidth, int inputHeight,
    int channels) {
    const int x = blockIdx.x * blockDim.x + threadIdx.x;
    const int y = blockIdx.y * blockDim.y + threadIdx.y;

    if (x < outputWidth && y < outputHeight) {
        // Calculate source coordinates
        float scaleX = static_cast<float>(inputWidth) / outputWidth;
        float scaleY = static_cast<float>(inputHeight) / outputHeight;
        float srcX = (static_cast<float>(x) + 0.5f) * scaleX - 0.5f;
        float srcY = (static_cast<float>(y) + 0.5f) * scaleY - 0.5f;

        // Lanczos-3 support is 3 pixels in each direction
        int x_start = max(0, static_cast<int>(floorf(srcX)) - 2);
        int x_end = min(inputWidth - 1, static_cast<int>(ceilf(srcX)) + 2);
        int y_start = max(0, static_cast<int>(floorf(srcY)) - 2);
        int y_end = min(inputHeight - 1, static_cast<int>(ceilf(srcY)) + 2);

        for (int c = 0; c < channels; c++) {
            float sum = 0.0f;
            float weightSum = 0.0f;

            int planeSize = inputWidth * inputHeight;
            int planeOffset = c * planeSize;

            // Apply Lanczos-3 filter
            for (int iy = y_start; iy <= y_end; iy++) {
                for (int ix = x_start; ix <= x_end; ix++) {
                    float dx = srcX - static_cast<float>(ix);
                    float dy = srcY - static_cast<float>(iy);

                    float weightX = lanczos3(dx);
                    float weightY = lanczos3(dy);
                    float weight = weightX * weightY;

                    if (weight != 0.0f) {
                        float pixel = input[planeOffset + (iy * inputWidth + ix)];
                        sum += pixel * weight;
                        weightSum += weight;
                    }
                }
            }

            // Normalize and store result
            float value = (weightSum > 0.0f) ? sum / weightSum : 0.0f;

            int outPlaneSize = outputWidth * outputHeight;
            int outPlaneOffset = c * outPlaneSize;
            output[outPlaneOffset + (y * outputWidth + x)] = value;
        }
    }
}

bool LaunchLanczosResizeDownKernel(float* output, int outputWidth, int outputHeight,
    float* input, int inputWidth, int inputHeight, int channels, CUstream stream) {
    unsigned int blockSizeX = 16;
    unsigned int blockSizeY = 16;
    unsigned int gridSizeX = (outputWidth + blockSizeX - 1) / blockSizeX;
    unsigned int gridSizeY = (outputHeight + blockSizeY - 1) / blockSizeY;

    try {
        CUfunction kernel = KernelHelpers::GetKernelFunction("LanczosResizeDownKernel");
        void* kernelArgs[] = { &output, &outputWidth, &outputHeight, &input, &inputWidth, &inputHeight, &channels };
        CUresult result = KernelHelpers::LaunchKernel(kernel,
            gridSizeX, gridSizeY, 1, // Grid dimensions
            blockSizeX, blockSizeY, 1, // Block dimensions
            0, stream, kernelArgs);
            
        if (result != CUDA_SUCCESS) {
            const char* errorStr;
            cuGetErrorString(result, &errorStr);
            std::cerr << "LaunchLanczosResizeDownKernel: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
        }
        return result == CUDA_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "LaunchLanczosResizeDownKernel: Exception: " << e.what() << std::endl;
        return false;
    }
}

// Helper function to resize an image using Lanczos interpolation
CUresult LanczosResizeKernelLauncher(float* output, int outputWidth, int outputHeight,
    const float* input, int inputWidth, int inputHeight,
	int channels, CUstream stream) {
    // Validate input parameters
    if (!output || !input || outputWidth <= 0 || outputHeight <= 0 || 
        inputWidth <= 0 || inputHeight <= 0 || channels <= 0) {
        std::cerr << "LanczosResizeKernelLauncher: Invalid parameters" << std::endl;
        return CUDA_ERROR_INVALID_VALUE;
    }

    unsigned int blockSizeX = 16;
    unsigned int blockSizeY = 16;
    unsigned int gridSizeX = (outputWidth + blockSizeX - 1) / blockSizeX;
    unsigned int gridSizeY = (outputHeight + blockSizeY - 1) / blockSizeY;

    // Validate grid sizes
    if (gridSizeX == 0 || gridSizeY == 0) {
        std::cerr << "LanczosResizeKernelLauncher: Invalid grid configuration" << std::endl;
        return CUDA_ERROR_INVALID_VALUE;
    }

    try {
        CUfunction kernel = KernelHelpers::GetKernelFunction("LanczosResizeDownKernel");
        // Cast const away for kernel parameter - CUDA kernels don't preserve const in driver API
        float* inputPtr = const_cast<float*>(input);
        void* kernelArgs[] = { &output, &outputWidth, &outputHeight, &inputPtr, &inputWidth, &inputHeight, &channels };
        CUresult result = KernelHelpers::LaunchKernel(kernel,
            gridSizeX, gridSizeY, 1, // Grid dimensions
            blockSizeX, blockSizeY, 1, // Block dimensions
            0, stream, kernelArgs);
            
        if (result != CUDA_SUCCESS) {
            const char* errorStr;
            cuGetErrorString(result, &errorStr);
            std::cerr << "LanczosResizeKernelLauncher: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
        }
        return result;
    }
    catch (const std::exception& e) {
        std::cerr << "LanczosResizeKernelLauncher: Exception: " << e.what() << std::endl;
        return CUDA_ERROR_LAUNCH_FAILED;
    }
}

// Alternative: Mitchell-Netravali filter for even better detail preservation
__device__ inline float mitchell(float x) {
    x = fabsf(x);
    if (x >= 2.0f) return 0.0f;

    const float B = 1.0f / 3.0f;
    const float C = 1.0f / 3.0f;

    if (x < 1.0f) {
        return ((12.0f - 9.0f * B - 6.0f * C) * x * x * x +
            (-18.0f + 12.0f * B + 6.0f * C) * x * x +
            (6.0f - 2.0f * B)) / 6.0f;
    }
    else {
        return ((-B - 6.0f * C) * x * x * x +
            (6.0f * B + 30.0f * C) * x * x +
            (-12.0f * B - 48.0f * C) * x +
            (8.0f * B + 24.0f * C)) / 6.0f;
    }
}

extern "C" __global__ void MitchellResizeDownKernel(float* output, int outputWidth, int outputHeight,
    const float* input, int inputWidth, int inputHeight,
    int channels) {
    const int x = blockIdx.x * blockDim.x + threadIdx.x;
    const int y = blockIdx.y * blockDim.y + threadIdx.y;

    if (x < outputWidth && y < outputHeight) {
        float scaleX = static_cast<float>(inputWidth) / outputWidth;
        float scaleY = static_cast<float>(inputHeight) / outputHeight;
        float srcX = (static_cast<float>(x) + 0.5f) * scaleX - 0.5f;
        float srcY = (static_cast<float>(y) + 0.5f) * scaleY - 0.5f;

        // Mitchell filter has support of 2 pixels in each direction
        int x_start = max(0, static_cast<int>(floorf(srcX)) - 1);
        int x_end = min(inputWidth - 1, static_cast<int>(ceilf(srcX)) + 1);
        int y_start = max(0, static_cast<int>(floorf(srcY)) - 1);
        int y_end = min(inputHeight - 1, static_cast<int>(ceilf(srcY)) + 1);

        for (int c = 0; c < channels; c++) {
            float sum = 0.0f;
            float weightSum = 0.0f;

            int planeSize = inputWidth * inputHeight;
            int planeOffset = c * planeSize;

            for (int iy = y_start; iy <= y_end; iy++) {
                for (int ix = x_start; ix <= x_end; ix++) {
                    float dx = srcX - static_cast<float>(ix);
                    float dy = srcY - static_cast<float>(iy);

                    float weightX = mitchell(dx);
                    float weightY = mitchell(dy);
                    float weight = weightX * weightY;

                    if (weight != 0.0f) {
                        float pixel = input[planeOffset + (iy * inputWidth + ix)];
                        sum += pixel * weight;
                        weightSum += weight;
                    }
                }
            }

            float value = (weightSum > 0.0f) ? sum / weightSum : 0.0f;

            int outPlaneSize = outputWidth * outputHeight;
            int outPlaneOffset = c * outPlaneSize;
            output[outPlaneOffset + (y * outputWidth + x)] = value;
        }
    }
}

bool LaunchMitchellResizeDownKernel(float* output, int outputWidth, int outputHeight,
    float* input, int inputWidth, int inputHeight, int channels, CUstream stream) {
    unsigned int blockSizeX = 16;
    unsigned int blockSizeY = 16;
    unsigned int gridSizeX = (outputWidth + blockSizeX - 1) / blockSizeX;
    unsigned int gridSizeY = (outputHeight + blockSizeY - 1) / blockSizeY;

    try {
        CUfunction kernel = KernelHelpers::GetKernelFunction("MitchellResizeDownKernel");
        void* kernelArgs[] = { &output, &outputWidth, &outputHeight, &input, &inputWidth, &inputHeight, &channels };
        CUresult result = KernelHelpers::LaunchKernel(kernel,
            gridSizeX, gridSizeY, 1, // Grid dimensions
            blockSizeX, blockSizeY, 1, // Block dimensions
            0, stream, kernelArgs);
            
        if (result != CUDA_SUCCESS) {
            const char* errorStr;
            cuGetErrorString(result, &errorStr);
            std::cerr << "LaunchMitchellResizeDownKernel: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
        }
        return result == CUDA_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "LaunchMitchellResizeDownKernel: Exception: " << e.what() << std::endl;
        return false;
    }
}

// Helper function to resize an image using Mitchell interpolation
bool MitchellResizeKernelLauncher(float* output, int outputWidth, int outputHeight,
    const float* input, int inputWidth, int inputHeight,
    int channels, CUstream stream) {
    unsigned int blockSizeX = 16;
    unsigned int blockSizeY = 16;
    unsigned int gridSizeX = (outputWidth + blockSizeX - 1) / blockSizeX;
    unsigned int gridSizeY = (outputHeight + blockSizeY - 1) / blockSizeY;

    try {
        CUfunction kernel = KernelHelpers::GetKernelFunction("MitchellResizeDownKernel");
        void* kernelArgs[] = { &output, &outputWidth, &outputHeight, &input, &inputWidth, &inputHeight, &channels };
        CUresult result = KernelHelpers::LaunchKernel(kernel,
            gridSizeX, gridSizeY, 1, // Grid dimensions
            blockSizeX, blockSizeY, 1, // Block dimensions
            0, stream, kernelArgs);
            
        if (result != CUDA_SUCCESS) {
            const char* errorStr;
            cuGetErrorString(result, &errorStr);
            std::cerr << "MitchellResizeKernelLauncher: Kernel launch failed: " << (errorStr ? errorStr : "unknown") << std::endl;
        }
        return result == CUDA_SUCCESS;
    }
    catch (const std::exception& e) {
        std::cerr << "MitchellResizeKernelLauncher: Exception: " << e.what() << std::endl;
        return false;
    }
}
#endif
