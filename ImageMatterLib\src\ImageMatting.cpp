#define NOMINMAX // Disable the min/max macros from windows.h which interfere with std::min/max

#include "ImageMatting.h"
#include "ImageMattingFactory.h"  // For ResizeMethod enum
#include "Matting_Kernels.cuh"
#include "DownscaleResizingKernels.cuh"
#include "UpscaleResizingKernels.cuh"
#include "HeadDetectorKernels.cuh"  // For LanczosResizeAndPadKernel
#include <iostream>
#include <cuda.h>

// Common preprocessing implementation (non-static method)
CUresult ImageMatting::PreprocessInputBufferCommon(
    const CUdeviceptr inputBuffer,
    CUdeviceptr preprocessedBuffer,
    CUdeviceptr outputBuffer,
    int imageWidth,
    int imageHeight,
    int modelWidth,
    int modelHeight,
    bool isRgba,
    const NormalizationParams& normParams,
    size_t preprocessedBufferSize,
    ResizeMethod resizeMethod,
    CUstream stream) {

    if (!inputBuffer || !outputBuffer) {
        return CUDA_ERROR_INVALID_VALUE;
    }

    bool needsResize = (imageWidth != modelWidth || imageHeight != modelHeight);
    CUresult cudaStatus = CUDA_SUCCESS;

    // Step 1: Preprocess the input buffer (normalization)
    if (needsResize) {
        // When resizing is needed, preprocess into intermediate buffer first
        if (!preprocessedBuffer) {
            return CUDA_ERROR_INVALID_VALUE;
        }
        cudaStatus = LaunchPreprocessBufferKernel((float*)preprocessedBuffer, (const float*)inputBuffer,
                                                imageWidth, imageHeight, isRgba,
                                                normParams, stream);
    } else {
        // When no resizing is needed, preprocess directly into output buffer
        cudaStatus = LaunchPreprocessBufferKernel((float*)outputBuffer, (const float*)inputBuffer,
                                                imageWidth, imageHeight, isRgba,
                                                normParams, stream);
    }
    if (cudaStatus != CUDA_SUCCESS) {
        const char* errorStr;
        cuGetErrorString(cudaStatus, &errorStr);
        std::cerr << "Failed to preprocess buffer: " << errorStr << std::endl;
        return cudaStatus;
    }

    // Step 2: Resize if needed
    if (needsResize) {
        if (resizeMethod == ResizeMethod::EXTEND_SHRINK_LANCZOS) {
            // Use direct Lanczos resize (extend/shrink as needed)
            cudaStatus = LanczosResizeKernelLauncher((float*)outputBuffer, modelWidth, modelHeight,
                (const float*)preprocessedBuffer, imageWidth, imageHeight,
                isRgba ? 4 : 3, stream);
        }
        else if (resizeMethod == ResizeMethod::STRETCH_ASPECT_RATIO_PAD) {
            // Use Lanczos resize with aspect ratio preservation and padding
            // Note: The HeadDetectorKernels version only supports RGB (3 channels)
            if (isRgba) {
                std::cerr << "STRETCH_ASPECT_RATIO_PAD resize method does not support RGBA input yet" << std::endl;
                return CUDA_ERROR_NOT_SUPPORTED;
            }
            cudaStatus = LaunchLanczosResizeAndPadKernel((float*)outputBuffer, modelWidth, modelHeight,
                (float*)preprocessedBuffer, imageWidth, imageHeight);
        }
        else {
            std::cerr << "Unknown resize method" << std::endl;
            return CUDA_ERROR_INVALID_VALUE;
        }

        if (cudaStatus != CUDA_SUCCESS) {
            const char* errorStr;
            cuGetErrorString(cudaStatus, &errorStr);
            std::cerr << "Failed to resize input: " << errorStr << std::endl;
            return cudaStatus;
        }
    }
    else {
        // If no resize needed, preprocessing was already done directly into output buffer
        // No additional copy needed
    }

    return CUDA_SUCCESS;
}

// Common postprocessing implementation (non-static method)
CUresult ImageMatting::PostprocessOutputBufferCommon(
    const CUdeviceptr modelOutput,
    CUdeviceptr outputBuffer,
    int modelWidth,
    int modelHeight,
    int imageWidth,
    int imageHeight,
    ResizeMethod resizeMethod,
    CUstream stream) {

    // If dimensions are the same, just copy the data
    if (modelWidth == imageWidth && modelHeight == imageHeight) {
        return cuMemcpyDtoDAsync(outputBuffer, modelOutput,
            imageWidth * imageHeight * sizeof(float),
            stream);
    }

    if (resizeMethod == ResizeMethod::EXTEND_SHRINK_LANCZOS) {
        // Use direct Lanczos upscaling for better quality
        LanczosUpscaleKernelLauncher((float*)outputBuffer, imageWidth, imageHeight,
                                    (const float*)modelOutput, modelWidth, modelHeight, 1, stream);
    }
    else if (resizeMethod == ResizeMethod::STRETCH_ASPECT_RATIO_PAD) {
        // For aspect ratio preserved input, we need to "uncrop" the padded output back to original size
        // During preprocessing, the image was resized with aspect ratio preservation and padded
        // Now we need to extract the content region from the padded model output and resize back
        float scale = std::min(static_cast<float>(modelWidth) / imageWidth,
                              static_cast<float>(modelHeight) / imageHeight);
        int scaledWidth = static_cast<int>(imageWidth * scale);
        int scaledHeight = static_cast<int>(imageHeight * scale);
        int padLeft = (modelWidth - scaledWidth) / 2;
        int padTop = (modelHeight - scaledHeight) / 2;

        // Step 1: Extract the content region from the padded model output
        // The model output contains padding, we need to extract the center region that contains actual content
        // This region is [padLeft, padTop, scaledWidth, scaledHeight] within the model output

        // For now, allocate a temporary buffer for the cropped region
        CUdeviceptr croppedBuffer = 0;
        size_t croppedBufferSize = scaledWidth * scaledHeight * sizeof(float);
        CUresult allocResult = cuMemAlloc(&croppedBuffer, croppedBufferSize);
        if (allocResult != CUDA_SUCCESS) {
            const char* errorStr;
            cuGetErrorString(allocResult, &errorStr);
            std::cerr << "Failed to allocate temporary buffer for cropping: " << errorStr << std::endl;
            return allocResult;
        }

        // Step 2: Extract the cropped region using cuMemcpy2DAsync
        // Copy the relevant region from modelOutput to croppedBuffer
        CUDA_MEMCPY2D copyDesc;
        memset(&copyDesc, 0, sizeof(copyDesc));
        copyDesc.srcMemoryType = CU_MEMORYTYPE_DEVICE;
        copyDesc.srcDevice = modelOutput + padTop * modelWidth * sizeof(float) + padLeft * sizeof(float);
        copyDesc.srcPitch = modelWidth * sizeof(float);
        copyDesc.dstMemoryType = CU_MEMORYTYPE_DEVICE;
        copyDesc.dstDevice = croppedBuffer;
        copyDesc.dstPitch = scaledWidth * sizeof(float);
        copyDesc.WidthInBytes = scaledWidth * sizeof(float);
        copyDesc.Height = scaledHeight;

        CUresult copyResult = cuMemcpy2DAsync(&copyDesc, stream);
        if (copyResult != CUDA_SUCCESS) {
            const char* errorStr;
            cuGetErrorString(copyResult, &errorStr);
            std::cerr << "Failed to copy cropped region: " << errorStr << std::endl;
            cuMemFree(croppedBuffer);
            return copyResult;
        }

        // Step 3: Resize the cropped region to original image size
        LanczosUpscaleKernelLauncher((float*)outputBuffer, imageWidth, imageHeight,
                                    (const float*)croppedBuffer, scaledWidth, scaledHeight, 1, stream);

        // Clean up temporary buffer
        cuMemFree(croppedBuffer);
    }
    else {
        std::cerr << "Unknown resize method in postprocessing" << std::endl;
        return CUDA_ERROR_INVALID_VALUE;
    }

    return CUDA_SUCCESS;
}
